/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.service.impl;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.PenSource;
import com.app.cargill.ddw.calculation.Calculation;
import com.app.cargill.ddw.constants.HerdConstant;
import com.app.cargill.ddw.document.TempDDWDocument;
import com.app.cargill.ddw.enums.Source;
import com.app.cargill.ddw.model.*;
import com.app.cargill.ddw.service.IHerdService;
import com.app.cargill.document.*;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.*;
import com.app.cargill.repository.*;
import com.app.cargill.service.IPensService;
import com.app.cargill.service.ISiteService;
import com.app.cargill.utils.MathUtils;
import com.azure.cosmos.implementation.Strings;
import java.time.Instant;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service("herdServiceImpl")
@RequiredArgsConstructor
public class HerdServiceImpl implements IHerdService {

  private final AccountsRepository accountsRepository;

  private final SitesRepository sitesRepository;

  private final SiteMappingsRepository siteMappingsRepository;

  private final DietRepository dietRepository;

  private final TempDDWRepository tempDDWRepository;

  private final PensRepository pensRepository;

  private final ISiteService siteService;

  private final IPensService pensService;

  // Initialize Constants
  public static final String HERD_LACTATING_COWS = "HerdLactatingCows";

  public static final String HERD = "Herd";
  public static final String MINUS_VALUE = "-99999";
  public static final String NO_GROUP = "NoGroup";

  @Override
  public List<String> updateHerds(List<HerdData> herds) {

    List<String> messages = new ArrayList<>();
    log.debug("[HerdServiceImpl][updateHerds] Entering...");

    herds.stream()
        .filter(
            herdFilter ->
                herdFilter != null
                    && (herdFilter.getInventory() != null || herdFilter.getPeriod() != null))
        .forEach(
            herd -> {
              String ddwHerdId =
                  (herd.getInventory() != null)
                      ? herd.getInventory().getHerdProfileId()
                      : herd.getPeriod().getHerdProfileId();

              stageHerdData(herd);
              log.debug("[HerdServiceImpl][updateHerds]  DDW Herd ID " + ddwHerdId);

              messages.add("Staging DDW Herd Profile " + ddwHerdId);
            });

    /* Update Data for
     *  1. Site
     *  2. Account
     *  3. Pens & Diets
     * */
    updateSiteAndAccountData(herds, messages);

    return messages;
  }

  private void updateSiteAndAccountData(List<HerdData> herds, List<String> messages) {
    log.debug("[HerdServiceImpl][updateSiteAndAccountData]...Entering..");

    for (HerdData herd : herds) {
      if (isValidHerdData(herd)) {

        log.debug(
            "[HerdServiceImpl][updateSiteAndAccountData] ... "
                + " Check Herd Inventory OR Profile exists!");

        processHerdData(herd, messages);
      }
    }
  }

  private boolean isValidHerdData(HerdData herd) {
    log.debug("[HerdServiceImpl][isValidHerdData]...Entering..");

    return herd != null && (herd.getInventory() != null || herd.getPeriod() != null);
  }

  public void processHerdData(HerdData herd, List<String> messages) {

    log.debug("[HerdServiceImpl][processHerdData]...Entering..");
    String ddwHerdId = getHerdProfileId(herd);
    SiteMappings siteMapping = getSiteMappingByDDWHerdId(ddwHerdId);

    if (siteMapping == null) {

      log.warn("[HerdServiceImpl][processHerdData] --> NO_DDW_HERD_MAPPING_FOUND {}", ddwHerdId);

      messages.add("NO_DDW_HERD_MAPPING_FOUND " + ddwHerdId);
      return;
    }

    Accounts account = getAccountBySiteMapping(siteMapping);
    if (account == null) {

      log.warn(
          "[HerdServiceImpl][processHerdData] -->"
              + "error: no account with id "
              + siteMapping.getSiteMappingDocument().getLabyrinthAccountId()
              + " found.");

      messages.add(
          "warn: no account with id "
              + siteMapping.getSiteMappingDocument().getLabyrinthAccountId()
              + " found.");
      return;
    }

    boolean updated = updateAccountWithHerdData(account, siteMapping, herd);
    String accountId = Objects.requireNonNull(account).getAccountDocument().getId().toString();

    String updateMessage = updated ? "was applied to" : "was not applied to";
    messages.add("herd profile " + ddwHerdId + " " + updateMessage + " account " + accountId + ".");

    log.debug(
        "[HerdServiceImpl][processHerdData] -->"
            + "herd profile "
            + ddwHerdId
            + " "
            + updateMessage
            + " account "
            + accountId
            + ".");
  }

  private String getHerdProfileId(HerdData herd) {
    if (herd != null && (herd.getInventory() != null || herd.getPeriod() != null)) {

      return (herd.getInventory() != null)
          ? herd.getInventory().getHerdProfileId()
          : herd.getPeriod().getHerdProfileId();
    }
    return null;
  }

  private SiteMappings getSiteMappingByDDWHerdId(String ddwHerdId) {
    return siteMappingsRepository.findByDDWHerdIdAndDeleted(ddwHerdId);
  }

  private Accounts getAccountBySiteMapping(SiteMappings siteMapping) {
    String accountId = siteMapping.getSiteMappingDocument().getLabyrinthAccountId().toString();
    return accountsRepository.findByAccountId(accountId);
  }

  private void stageHerdData(HerdData herdData) {
    String ddwHerdId;

    if (herdData.getInventory() != null) ddwHerdId = herdData.getInventory().getHerdProfileId();
    else ddwHerdId = herdData.getPeriod().getHerdProfileId();

    List<TempDDW> existingTempDDWRecord = tempDDWRepository.getByHerdProfileId(ddwHerdId);

    TempDDWDocument newTempDDWDocument = new TempDDWDocument();
    newTempDDWDocument.setHerd(herdData);

    if (existingTempDDWRecord != null && !existingTempDDWRecord.isEmpty()) {
      this.updateExistingTempDDWRecord(existingTempDDWRecord.get(0), newTempDDWDocument, herdData);
    } else {
      TempDDW tempDDW = new TempDDW();
      newTempDDWDocument.setId(UUID.randomUUID());
      tempDDW.setTempDdwDocument(newTempDDWDocument);
      tempDDW.setLocalId(UUID.randomUUID().toString());
      tempDDWRepository.save(tempDDW);
    }
  }

  public void updateExistingTempDDWRecord(
      TempDDW existingTempDDW, TempDDWDocument newTempDDWDocument, HerdData herdData) {

    newTempDDWDocument.setId(existingTempDDW.getTempDdwDocument().getId());

    if (herdData.getCullings() == null) {

      newTempDDWDocument
          .getHerd()
          .setCullings(existingTempDDW.getTempDdwDocument().getHerd().getCullings());
    }

    if (herdData.getHealth() == null) {

      newTempDDWDocument
          .getHerd()
          .setHealth(existingTempDDW.getTempDdwDocument().getHerd().getHealth());
    }

    if (herdData.getInventory() == null) {
      newTempDDWDocument
          .getHerd()
          .setInventory(existingTempDDW.getTempDdwDocument().getHerd().getInventory());
    }
    if (herdData.getMilkProductions() == null) {
      newTempDDWDocument
          .getHerd()
          .setMilkProductions(existingTempDDW.getTempDdwDocument().getHerd().getMilkProductions());
    }

    if (herdData.getMilkRecordings() == null) {
      newTempDDWDocument
          .getHerd()
          .setMilkRecordings(existingTempDDW.getTempDdwDocument().getHerd().getMilkRecordings());
    }
    if (herdData.getNutritions() == null) {
      newTempDDWDocument
          .getHerd()
          .setNutritions(existingTempDDW.getTempDdwDocument().getHerd().getNutritions());
    }
    if (herdData.getPenDatas() == null) {
      newTempDDWDocument
          .getHerd()
          .setPenDatas(existingTempDDW.getTempDdwDocument().getHerd().getPenDatas());
    }
    if (herdData.getPeriod() == null) {
      newTempDDWDocument
          .getHerd()
          .setPeriod(existingTempDDW.getTempDdwDocument().getHerd().getPeriod());
    }
    if (herdData.getReproduction() == null) {
      newTempDDWDocument
          .getHerd()
          .setReproduction(existingTempDDW.getTempDdwDocument().getHerd().getReproduction());
    }

    if (herdData.getUdderHealths() == null) {
      newTempDDWDocument
          .getHerd()
          .setUdderHealths(existingTempDDW.getTempDdwDocument().getHerd().getUdderHealths());
    }

    existingTempDDW.setUpdatedDate(new Date());
    existingTempDDW.setTempDdwDocument(newTempDDWDocument);
    tempDDWRepository.save(existingTempDDW);
  }

  public UUID getDietId(HerdCulling herdPenData, List<DietDocument> diets) {

    String groupCategory = herdPenData.getGroupCategory().toUpperCase();
    String targetDietName;

    switch (groupCategory) {
      case HerdConstant.HEIFER -> targetDietName = HerdConstant.HEIFERS_DIET;
      case HerdConstant.DRY_COWS -> targetDietName = HerdConstant.DRY_DIET;
      case HerdConstant.LACTATING_COWS, HerdConstant.UNKNOWN -> targetDietName =
          HerdConstant.LACTATING_DIET;
      default -> {
        return null;
      }
    }

    return diets.stream()
        .filter(
            diet ->
                diet.getSource() != null
                    && diet.getSource().getDietSource().equals(Source.SYSTEM_GENERATED.getValue())
                    && diet.getName() != null
                    && diet.getName().equalsIgnoreCase(targetDietName))
        .map(DietDocument::getId)
        .findFirst()
        .orElse(null);
  }

  public UUID getDietId(MilkRecording herdPenData, List<DietDocument> diets) {
    String groupCategory = herdPenData.getGroupCategory().toUpperCase();
    List<DietDocument> filteredDiets =
        diets.stream()
            .filter(
                a ->
                    a.getSource() != null
                        && a.getSource().getDietSource().equals(Source.SYSTEM_GENERATED.getValue()))
            .toList();

    String targetDietName;
    switch (groupCategory) {
      case HerdConstant.HEIFER -> targetDietName = HerdConstant.HEIFERS_DIET;
      case HerdConstant.DRY_COWS -> targetDietName = HerdConstant.DRY_DIET;
      case HerdConstant.LACTATING_COWS, HerdConstant.UNKNOWN -> targetDietName =
          HerdConstant.LACTATING_DIET;
      default -> {
        return null;
      }
    }

    return filteredDiets.stream()
        .filter(a -> a.getName() != null && a.getName().equalsIgnoreCase(targetDietName))
        .findFirst()
        .map(DietDocument::getId)
        .orElse(null);
  }

  public UUID getDietId(HerdMilkProduction herdPenData, List<DietDocument> diets) {

    String groupCategory = herdPenData.getGroupCategory().toUpperCase();
    String targetDietName;

    switch (groupCategory) {
      case HerdConstant.HEIFER -> targetDietName = HerdConstant.HEIFERS_DIET;
      case HerdConstant.DRY_COWS -> targetDietName = HerdConstant.DRY_DIET;
      case HerdConstant.LACTATING_COWS, HerdConstant.UNKNOWN -> targetDietName =
          HerdConstant.LACTATING_DIET;
      default -> {
        return null;
      }
    }

    return diets.stream()
        .filter(
            diet ->
                diet.getSource() != null
                    && diet.getSource().getDietSource().equals(Source.SYSTEM_GENERATED.getValue())
                    && diet.getName() != null
                    && diet.getName().equalsIgnoreCase(targetDietName))
        .map(DietDocument::getId)
        .findFirst()
        .orElse(null);
  }

  private void updateDiets(List<Diets> diets) {
    if (diets != null) {
      addDietIfNotExists(diets, "Lactating/Milking", "Lactating", "Milking");
      addDietIfNotExists(diets, "Dry/FarOff", "Dry", "FarOff");
      addDietIfNotExists(diets, "Heifers/Heifers", "Heifers", "Heifers");
    }
  }

  private void addDietIfNotExists(
      List<Diets> diets, String name, String animalClass, String subClass) {

    log.debug("[HerdServiceImpl][addDietIfNotExists] ... Entering Diet " + name);

    boolean exists =
        diets.stream()
            .anyMatch(
                diet ->
                    diet.getDietDocument()
                            .getSource()
                            .getDietSource()
                            .equals(Source.SYSTEM_GENERATED.getValue())
                        && diet.getDietDocument().getName().equalsIgnoreCase(name));

    if (!exists) {
      UUID dietId = UUID.randomUUID();
      UUID animalTypeId = UUID.randomUUID();

      AnimalClass animalType = new AnimalClass(animalTypeId, animalClass, subClass);
      DietDocument newDiet =
          new DietDocument(dietId, name, DietSource.SYSTEM_GENERATED, animalType);
      diets.add(addDiet(newDiet));
    }
  }

  private Diets addDiet(DietDocument dietDocument) {
    Diets diet = new Diets();
    diet.setLocalId(UUID.randomUUID().toString());
    diet.setDietDocument(dietDocument);
    return diet;
  }

  private PenDocument createPenFromHerdData(
      Sites site,
      List<Diets> diets,
      HerdCulling herdPenData,
      HerdNutrition nutrition,
      HerdData herd,
      HerdMilkProduction milkProduction,
      MilkRecording milkRecording) {
    PenDocument penDocument = new PenDocument();
    penDocument.setId(herdPenData.getPenId() != null ? herdPenData.getPenId() : UUID.randomUUID());

    // Set site, barn, and customer account properties

    SiteDocument siteDocument = site.getSiteDocument();
    if (siteDocument != null && !siteDocument.getBarns().isEmpty()) {
      penDocument.setSiteId(siteDocument.getId());
      penDocument.setBarnId(siteDocument.getBarns().get(0).getId());
      penDocument.setCustomerAccountId(siteDocument.getAccountId());
    }

    // Map diets to DietDocuments and set the dietId

    List<DietDocument> dietDocuments = diets.stream().map(Diets::getDietDocument).toList();
    penDocument.setDietId(getDietId(herdPenData, dietDocuments));

    // Set basic properties

    penDocument.setSource(PenSource.DDW);
    penDocument.setDietSource(DietSource.SYSTEM_GENERATED);
    penDocument.setGroupId(herdPenData.getGroupId());
    penDocument.setName(herdPenData.getGroupName());
    penDocument.setDDWLastUpdatedDate(Instant.now());

    // Set milk production or milk recording properties

    if (milkProduction != null) {
      setMilkProductionProperties(penDocument, milkProduction);
    } else if (milkRecording != null) {
      setMilkRecordingProperties(penDocument, milkRecording);
    }

    // Set nutrition properties

    if (nutrition != null) {
      setNutritionProperties(penDocument, nutrition, herdPenData, herd);
    }

    return penDocument;
  }

  private PenDocument createPenFromHerdData(
      Sites site,
      List<Diets> diets,
      HerdNutrition nutrition,
      HerdData herd,
      HerdMilkProduction milkProduction,
      MilkRecording milkRecording) {
    PenDocument penDocument = new PenDocument();
    penDocument.setId(
        milkProduction.getPenId() != null ? milkProduction.getPenId() : UUID.randomUUID());

    // Set site, barn, and customer account properties

    SiteDocument siteDocument = site.getSiteDocument();
    if (siteDocument != null && !siteDocument.getBarns().isEmpty()) {
      penDocument.setSiteId(siteDocument.getId());
      penDocument.setBarnId(siteDocument.getBarns().get(0).getId());
      penDocument.setCustomerAccountId(siteDocument.getAccountId());
    }

    // Map diets to DietDocuments and set the dietId

    List<DietDocument> dietDocuments = diets.stream().map(Diets::getDietDocument).toList();

    // Set basic properties

    penDocument.setSource(PenSource.DDW);
    penDocument.setDietSource(DietSource.SYSTEM_GENERATED);
    penDocument.setDDWLastUpdatedDate(Instant.now());

    if (milkRecording != null) {
      penDocument.setDietId(getDietId(milkRecording, dietDocuments));
      penDocument.setGroupId(milkRecording.getGroupId());
      penDocument.setName(milkRecording.getGroupName());
    } else if (milkProduction != null) {
      penDocument.setDietId(getDietId(milkProduction, dietDocuments));
      penDocument.setGroupId(milkProduction.getGroupId());
      penDocument.setName(milkProduction.getGroupName());
    }

    // Set milk production or milk recording properties
    if (milkProduction != null) {
      setMilkProductionProperties(penDocument, milkProduction);
    } else if (milkRecording != null) {
      setMilkRecordingProperties(penDocument, milkRecording);
    }

    // Set nutrition properties

    if (nutrition != null) {
      setNutritionAndFedIntakeProperties(penDocument, nutrition, herd, milkProduction);
    }

    return penDocument;
  }

  private void setMilkProductionProperties(
      PenDocument penDocument, HerdMilkProduction milkProduction) {
    log.debug("[HerdServiceImpl][setMilkProductionProperties]...Entering..");

    Integer animals = MathUtils.customRound(milkProduction.getNrCowsOverall());
    log.debug("[HerdServiceImpl][setMilkProductionProperties]...Set Pen's Animals : " + animals);
    penDocument.setAnimals(animals);

    Integer daysInMilk = MathUtils.customRound(milkProduction.getAverageDIMOverall());

    log.debug(
        "[HerdServiceImpl][setMilkProductionProperties]...Set Pen's DaysInMilk: " + daysInMilk);
    penDocument.setDaysInMilk(daysInMilk);

    Double milk = NumberUtils.toDouble(milkProduction.getAverageYieldOverall());
    log.debug("[HerdServiceImpl][setMilkProductionProperties]...Set Pen's Milk: " + milk);
    penDocument.setMilk(milk);
  }

  private void setMilkRecordingProperties(PenDocument penDocument, MilkRecording milkRecording) {
    log.debug("[HerdServiceImpl][setMilkRecordingProperties]...Entering..");

    Integer animals = MathUtils.customRound(milkRecording.getNumberCowsInMilk());
    log.debug("[HerdServiceImpl][setMilkRecordingProperties]...Set Pen's Animals : " + animals);
    penDocument.setAnimals(animals);

    Integer daysInMilk = MathUtils.customRound(milkRecording.getAverageDIM());
    log.debug(
        "[HerdServiceImpl][setMilkRecordingProperties]...Set Pen's daysInMilk : " + daysInMilk);
    penDocument.setDaysInMilk(daysInMilk);

    Double milk = NumberUtils.toDouble(milkRecording.getAverageDailyYieldKg());
    log.debug("[HerdServiceImpl][setMilkRecordingProperties]...Set Pen's Milk : " + milk);
    penDocument.setMilk(milk);
  }

  private void setNutritionProperties(
      PenDocument penDocument, HerdNutrition nutrition, HerdCulling herdPenData, HerdData herd) {
    log.debug("[HerdServiceImpl][setNutritionProperties]...Entering..");

    double dryMatterIntake = Calculation.dryMatterIntakeForPen(nutrition);
    double rationCostCalculation = Calculation.rationCostCalculationForPen(nutrition);
    double asFedIntake = Calculation.asFedIntake(nutrition, herdPenData, herd);

    if (dryMatterIntake != 0) {
      log.debug(
          "[HerdServiceImpl][setNutritionProperties]... Set Pen's DryMatterIntake.."
              + dryMatterIntake);
      penDocument.setDryMatterIntake(dryMatterIntake);
    }

    if (rationCostCalculation != 0) {
      log.debug(
          "[HerdServiceImpl][setNutritionProperties]... Set Pen's RationCostPerAnimal.."
              + rationCostCalculation);
      penDocument.setRationCostPerAnimal(rationCostCalculation);
    }

    if (asFedIntake != 0) {
      log.debug(
          "[HerdServiceImpl][setNutritionProperties]... Set Pen's AsFedIntake.." + asFedIntake);
      penDocument.setAsFedIntake(asFedIntake);
    }
  }

  private void setNutritionAndFedIntakeProperties(
      PenDocument penDocument,
      HerdNutrition nutrition,
      HerdData herd,
      HerdMilkProduction milkProduction) {
    log.debug("[HerdServiceImpl][setNutritionAndFedIntakeProperties]...Entering..");

    double dryMatterIntake = Calculation.dryMatterIntakeForPen(nutrition);
    double rationCostCalculation = Calculation.rationCostCalculationForPen(nutrition);
    double asFedIntake = Calculation.asFedIntake(nutrition, milkProduction, herd);

    if (dryMatterIntake != 0) {
      log.debug(
          "[HerdServiceImpl][setNutritionAndFedIntakeProperties]... Set Pen's DryMatterIntake.."
              + dryMatterIntake);
      penDocument.setDryMatterIntake(dryMatterIntake);
    }

    if (rationCostCalculation != 0) {
      log.debug(
          "[HerdServiceImpl][setNutritionAndFedIntakeProperties]... Set Pen's RationCostPerAnimal.."
              + rationCostCalculation);
      penDocument.setRationCostPerAnimal(rationCostCalculation);
    }

    if (asFedIntake != 0) {
      log.debug(
          "[HerdServiceImpl][setNutritionAndFedIntakeProperties]... Set Pen's AsFedIntake.."
              + asFedIntake);
      penDocument.setAsFedIntake(asFedIntake);
    }
  }

  public void addOrUpdatePens(Sites site, HerdData herd, List<Diets> diets) {
    log.debug("[HerdServiceImpl][addOrUpdatePens]...Entering..");

    List<Pens> existingPens =
        pensRepository.findBySiteId(site.getSiteDocument().getId().toString());

    if (site.getSiteDocument().getBarns() == null || site.getSiteDocument().getBarns().isEmpty()) {
      saveNewSiteBarns(site);
    }

    setSiteNetEnergyLactationMilk(herd, existingPens, site);

    addOrUpdatePenDocuments(existingPens, site, herd, diets);
  }

  private void addOrUpdatePenDocuments(
      List<Pens> existingPens, Sites site, HerdData herd, List<Diets> diets) {

    log.debug("[HerdServiceImpl][addOrUpdatePenDocuments]...Entering..");
    List<Pens> pens = new ArrayList<>();

    if (herd.getMilkProductions() != null && !herd.getMilkProductions().isEmpty()) {
      List<Pens> herdMilkProductions = extractMilkProductions(existingPens, site, herd, diets);
      pens.addAll(herdMilkProductions);
    }

    if (herd.getCullings() != null && !herd.getCullings().isEmpty()) {

      pens.addAll(extractCullingPens(existingPens, site, herd, diets));
    }

    if (herd.getMilkRecordings() != null && !herd.getMilkRecordings().isEmpty()) {

      List<Pens> milkRecordingPens = extractMilkRecordings(existingPens, site, herd, diets);
      pens.addAll(milkRecordingPens);
    }

    // Create New Pen Documents of Site
    createNewPenDocuments(pens);
  }

  private void saveNewSiteBarns(Sites site) {

    List<Barn> newBarns = new ArrayList<>();
    Barn barn = new Barn();
    barn.setId(UUID.randomUUID());
    barn.setBarnName("");
    newBarns.add(barn);
    site.getSiteDocument().setBarns(newBarns);
    // Update Barn in Site Repository
    sitesRepository.save(site);
  }

  private void createNewPenDocuments(List<Pens> pens) {
    List<Pens> groupedPens = getUniquePens(pens);
    for (Pens pen : groupedPens) {
      updateOrSavePen(pen);
    }
  }

  private List<Pens> extractCullingPens(
      List<Pens> existingPens, Sites site, HerdData herd, List<Diets> diets) {
    log.debug("[HerdServiceImpl][extractCullingPens]...Entering..");

    return herd.getCullings().stream()
        .filter(this::isValidCulling)
        .map(penData -> createPenDocumentsForCulling(existingPens, site, diets, herd, penData))
        .toList();
  }

  private List<Pens> extractMilkProductions(
      List<Pens> existingPens, Sites site, HerdData herd, List<Diets> diets) {

    log.debug("[HerdServiceImpl][extractMilkProductions]...Entering..");

    return herd.getMilkProductions().stream()
        .filter(this::isValidMilkProduction)
        .map(
            penData ->
                createPenDocumentsForMilkProduction(existingPens, site, diets, herd, penData))
        .toList();
  }

  private List<Pens> extractMilkRecordings(
      List<Pens> existingPens, Sites site, HerdData herd, List<Diets> diets) {
    log.debug("[HerdServiceImpl][extractMilkRecordings]...Entering..");

    return herd.getMilkRecordings().stream()
        .filter(this::isValidPenData)
        .map(
            penData ->
                createPenDocumentsForMilkRecordings(existingPens, site, diets, herd, penData))
        .toList();
  }

  private boolean isValidCulling(HerdCulling penData) {
    log.debug("[HerdServiceImpl][isValidCulling]...Entering..");

    return !Strings.isNullOrEmpty(penData.getGroupName())
        && !Strings.isNullOrEmpty(penData.getGroupId())
        && (!penData.getGroupName().contains(HERD)
            && !penData.getGroupName().equalsIgnoreCase(NO_GROUP)
            && !penData.getGroupId().equalsIgnoreCase(MINUS_VALUE));
  }

  private boolean isValidMilkProduction(HerdMilkProduction milkProduction) {
    log.debug("[HerdServiceImpl][isValidMilkProduction]...Entering..");

    return !Strings.isNullOrEmpty(milkProduction.getGroupName())
        && !Strings.isNullOrEmpty(milkProduction.getGroupId())
        && (!milkProduction.getGroupName().contains(HERD)
            && !milkProduction.getGroupName().equalsIgnoreCase(NO_GROUP)
            && !milkProduction.getGroupId().equalsIgnoreCase(MINUS_VALUE));
  }

  private boolean isValidPenData(MilkRecording recording) {
    log.debug("[HerdServiceImpl][isValidPenData]...Entering..");

    return !Strings.isNullOrEmpty(recording.getGroupName())
        && !Strings.isNullOrEmpty(recording.getGroupId())
        && (!recording.getGroupName().contains(HERD)
            && !recording.getGroupName().equalsIgnoreCase(NO_GROUP)
            && !recording.getGroupId().equalsIgnoreCase(MINUS_VALUE));
  }

  private Pens createPenDocumentsForCulling(
      List<Pens> existingPens, Sites site, List<Diets> diets, HerdData herd, HerdCulling penData) {

    log.debug("[HerdServiceImpl][createPenDocumentsForCulling]...Entering..");

    UUID animalClassId = determineAnimalClassId(penData);
    HerdNutrition nutrition = getMatchingNutritionList(herd.getNutritions(), penData);

    MilkRecording recording = getMatchingRecording(herd.getMilkRecordings(), penData);
    HerdMilkProduction production = getMatchingMilkProduction(herd.getMilkProductions(), penData);

    // Update Case of Pen Document
    if (existingPens != null && !existingPens.isEmpty()) {

      Pens existingBarnPens =
          existingPens.stream()
              .filter(
                  pen ->
                      (pen.getPenDocument().getGroupId() != null
                          && pen.getPenDocument().getGroupId().equals(penData.getGroupId())
                          && pen.getPenDocument()
                              .getSource()
                              .getValue()
                              .equals(Source.DDW.getValue())))
              .findFirst()
              .orElse(null);

      List<DietDocument> dietsList = diets.stream().map(Diets::getDietDocument).toList();

      if (existingBarnPens != null) {
        log.debug(
            "[HerdServiceImpl][createPenDocumentsForCulling]...Updating Pen : "
                + existingBarnPens.getPenDocument().getName());

        PenDocument document =
            updatePenFromHerdData(
                existingBarnPens.getPenDocument(), site, dietsList, penData, production, recording);

        if (animalClassId != null) {
          log.debug(
              "[HerdServiceImpl][createPenDocumentsForCulling]...Updating Pen's animalClassId : "
                  + animalClassId);
          document.setAnimalClassId(animalClassId);
        }

        if (nutrition != null) {
          setNutritionProperties(nutrition, existingBarnPens.getPenDocument(), production, herd);
        }

        existingBarnPens.setPenDocument(document);
        return existingBarnPens;
      }
    }
    PenDocument newPenDocument =
        createPenFromHerdData(site, diets, penData, nutrition, herd, production, recording);
    log.debug(
        "[HerdServiceImpl][createPenDocumentsForCulling]...Creating Pen : "
            + newPenDocument.getName());

    Pens newPen = new Pens();
    newPen.setLocalId(UUID.randomUUID().toString());

    if (animalClassId != null) {
      log.debug(
          "[HerdServiceImpl][createPenDocumentsForCulling]...Add Pen's animalClassId : "
              + animalClassId);
      newPenDocument.setAnimalClassId(animalClassId);
    }
    newPen.setPenDocument(newPenDocument);
    return newPen;
  }

  private Pens createPenDocumentsForMilkProduction(
      List<Pens> existingPens,
      Sites site,
      List<Diets> diets,
      HerdData herd,
      HerdMilkProduction herdMilkProduction) {

    log.debug("[HerdServiceImpl][createPenDocumentsForMilkProduction]...Entering..");

    HerdNutrition nutritionList =
        getMatchingNutritionList(herd.getNutritions(), herdMilkProduction);

    MilkRecording recording =
        getMatchingRecordingAndMilkProduction(herd.getMilkRecordings(), herdMilkProduction);
    HerdMilkProduction production =
        getMatchingMilkProduction(herd.getMilkProductions(), herdMilkProduction);

    if (existingPens != null && !existingPens.isEmpty()) {

      Pens existingBarnPens =
          existingPens.stream()
              .filter(
                  pen ->
                      (pen.getPenDocument().getGroupId() != null
                          && pen.getPenDocument()
                              .getGroupId()
                              .equals(herdMilkProduction.getGroupId())
                          && pen.getPenDocument()
                              .getSource()
                              .getValue()
                              .equals(Source.DDW.getValue())))
              .findFirst()
              .orElse(null);

      List<DietDocument> dietsList = diets.stream().map(Diets::getDietDocument).toList();

      if (existingBarnPens != null) {
        PenDocument document =
            updatePenFromHerdData(
                existingBarnPens.getPenDocument(),
                site,
                dietsList,
                nutritionList,
                herd,
                production);

        document.setAnimalClassId(existingBarnPens.getPenDocument().getAnimalClassId());
        existingBarnPens.setPenDocument(document);
        return existingBarnPens;
      }
    }

    PenDocument newPenDocument =
        createPenFromHerdData(site, diets, nutritionList, herd, production, recording);
    Pens newPen = new Pens();
    newPen.setLocalId(UUID.randomUUID().toString());
    newPen.setPenDocument(newPenDocument);
    return newPen;
  }

  private Pens createPenDocumentsForMilkRecordings(
      List<Pens> existingPens,
      Sites site,
      List<Diets> diets,
      HerdData herd,
      MilkRecording recording) {

    log.debug("[HerdServiceImpl][createPenDocumentsForMilkRecordings]...Entering..");
    HerdNutrition nutritionList = getMatchingNutritionRec(herd.getNutritions(), recording);
    MilkRecording milkRecording =
        getMatchingMilkRecordingsAndRec(herd.getMilkRecordings(), recording);
    HerdMilkProduction production =
        getMatchingMilkProductionAndRecording(herd.getMilkProductions(), recording);

    if (existingPens != null && !existingPens.isEmpty() && milkRecording != null) {

      Pens existingBarnPens =
          existingPens.stream()
              .filter(
                  pen ->
                      (pen.getPenDocument().getGroupId() != null
                          && pen.getPenDocument().getGroupId().equals(milkRecording.getGroupId())
                          && pen.getPenDocument()
                              .getSource()
                              .getValue()
                              .equals(Source.DDW.getValue())))
              .findFirst()
              .orElse(null);

      List<DietDocument> dietsList = diets.stream().map(Diets::getDietDocument).toList();

      if (existingBarnPens != null) {
        PenDocument document =
            updatePenFromHerdDataMilkRecording(
                (Objects.requireNonNull(existingBarnPens).getPenDocument()),
                site,
                dietsList,
                nutritionList,
                herd,
                production,
                recording);
        document.setAnimalClassId(existingBarnPens.getPenDocument().getAnimalClassId());
        existingBarnPens.setPenDocument(document);
        return existingBarnPens;
      }
    }

    PenDocument newPenDocument =
        createPenFromHerdData(site, diets, nutritionList, herd, production, recording);
    Pens newPen = new Pens();
    newPen.setLocalId(UUID.randomUUID().toString());
    newPen.setPenDocument(newPenDocument);
    return newPen;
  }

  public HerdNutrition getMatchingNutritionList(
      List<HerdNutrition> nutritionList, HerdCulling penData) {

    log.debug("[HerdServiceImpl][getMatchingNutritionList]...Entering..");

    if (nutritionList != null) {
      return nutritionList.stream()
          .filter(
              nutrition ->
                  nutrition.getPenName() != null
                      && (nutrition.getPenName().equalsIgnoreCase(penData.getGroupId())
                          || nutrition.getPenName().equalsIgnoreCase(penData.getGroupName())))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  private HerdNutrition getMatchingNutritionList(
      List<HerdNutrition> nutritionList, HerdMilkProduction milkProduction) {
    log.debug("[HerdServiceImpl][getMatchingNutritionList]...Entering..");

    if (nutritionList != null) {
      return nutritionList.stream()
          .filter(
              nutrition ->
                  nutrition != null
                      && ((nutrition.getPenName() != null
                              && nutrition
                                  .getPenName()
                                  .equalsIgnoreCase(milkProduction.getGroupId()))
                          || (nutrition.getPenName() != null
                              && nutrition
                                  .getPenName()
                                  .equalsIgnoreCase(milkProduction.getGroupName()))))
          .findFirst()
          .orElse(null);
    }

    return null;
  }

  private HerdNutrition getMatchingNutritionRec(
      List<HerdNutrition> nutritionList, MilkRecording milkRecording) {
    log.debug("[HerdServiceImpl][getMatchingNutritionRec]...Entering..");

    if (nutritionList != null) {
      return nutritionList.stream()
          .filter(
              nutrition ->
                  nutrition != null
                      && ((nutrition.getPenName() != null
                              && nutrition
                                  .getPenName()
                                  .equalsIgnoreCase(milkRecording.getGroupId()))
                          || (nutrition.getPenName() != null
                              && nutrition
                                  .getPenName()
                                  .equalsIgnoreCase(milkRecording.getGroupName()))))
          .findFirst()
          .orElse(null);
    }

    return null;
  }

  private MilkRecording getMatchingMilkRecordingsAndRec(
      List<MilkRecording> recordings, MilkRecording milkRecording) {

    log.debug("[HerdServiceImpl][getMatchingMilkRecordingsAndRec]...Entering..");

    if (recordings != null) {
      return recordings.stream()
          .filter(
              recording ->
                  recording != null
                      && ((recording.getGroupId() != null
                              && recording
                                  .getGroupId()
                                  .equalsIgnoreCase(milkRecording.getGroupId()))
                          || (recording.getGroupName() != null
                              && recording
                                  .getGroupName()
                                  .equalsIgnoreCase(milkRecording.getGroupName()))))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  private MilkRecording getMatchingRecording(List<MilkRecording> recordings, HerdCulling penData) {

    log.debug("[HerdServiceImpl][getMatchingRecording]...Entering..");

    if (recordings != null) {
      return recordings.stream()
          .filter(
              recording ->
                  recording != null
                      && ((recording.getGroupId() != null
                              && recording.getGroupId().equalsIgnoreCase(penData.getGroupId()))
                          || (recording.getGroupName() != null
                              && recording
                                  .getGroupName()
                                  .equalsIgnoreCase(penData.getGroupName()))))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  private MilkRecording getMatchingRecordingAndMilkProduction(
      List<MilkRecording> recordings, HerdMilkProduction milkProduction) {

    log.debug("[HerdServiceImpl][getMatchingRecordingAndMilkProduction]...Entering..");

    if (recordings != null) {
      return recordings.stream()
          .filter(
              recording ->
                  recording != null
                      && ((recording.getGroupId() != null
                              && recording
                                  .getGroupId()
                                  .equalsIgnoreCase(milkProduction.getGroupId()))
                          || (recording.getGroupName() != null
                              && recording
                                  .getGroupName()
                                  .equalsIgnoreCase(milkProduction.getGroupName()))))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  private HerdMilkProduction getMatchingMilkProduction(
      List<HerdMilkProduction> milkProductions, HerdCulling penData) {

    log.debug("[HerdServiceImpl][getMatchingMilkProduction]...Entering..");

    if (milkProductions != null) {
      return milkProductions.stream()
          .filter(
              recording ->
                  recording != null
                      && ((recording.getGroupId() != null
                              && recording.getGroupId().equalsIgnoreCase(penData.getGroupId()))
                          || (recording.getGroupName() != null
                              && recording
                                  .getGroupName()
                                  .equalsIgnoreCase(penData.getGroupName()))))
          .findFirst()
          .orElse(null);
    }

    return null;
  }

  private HerdMilkProduction getMatchingMilkProduction(
      List<HerdMilkProduction> milkProductions, HerdMilkProduction production) {

    log.debug("[HerdServiceImpl][getMatchingMilkProduction]...Entering..");

    if (milkProductions != null) {
      return milkProductions.stream()
          .filter(
              recording ->
                  recording != null
                      && ((recording.getGroupId() != null
                              && recording.getGroupId().equalsIgnoreCase(production.getGroupId()))
                          || (recording.getGroupName() != null
                              && recording
                                  .getGroupName()
                                  .equalsIgnoreCase(production.getGroupName()))))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  private HerdMilkProduction getMatchingMilkProductionAndRecording(
      List<HerdMilkProduction> milkProductions, MilkRecording recording) {

    log.debug("[HerdServiceImpl][getMatchingMilkProductionAndRecording]...Entering..");

    if (milkProductions != null) {
      return milkProductions.stream()
          .filter(
              rec ->
                  rec != null
                      && ((rec.getGroupId() != null
                              && rec.getGroupId().equalsIgnoreCase(recording.getGroupId()))
                          || (rec.getGroupName() != null
                              && rec.getGroupName().equalsIgnoreCase(recording.getGroupName()))))
          .findFirst()
          .orElse(null);
    }

    return null;
  }

  public PenDocument updatePenFromHerdData(
      PenDocument existingPen,
      Sites site,
      List<DietDocument> diets,
      HerdCulling herdPenData,
      HerdMilkProduction milkProduction,
      MilkRecording milkRecording) {

    log.debug("[HerdServiceImpl][updatePenFromHerdData]...Entering..");

    existingPen.setSiteId(site.getSiteDocument().getId());
    existingPen.setDietId(getDietId(herdPenData, diets));
    existingPen.setBarnId(site.getSiteDocument().getBarns().get(0).getId());
    existingPen.setCustomerAccountId(site.getSiteDocument().getAccountId());
    existingPen.setSource(PenSource.DDW);
    existingPen.setName(herdPenData.getGroupName());
    existingPen.setDietSource(DietSource.SYSTEM_GENERATED);
    existingPen.setIsDeleted(false);
    existingPen.setDDWLastUpdatedDate(Instant.now());
    if (milkProduction != null) {
      setMilkProductionPropertiesPercent(milkProduction, existingPen);
    } else if (milkRecording != null) {
      setMilkRecordingPropertiesPercent(milkRecording, existingPen);
    }

    return existingPen;
  }

  private void setMilkProductionPropertiesPercent(
      HerdMilkProduction milkProduction, PenDocument existingPen) {
    log.debug("[HerdServiceImpl][setMilkProductionPropertiesPercent]...Entering..");

    if (!Strings.isNullOrEmpty(milkProduction.getNrCowsOverall()))
      existingPen.setAnimals(MathUtils.customRound(milkProduction.getNrCowsOverall()));
    if (!Strings.isNullOrEmpty(milkProduction.getAverageDIMOverall()))
      existingPen.setDaysInMilk(MathUtils.customRound(milkProduction.getAverageDIMOverall()));
    if (!Strings.isNullOrEmpty(milkProduction.getAverageYieldOverall()))
      existingPen.setMilk(NumberUtils.toDouble(milkProduction.getAverageYieldOverall()));
  }

  private void setMilkRecordingPropertiesPercent(
      MilkRecording milkRecording, PenDocument existingPen) {
    log.debug("[HerdServiceImpl][setMilkRecordingPropertiesPercent]...Entering..");

    if (milkRecording != null && !Strings.isNullOrEmpty(milkRecording.getNumberCowsInMilk()))
      existingPen.setAnimals(MathUtils.customRound(milkRecording.getNumberCowsInMilk()));
    if (milkRecording != null && !Strings.isNullOrEmpty(milkRecording.getAverageDIM()))
      existingPen.setDaysInMilk(MathUtils.customRound(milkRecording.getAverageDIM()));
    if (milkRecording != null && !Strings.isNullOrEmpty(milkRecording.getAverageDailyYieldKg()))
      existingPen.setMilk(NumberUtils.toDouble(milkRecording.getAverageDailyYieldKg()));
  }

  private void setNutritionProperties(
      HerdNutrition nutrition,
      PenDocument existingPen,
      HerdMilkProduction milkProduction,
      HerdData herd) {
    log.debug("[HerdServiceImpl][setNutritionProperties]...Entering..");

    if (nutrition != null) {
      if (Calculation.dryMatterIntakeForPen(nutrition) != 0)
        existingPen.setDryMatterIntake(Calculation.dryMatterIntakeForPen(nutrition));
      if (Calculation.rationCostCalculationForPen(nutrition) != 0)
        existingPen.setRationCostPerAnimal(Calculation.rationCostCalculationForPen(nutrition));
      if (Calculation.asFedIntake(nutrition, milkProduction, herd) != 0)
        existingPen.setAsFedIntake(Calculation.asFedIntake(nutrition, milkProduction, herd));
    }
  }

  public PenDocument updatePenFromHerdData(
      PenDocument existingPen,
      Sites site,
      List<DietDocument> diets,
      HerdNutrition nutrition,
      HerdData herd,
      HerdMilkProduction milkProduction) {

    log.debug("[HerdServiceImpl][updatePenFromHerdData]...Entering..");
    existingPen.setSiteId(site.getSiteDocument().getId());
    existingPen.setDietId(getDietId(milkProduction, diets));
    existingPen.setBarnId(
        site.getSiteDocument().getBarns() != null && !site.getSiteDocument().getBarns().isEmpty()
            ? site.getSiteDocument().getBarns().get(0).getId()
            : null);
    existingPen.setCustomerAccountId(site.getSiteDocument().getAccountId());
    existingPen.setSource(PenSource.DDW);
    existingPen.setDietSource(DietSource.SYSTEM_GENERATED);

    existingPen.setName(milkProduction.getGroupName());
    existingPen.setIsDeleted(false);

    log.debug(
        "[HerdServiceImpl][updatePenFromHerdData]...Updating Pen data"
            + " (i.e. animals, daysInMilk, milk) regarding Milk Production..");

    if (!Strings.isNullOrEmpty(milkProduction.getNrCowsOverall()))
      existingPen.setAnimals(MathUtils.customRound(milkProduction.getNrCowsOverall()));
    if (!Strings.isNullOrEmpty(milkProduction.getAverageDIMOverall()))
      existingPen.setDaysInMilk(MathUtils.customRound(milkProduction.getAverageDIMOverall()));
    if (!Strings.isNullOrEmpty(milkProduction.getAverageYieldOverall()))
      existingPen.setMilk(NumberUtils.toDouble(milkProduction.getAverageYieldOverall()));

    if (nutrition != null) {
      log.debug(
          "[HerdServiceImpl][updatePenFromHerdData]...Updating Pen data "
              + " (i.e. dryMatterIntake, rationCost, asFedIntake) regarding Nutrition..");
      if (Calculation.dryMatterIntakeForPen(nutrition) != 0)
        existingPen.setDryMatterIntake(Calculation.dryMatterIntakeForPen(nutrition));
      if (Calculation.rationCostCalculationForPen(nutrition) != 0)
        existingPen.setRationCostPerAnimal(Calculation.rationCostCalculationForPen(nutrition));
      if (Calculation.asFedIntake(nutrition, milkProduction, herd) != 0)
        existingPen.setAsFedIntake(Calculation.asFedIntake(nutrition, milkProduction, herd));
    }

    return existingPen;
  }

  public PenDocument updatePenFromHerdDataMilkRecording(
      PenDocument existingPen,
      Sites site,
      List<DietDocument> diets,
      HerdNutrition nutrition,
      HerdData herd,
      HerdMilkProduction milkProduction,
      MilkRecording milkRecording) {

    log.debug("[HerdServiceImpl][updatePenFromHerdDataMilkRecording]...Entering..");

    existingPen.setSiteId(site.getSiteDocument().getId());
    existingPen.setDietId(getDietId(milkRecording, diets));
    existingPen.setBarnId(Objects.requireNonNull(site.getSiteDocument().getBarns().get(0).getId()));
    existingPen.setCustomerAccountId(site.getSiteDocument().getAccountId());
    existingPen.setSource(PenSource.DDW);
    existingPen.setDietSource(DietSource.SYSTEM_GENERATED);
    existingPen.setName(
        milkProduction != null ? milkProduction.getGroupName() : existingPen.getName());
    log.debug(
        "[HerdServiceImpl][updatePenFromHerdDataMilkRecording]...Pen Name : "
            + existingPen.getName());
    existingPen.setIsDeleted(false);

    if (milkProduction != null) {
      if (!Strings.isNullOrEmpty(milkProduction.getNrCowsOverall()))
        existingPen.setAnimals(MathUtils.customRound(milkProduction.getNrCowsOverall()));
      if (!Strings.isNullOrEmpty(milkProduction.getAverageDIMOverall()))
        existingPen.setDaysInMilk(MathUtils.customRound(milkProduction.getAverageDIMOverall()));
      if (!Strings.isNullOrEmpty(milkProduction.getAverageYieldOverall()))
        existingPen.setMilk(NumberUtils.toDouble(milkProduction.getAverageYieldOverall()));
    }

    if (nutrition != null) {
      if (Calculation.dryMatterIntakeForPen(nutrition) != 0)
        existingPen.setDryMatterIntake(Calculation.dryMatterIntakeForPen(nutrition));
      if (Calculation.rationCostCalculationForPen(nutrition) != 0)
        existingPen.setRationCostPerAnimal(Calculation.rationCostCalculationForPen(nutrition));
      if (Calculation.asFedIntake(nutrition, milkProduction, herd) != 0)
        existingPen.setAsFedIntake(Calculation.asFedIntake(nutrition, milkProduction, herd));
    }

    return existingPen;
  }

  public void updateSiteWithHerdData(Sites site, List<Diets> diets, HerdData herdData) {
    site.getSiteDocument().setDDWLastUpdatedDate(Instant.now());

    log.debug(
        "[HerdServiceImpl][updateSiteWithHerdData]...  Value updated for DDWLastUpdatedDate "
            + site.getSiteDocument().getDDWLastUpdatedDate());

    // Set Site calculations
    setValues(herdData, site);

    // Add Or Update Diets
    updateDiets(diets);

    // Add Or Update Pens
    addOrUpdatePens(site, herdData, diets);
  }

  public void setValues(HerdData herdData, Sites site) {
    log.debug("[HerdServiceImpl][setValues]... Calculating Site Fields ");

    // Set Herd Inventory Data
    setHerdInventoryData(herdData.getInventory(), site);

    // Set Milk Recording
    setValuesMilk(herdData, site);

    // Set Milk Recording Percentages
    setMilkRecordingPercentage(herdData.getMilkRecordings(), site);

    // Set Site Nutrition
    setSiteNutrition(herdData.getNutritions(), site);
  }

  private void setHerdInventoryData(HerdInventory inventory, Sites site) {
    log.debug("[HerdServiceImpl][setHerdInventoryData]... Calculating Site Fields ");

    if (inventory != null) {
      String nrMilkingCows = inventory.getNrMilkingCows();
      if (!Strings.isNullOrEmpty(nrMilkingCows)) {
        site.getSiteDocument().setLactatingAnimal(MathUtils.customRound(nrMilkingCows));
        log.debug(
            "[HerdServiceImpl][setHerdInventoryData]...Site_LactatingAnimal : "
                + site.getSiteDocument().getLactatingAnimal());
      }

      String avgDimDaysExclDry = inventory.getAvgDimDaysExclDry();
      if (!Strings.isNullOrEmpty(avgDimDaysExclDry)) {
        site.getSiteDocument().setDaysInMilk(MathUtils.customRound(avgDimDaysExclDry));

        log.debug(
            "[HerdServiceImpl][setHerdInventoryData]...Site_DaysInMilk : "
                + site.getSiteDocument().getDaysInMilk());
      }
    }
  }

  public void setValuesMilk(HerdData herdData, Sites site) {
    log.debug("[HerdServiceImpl][setValuesMilk]... Calculating Site Milk Fields ");

    List<HerdMilkProduction> milkProductions = herdData.getMilkProductions();
    List<MilkRecording> milkRecordings = herdData.getMilkRecordings();

    long countMilkProduction = countValidProductions(milkProductions);
    long countMilkRecording = countValidMilkRecordings(milkRecordings);

    if (countMilkProduction > 0) {
      double milkYieldPercent = Calculation.calculateMilkYieldPercent(milkProductions);
      log.debug(
          "[HerdServiceImpl][setValuesMilk]...Set Site Milk against Milk Production : "
              + milkYieldPercent);
      updateSiteMilk(site, milkYieldPercent);
    } else if (countMilkRecording > 0) {
      double milkYieldPercent = Calculation.calculateMilkYieldPercent(milkRecordings, 0);
      log.debug(
          "[HerdServiceImpl][setValuesMilk]...Set Site Milk against Milk Recording : "
              + milkYieldPercent);
      updateSiteMilk(site, milkYieldPercent);
    }
  }

  private long countValidProductions(List<HerdMilkProduction> items) {
    log.debug("[HerdServiceImpl][countValidProductions]...Entering..");

    if (items != null) {
      return items.stream()
          .filter(item -> item != null && HERD_LACTATING_COWS.equalsIgnoreCase(item.getGroupName()))
          .count();
    }
    return 0;
  }

  private long countValidMilkRecordings(List<MilkRecording> items) {
    log.debug("[HerdServiceImpl][countValidMilkRecordings]...Entering..");

    if (items != null) {
      return items.stream()
          .filter(item -> item != null && HERD_LACTATING_COWS.equalsIgnoreCase(item.getGroupName()))
          .count();
    }
    return 0;
  }

  private long countValidNutrition(List<HerdNutrition> items) {
    log.debug("[HerdServiceImpl][countValidNutrition]...Entering..");

    if (items != null) {
      return items.stream()
          .filter(item -> item != null && HERD_LACTATING_COWS.equalsIgnoreCase(item.getGroupName()))
          .count();
    }
    return 0;
  }

  private void updateSiteMilk(Sites site, double milkYieldPercent) {
    log.debug("[HerdServiceImpl][updateSiteMilk]...Entering..");

    if (milkYieldPercent != 0) {
      site.getSiteDocument().setMilk(milkYieldPercent);
    }
  }

  private void setMilkRecordingPercentage(List<MilkRecording> recordings, Sites site) {
    log.debug("[HerdServiceImpl][setMilkRecordingPercentage]...Entering.. ");

    if (countValidMilkRecordings(recordings) > 0) {
      double milkFatPercent = Calculation.calculateMilkFatPercent(recordings);
      log.debug(
          "[HerdServiceImpl][setMilkRecordingPercentage]...Site Milk Fat Percent against "
              + "Milk Recording : "
              + milkFatPercent);

      if (milkFatPercent != 0) site.getSiteDocument().setMilkFatPercent(milkFatPercent);
      double milkProteinPercent = Calculation.calculateMilkProteinPercentage(recordings);

      log.debug(
          "[HerdServiceImpl][setMilkRecordingPercentage]...Site Milk Protein Percent against "
              + " Milk Recording : "
              + milkProteinPercent);

      if (milkProteinPercent != 0) site.getSiteDocument().setMilkProteinPercent(milkProteinPercent);

      double milkSCC = Calculation.calculateMilkSCC(recordings);
      log.debug(
          "[HerdServiceImpl][setMilkRecordingPercentage]...Site Milk Somatic Cell against Milk"
              + " Recording : "
              + milkSCC);

      if (milkSCC != 0) site.getSiteDocument().setMilkSomaticCellCount((int) milkSCC);
    }
  }

  private void setSiteNutrition(List<HerdNutrition> nutritionList, Sites site) {
    log.debug("[HerdServiceImpl][setSiteNutrition] ... Entering.. ");

    if (countValidNutrition(nutritionList) > 0) {

      // Set Dry Matter Intake
      double dryMaterIntake = Calculation.calculateDryMaterIntake(nutritionList);
      log.debug(
          "[HerdServiceImpl][setSiteNutrition] ... Site Dry Matter Intake : " + dryMaterIntake);

      if (dryMaterIntake != 0) {
        site.getSiteDocument().setDryMatterIntake(dryMaterIntake);
      }

      // Set Site Fed Intake
      double asFedIntake = Calculation.calculateAsFedIntake(nutritionList);
      log.debug("[HerdServiceImpl][setSiteNutrition] ... Site As Fed Intake : " + asFedIntake);

      if (asFedIntake != 0) {
        site.getSiteDocument().setAsFedIntake(asFedIntake);
      }

      // Set Ration Cost
      double rationCost = Calculation.calculateRationCost(nutritionList);
      log.debug("[HerdServiceImpl][setSiteNutrition] ... Site Ration Cost : " + rationCost);

      if (rationCost != 0) {
        site.getSiteDocument().setRationCost(rationCost);
      }
    }
  }

  public boolean updateAccountWithHerdData(
      Accounts account, SiteMappings siteMapping, HerdData herdData) {

    log.debug("[HerdServiceImpl][updateAccountWithHerdData]...Entering..");

    if (account == null || account.getAccountDocument() == null) {
      return false;
    }

    String labyrinthSiteId = siteMapping.getSiteMappingDocument().getLabyrinthSiteId().toString();
    Sites site = sitesRepository.findBySiteId(labyrinthSiteId);
    if (site == null) {
      return false;
    }

    List<Diets> diets = dietRepository.findBySiteId(labyrinthSiteId);

    updateSiteWithHerdData(site, diets, herdData);
    siteService.updateSiteMappingsMax(site, siteMapping.getSiteMappingDocument());

    Instant currentTimeUtc = getCurrentUtcTime();
    // Trigger Site Integration Update
    log.debug(
        "[HerdServiceImpl][updateAccountWithHerdData] ... SiteLastSyncTimeUtc : " + currentTimeUtc);
    site.getSiteDocument().setLastSyncTimeUtc(currentTimeUtc);

    // Trigger Mobile Updates
    account.getAccountDocument().setLastModifiedTimeUtc(currentTimeUtc);

    accountsRepository.save(account);
    sitesRepository.save(site);

    for (Diets diet : diets) {
      DietDocument dietDocument = diet.getDietDocument();
      dietDocument.setLabyrinthAccountId(account.getAccountDocument().getId());
      dietDocument.setSiteId(site.getSiteDocument().getId());
      dietRepository.save(diet);
    }

    return true;
  }

  public static Instant getCurrentUtcTime() {
    // pass UTC date to main method.
    return Instant.now();
  }

  private void updateOrSavePen(Pens pen) {
    log.debug("[HerdServiceImpl][updateOrSavePen]...Entering..");

    PenDocument penDocument = pen.getPenDocument();
    String siteId = penDocument.getSiteId().toString();
    String penName = penDocument.getName();
    String groupId = penDocument.getGroupId();

    Pens existingPen = null;

    if (penDocument.getId() != null) {
      existingPen = pensRepository.findByPenId(penDocument.getId().toString());
    }

    if (existingPen == null) {
      existingPen =
          pensRepository.findBySiteIdAndPenNameAndSourceAndGroupId(
              siteId, penName, PenSource.DDW.name(), groupId);
    }

    if (existingPen != null) {
      log.debug(
          "[HerdServiceImpl][updateOrSavePen] updating pen: "
              + existingPen.getPenDocument().getId());
      existingPen.setPenDocument(penDocument);
      pensRepository.save(existingPen);
    } else {
      pensRepository.save(pen);
    }
  }

  public UUID determineAnimalClassId(HerdCulling herdCulling) {

    if (herdCulling != null) {
      String groupCategory = herdCulling.getGroupCategory();

      if (!Strings.isNullOrEmpty(groupCategory)) {
        String trimmedCategory = groupCategory.trim();

        if ("HEIFERS".equalsIgnoreCase(trimmedCategory)) {
          return HerdConstant.HEIFER_ID;
        } else if ("DRYCOWS".equalsIgnoreCase(trimmedCategory)) {
          return HerdConstant.DRY_FAR_OFF_ID;
        } else if ("LACTATINGCOWS".equalsIgnoreCase(trimmedCategory)
            || "UNKNOWN".equalsIgnoreCase(trimmedCategory)) {
          return HerdConstant.LACTATING_MILKING_ID;
        }
      }
    }

    return null;
  }

  public List<Pens> getUniquePens(List<Pens> pens) {
    Map<String, Pens> firstPenInEachGroup = new LinkedHashMap<>();

    for (Pens pen : pens) {
      String name = pen.getPenDocument().getName();
      firstPenInEachGroup.putIfAbsent(name, pen);
    }

    return new ArrayList<>(firstPenInEachGroup.values());
  }

  public void setSiteNetEnergyLactationMilk(
      HerdData herdData, List<Pens> existingPens, Sites site) {
    log.trace("[HerdServiceImpl][setSiteNetEnergyLactationMilk].. Entering.. ");
    if (herdData.getNutritions() != null
        && !herdData.getNutritions().isEmpty()
        && existingPens != null
        && !existingPens.isEmpty()) {
      try {
        Double netEnergyOfLactationDairy = pensService.getNetEnergyOfLactationDairy(existingPens);

        if (netEnergyOfLactationDairy != 0) {
          log.debug(
              "[HerdServiceImpl][setSiteNetEnergyLactationMilk] ... Site Net Energy Of Lactation"
                  + " Dairy : "
                  + site.getSiteDocument().getNetEnergyOfLactationDairy());

          site.getSiteDocument().setNetEnergyOfLactationDairy(netEnergyOfLactationDairy);
        }
      } catch (CustomDEExceptions ex) {

        log.error("[HerdServiceImpl][addOrUpdatePens] ---> Exception " + ex.getMessage());
      }
    }
  }
}
