/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class TMR implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("HerdProfile")
  private HerdProfile herdProfile;

  @JsonProperty("Feeding")
  private FeedingReturnOverFeed feeding;

  @JsonProperty("MilkProduction")
  private MilkProduction milkProduction;

  @JsonProperty("MilkProductionOutputs")
  private MilkProductionOutputs milkProductionOutputs;

  @JsonProperty("Summary")
  private Summary summary;

  @JsonProperty("CalculatedOutputs")
  private CalculatedOutputs calculatedOutputs;
}
