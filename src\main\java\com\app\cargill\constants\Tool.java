/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115")
public enum Tool {
  // //[Description("Scorecard")]
  CalfHeiferScorecard(101),
  // //[Description("Heat Stress Evaluation")]
  HeatStress(201),
  // [Description("Pen Time")]
  PenTimeBudgetTool(202),
  // [Description("Rumen Health Cud Chewing")]
  RumenHealth(301),
  // [Description("Rumen Health TMR Particle Score")]
  TMRParticleScore(302),
  // [Description("Rumen Health Manure Score")]
  RumenHealthManureScore(303),
  // [Description("Rumen Health Manure Screener")]
  ManureScreener(304),
  // [Description("RumenFill")]
  RumenFill(305),
  // [Description("Locomotion Score")]
  LocomotionScore(306),
  // [Description("Body Condition Score")]
  BodyCondition(307),
  // [Description("Urine pH")]
  UrinePHTool(308),
  // [Description("Metabolic Incidence")]
  MetabolicIncidence(309),
  // [Description("Ready2Milk")]
  ReadyToMilk(310),
  // [Description("Forage Audit")]
  ForageAuditScorecard(401),
  // [Description("Forage Inventories")]
  PileAndBunker(402),
  ForagePennState(403),
  // [Description("Milk Procedure Comparison")]
  Revenue(501),
  // [Description("Milk Solid Evaluation")]
  MilkSoldEvaluation(502),
  // [Description("Robotic Milking Evaluation")]
  RoboticMilkEvaluation(503),

  profitabilityAnalysis(504),
  ReturnOverFeed(505);

  final Integer toolValue;

  Tool(Integer tool) {
    this.toolValue = tool;
  }

  public Integer getTool() {
    return toolValue;
  }

  public static Tool fromId(int tool) {
    for (Tool item : values()) {
      if (item.getTool().intValue() == tool) {
        return item;
      }
    }
    return null;
  }
}
