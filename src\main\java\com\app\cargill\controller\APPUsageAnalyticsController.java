/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.model.UserDailyLogin;
import com.app.cargill.service.admin.UserAdminService;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@Slf4j
@RequiredArgsConstructor
@Controller
@RequestMapping("/app-usage")
public class APPUsageAnalyticsController extends BaseController {

  private final UserAdminService userAdminService;

  /*
   * runs at 1 am
   */
  @GetMapping(path = "/getUsersAllLogin")
  public ResponseEntity<ResponseEntityDto<List<UserDailyLogin>>> getUsersAllLogin(
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateFrom")
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          Instant dateFrom,
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateTo")
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          Instant dateTo) {

    log.info("getUsersAllLogin invoked");
    List<UserDailyLogin> page = userAdminService.getUsersAllLogin(dateFrom, dateTo);
    log.info(
        "getUsersAllLogin {} {} {}",
        page.size(),
        StringEscapeUtils.escapeJava(dateFrom.toString()),
        StringEscapeUtils.escapeJava(dateTo.toString()));
    return handleSuccessResponse(page);
  }
}
