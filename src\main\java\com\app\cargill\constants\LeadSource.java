/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum LeadSource {
  Advertisement(1),

  // [EnumMember(Value = "Employee Referral")]
  EmployeeReferral(2),

  // [EnumMember(Value = "External Referral")]
  ExternalReferral(3),

  Partner(4),

  // [EnumMember(Value = "Public Relations")]
  PublicRelations(5),

  // [EnumMember(Value = "Seminar - Internal")]
  SeminarInternal(6),

  // [EnumMember(Value = "Seminar - Partner")]
  SeminarPartner(7),

  // [EnumMember(Value = "Trade Show")]
  TradeShow(8),

  Web(9),

  // [EnumMember(Value = "Word of mouth")]
  Wordofmouth(10),

  Other(11);

  private final Integer value;

  LeadSource(Integer value) {
    this.value = value;
  }

  public Integer getValue() {
    return value;
  }
}
