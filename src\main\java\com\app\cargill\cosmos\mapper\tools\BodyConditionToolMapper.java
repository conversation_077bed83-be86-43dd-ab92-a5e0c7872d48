/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.BodyConditionToolCosmos;
import com.app.cargill.cosmos.model.tools.BodyConditionToolItemCosmos;
import com.app.cargill.document.BodyConditionTool;
import com.app.cargill.document.BodyConditionToolItem;
import java.time.Instant;
import java.util.UUID;

public class BodyConditionToolMapper {

  private BodyConditionToolMapper() {}

  public static BodyConditionTool map(BodyConditionToolCosmos input) {

    CosmosToModelMapper<BodyConditionToolItemCosmos, BodyConditionToolItem> itemMapper =
        source ->
            BodyConditionToolItem.builder()
                .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
                .penName(source.getPenName())
                .bodyConditionScoreVisitsSelected(
                    source.getBodyConditionScoreVisitsSelected() != null
                        ? source.getBodyConditionScoreVisitsSelected().stream()
                            .map(UUID::fromString)
                            .toList()
                        : null)
                .bodyConditionScores(source.getBodyConditionScores())
                .toolStatus(source.getToolStatus())
                .build();

    CosmosToModelMapper<BodyConditionToolCosmos, BodyConditionTool> mapper =
        source ->
            BodyConditionTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(
                    source.getLastModifiedTimeUtc() != null
                        ? source.getLastModifiedTimeUtc().getDate()
                        : Instant.MIN)
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .goals(source.getGoals())
                .build();

    return mapper.map(input);
  }
}
