/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdMilkProduction extends HerdBase {

  @JsonProperty("AverageDIMLact1")
  private String averageDIMLact1;

  @JsonProperty("AverageDIMLact2")
  private String averageDIMLact2;

  @JsonProperty("AverageDIMLact3plus")
  private String averageDIMLact3plus;

  @JsonProperty("AverageDIMOverall")
  private String averageDIMOverall;

  @JsonProperty("AverageYield0DIM15")
  private String averageYield0DIM15;

  @JsonProperty("AverageYield120DIM200")
  private String averageYield120DIM200;

  @JsonProperty("AverageYield15DIM60")
  private String averageYield15DIM60;

  @JsonProperty("AverageYield200DIM305")
  private String averageYield200DIM305;

  @JsonProperty("AverageYield60DIM120")
  private String averageYield60DIM120;

  @JsonProperty("AverageYieldDIM305plus")
  private String averageYieldDIM305plus;

  @JsonProperty("AverageYieldLact1")
  private String averageYieldLact1;

  @JsonProperty("AverageYieldLact2")
  private String averageYieldLact2;

  @JsonProperty("AverageYieldLact3plus")
  private String averageYieldLact3plus;

  @JsonProperty("AverageYieldOverall")
  private String averageYieldOverall;

  @JsonProperty("NrCows0DIM15")
  private String nrCows0DIM15;

  @JsonProperty("NrCows120DIM200")
  private String nrCows120DIM200;

  @JsonProperty("NrCows15DIM60")
  private String nrCows15DIM60;

  @JsonProperty("NrCows200DIM305")
  private String nrCows200DIM305;

  @JsonProperty("NrCows305plus")
  private String nrCows305plus;

  @JsonProperty("NrCows60DIM120")
  private String nrCows60DIM120;

  @JsonProperty("NrCowsLact1")
  private String nrCowsLact1;

  @JsonProperty("NrCowsLact2")
  private String nrCowsLact2;

  @JsonProperty("NrCowsLact3plus")
  private String nrCowsLact3plus;

  @JsonProperty("NrCowsOverall")
  private String nrCowsOverall;

  @JsonProperty("pen_uuid")
  private UUID penId;
}
