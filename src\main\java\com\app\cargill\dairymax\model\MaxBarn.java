/* Cargill Inc.(C) 2022 */
package com.app.cargill.dairymax.model;

import com.app.cargill.document.PenDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaxBarn extends com.app.cargill.document.Barn {

  @JsonProperty("Pens")
  private List<PenDocument> pens;

  public MaxBarn(List<PenDocument> pens, UUID id, String barnName, String createUser) {
    super(id, barnName, createUser);
    this.pens = pens;
  }
}
