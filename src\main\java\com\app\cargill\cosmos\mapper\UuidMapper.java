/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper;

import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UuidMapper {

  private UuidMapper() {}

  public static UUID getNullableUuid(String input) {
    try {
      return input != null ? UUID.fromString(input) : null;
    } catch (Exception e) {
      log.warn("INVALID_UUID_INPUT {}", input, e);
      return null;
    }
  }
}
