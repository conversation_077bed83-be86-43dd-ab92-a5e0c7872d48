/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper;

import com.app.cargill.constants.OptimizationType;
import com.app.cargill.cosmos.model.PenCosmos;
import com.app.cargill.document.PenDocument;
import java.util.ArrayList;
import java.util.UUID;

public class PenMapper {
  private PenMapper() {}

  public static PenDocument map(PenCosmos input) {

    CosmosToModelMapper<PenCosmos, PenDocument> penMapper =
        source ->
            PenDocument.builder()
                .id(getValidId(source.getId()))
                .customerAccountId(
                    source.getCustomerAccountId() != null
                        ? UUID.fromString(source.getCustomerAccountId())
                        : null)
                .siteId(source.getSiteId() != null ? UUID.fromString(source.getSiteId()) : null)
                .barnId(source.getBarnId() != null ? UUID.fromString(source.getBarnId()) : null)
                .source(source.getSource())
                .name(source.getName())
                .barn(source.getBarn())
                .animals(source.getAnimals())
                .daysInMilk(source.getDaysInMilk())
                .milk(source.getMilk())
                .milkingFrequency(source.getMilkingFrequency())
                .housingSystemType(source.getHousingSystemType())
                .feedingSystemType(source.getFeedingSystemType())
                .numberOfStalls(source.getNumberOfStalls())
                .dietId(source.getDietId() != null ? UUID.fromString(source.getDietId()) : null)
                .dryMatterIntake(source.getDryMatterIntake())
                .asFedIntake(source.getAsFedIntake())
                .rationCostPerAnimal(source.getRationCostPerAnimal())
                .isDeleted(source.getIsDeleted())
                .isMapped(source.getIsMapped())
                .associatedPens(
                    source.getAssociatedPens() != null
                        ? source.getAssociatedPens().stream().map(UUID::fromString).toList()
                        : new ArrayList<>())
                .createUser(source.getCreateUser())
                .netEnergyOfLactationDairy(source.getNetEnergyOfLactationDairy())
                .dietSource(source.getDietSource())
                .optimizationType(
                    source.getOptimizationType() != null
                        ? OptimizationType.fromId(Integer.valueOf(source.getOptimizationType()))
                        : null)
                .groupId(source.getGroupId())
                .optimizationId(source.getOptimizationId())
                .build();

    return penMapper.map(input);
  }

  private static UUID getValidId(String id) {
    // There are some invalid ids which were set to zeros for unknown reason
    if (id == null || "00000000-0000-0000-0000-000000000000".equals(id)) {
      return UUID.randomUUID();
    } else {
      return UUID.fromString(id);
    }
  }
}
