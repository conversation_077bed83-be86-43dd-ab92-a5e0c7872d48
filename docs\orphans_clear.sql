select *
from users
where <PERSON><PERSON><PERSON>(user_document ->> 'UserName') in (
    <PERSON><PERSON><PERSON>('chantal_van_der_mei<PERSON><PERSON>@cargill.com'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('rao_kanwal<PERSON><PERSON>@crgl-thirdparty.com'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<EMAIL>'),
    <PERSON><PERSON><PERSON>('<PERSON><PERSON>_<PERSON><EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>'),
    LOWER('<EMAIL>')
);

-- Select orphaned accounts that do not have active platform user
SELECT * from accounts where account_document->>'id' not in (
    SELECT acc.account_id
    FROM (
        select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
    ) acc
    JOIN users u on u.user_document->>'UserName' = acc.user_name
    GROUP BY acc.account_id);
-- delete
delete from accounts where account_document->>'id' not in (
    SELECT acc.account_id
    FROM (
             select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
         ) acc
             JOIN users u on u.user_document->>'UserName' = acc.user_name
    GROUP BY acc.account_id);

-- Select orphaned diets that do not have account
select d.diet_document->>'id' from diets d left join accounts a on d.diet_document->>'LabyrinthAccountId' = a.account_document->>'id' where a.account_document->>'id' is null;
-- delete
delete from diets where diet_document->>'id' in (select d.diet_document->>'id' from diets d left join accounts a on d.diet_document->>'LabyrinthAccountId' = a.account_document->>'id' where a.account_document->>'id' is null)