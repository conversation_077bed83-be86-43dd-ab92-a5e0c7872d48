/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.CalfHeiferScorecardCosmos;
import com.app.cargill.document.CalfHeiferScorecard;
import java.util.UUID;

public class CalfHeiferScorecardMapper {

  private CalfHeiferScorecardMapper() {}

  public static CalfHeiferScorecard map(CalfHeiferScorecardCosmos input) {

    CosmosToModelMapper<CalfHeiferScorecardCosmos, CalfHeiferScorecard> mapper =
        source ->
            CalfHeiferScorecard.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .sections(source.getSections())
                .build();

    return mapper.map(input);
  }
}
