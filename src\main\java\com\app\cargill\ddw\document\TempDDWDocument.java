/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.document;

import com.app.cargill.ddw.model.HerdData;
import com.app.cargill.document.EditableDocumentBase;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class TempDDWDocument extends EditableDocumentBase implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Herd")
  private HerdData herd;
}
