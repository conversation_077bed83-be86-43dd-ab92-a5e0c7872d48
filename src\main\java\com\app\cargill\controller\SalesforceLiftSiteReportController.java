/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.sf.cc.model.simple.SiteUpdateModel;
import com.app.cargill.sf.cc.service.LiftSitesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/lift/siteReport")
@Tag(
    name = "Salesforce Lift SiteReport",
    description = "Controller related to actions over SiteReport objects")
@RequiredArgsConstructor
@Slf4j
public class SalesforceLiftSiteReportController {
  private final LiftSitesService liftSitesService;

  @PostMapping
  @Operation(summary = "Create a LIFT SiteReport", description = "Create a lift SiteReport by")
  public Boolean createSiteReport(@RequestBody SiteUpdateModel siteUpdateModel) {
    return liftSitesService.updateSiteReport(
        siteUpdateModel.getExternalId(),
        siteUpdateModel.getHerdSummaryReport(),
        siteUpdateModel.getHerdSummaryReport());
  }
}
