/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum ToolGroup {
  CalfandHeifer(1),
  Comfort(2),
  Health(3),
  Nutrition(4),
  Productivity(5);

  private final Integer value;

  ToolGroup(Integer value) {
    this.value = value;
  }

  public Integer getValue() {
    return value;
  }

  public static ToolGroup fromId(int id) {
    for (ToolGroup type : values()) {
      if (type.getValue() == id) {
        return type;
      }
    }
    return null;
  }
}
