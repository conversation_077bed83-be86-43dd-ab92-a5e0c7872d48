/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.Business;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import org.springframework.data.annotation.Id;

@Container(containerName = "User")
@Getter
public class UserCosmos {

  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("UserName")
  private String userName;

  @JsonProperty("CountryId")
  private Business countryId;

  @JsonProperty("SalesforceCountryId")
  private Integer salesforceCountryId;
}
