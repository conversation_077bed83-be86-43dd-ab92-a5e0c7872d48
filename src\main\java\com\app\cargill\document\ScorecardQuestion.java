/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScorecardQuestion implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Indicates where this question is presented in the order of all questions.
  /// </summary>
  /// <value>The index.</value>
  @JsonProperty("Index")
  private Integer index;
  /// <summary>
  /// The question of the text to be displayed to the user.
  /// </summary>
  /// <value>The question text.</value>
  /// <remarks>This will be a key to the string resources files.</remarks>
  @JsonProperty("QuestionText")
  private String questionText;

  /// <summary>
  /// Indicates if the scorecard this question is part of can be considered
  /// complete
  /// if this question is not answered.
  /// </summary>
  /// <value>The is question required.</value>
  @JsonProperty("IsQuestionRequired")
  private Boolean isQuestionRequired;

  /// <summary>
  /// All the answers available for this question.
  /// </summary>
  @JsonProperty("AvailableAnswers")
  private List<ScorecardAnswer> availableAnswers;
  /// <summary>
  /// Which answer is the currently selected one.
  /// </summary>
  /// <value>The selected answer.</value>
  @JsonProperty("SelectedAnswer")
  private ScorecardAnswer selectedAnswer;
}
