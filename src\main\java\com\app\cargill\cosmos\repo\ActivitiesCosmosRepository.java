/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.ActivityCosmos;
import com.azure.spring.data.cosmos.repository.Query;
import com.azure.spring.data.cosmos.repository.ReactiveCosmosRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface ActivitiesCosmosRepository
    extends ReactiveCosmosRepository<ActivityCosmos, String> {
  @Query("Select * from c where c.AccountId = @accountId")
  Flux<ActivityCosmos> findAllByAccountId(@Param("accountId") String accountId);
}
