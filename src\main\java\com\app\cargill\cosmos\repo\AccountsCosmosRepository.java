/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.AccountCosmos;
import com.azure.spring.data.cosmos.repository.CosmosRepository;
import com.azure.spring.data.cosmos.repository.Query;
import java.util.List;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AccountsCosmosRepository extends CosmosRepository<AccountCosmos, String> {

  @Query(
      value = "Select * FROM c WHERE c.OwnerId = @userName OR ARRAY_CONTAINS(c.Users, @userName)")
  List<AccountCosmos> getAccountsByUserEmail(@Param("userName") String userName);

  @Query(value = "Select * FROM c WHERE c.id = @id OR ARRAY_CONTAINS(c.Users, @id)")
  List<AccountCosmos> getAccountsById(@Param("id") String id);
}
