<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="041" author="Analytics Team">
    <sql>
        CREATE TABLE IF NOT EXISTS user_activity_log (
            id BIGSERIAL NOT NULL,
            created_date TIMESTAMP,
            deleted BOOLEAN DEFAULT FALSE,
            local_id VARCHAR(255),
            updated_date TIMESTAMP,
            
            -- Core tracking fields
            event_name VARCHAR(255) NOT NULL,
            path VARCHAR(500) NOT NULL,
            username VARCHA<PERSON>(255) NOT NULL,
            user_id VARCHAR(255),
            session_id VARCHAR(255),
            
            -- Request/Response information
            ip_address VARCHAR(45),
            user_agent TEXT,
            browser VARCHAR(100),
            operating_system VARCHAR(100),
            device_type VARCHAR(50),
            screen_resolution VARCHAR(20),
            referrer_url TEXT,
            page_title VARCHAR(500),
            
            -- User interaction details
            action_type VARCHAR(100),
            element_clicked VARCHAR(255),
            form_data TEXT,
            response_time_ms BIGINT,
            error_message TEXT,
            http_status_code INTEGER,
            request_method VARCHAR(10),
            request_size_bytes BIGINT,
            response_size_bytes BIGINT,
            
            -- User behavior metrics
            time_on_page_seconds BIGINT,
            scroll_depth_percentage DOUBLE PRECISION,
            
            -- Geographic information
            country VARCHAR(100),
            region VARCHAR(100),
            city VARCHAR(100),
            timezone VARCHAR(50),
            language VARCHAR(10),
            
            -- Business context
            account_id VARCHAR(255),
            site_id VARCHAR(255),
            visit_id VARCHAR(255),
            feature_used VARCHAR(255),
            module_name VARCHAR(255),
            
            -- Timestamps and additional data
            last_visited TIMESTAMP,
            additional_data TEXT,
            
            PRIMARY KEY (id)
        );

        -- Create indexes for better query performance
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_username ON user_activity_log(username);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_event_name ON user_activity_log(event_name);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_created_date ON user_activity_log(created_date);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_updated_date ON user_activity_log(updated_date);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_path ON user_activity_log(path);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_account_id ON user_activity_log(account_id);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_site_id ON user_activity_log(site_id);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_feature_used ON user_activity_log(feature_used);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_module_name ON user_activity_log(module_name);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_session_id ON user_activity_log(session_id);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_last_visited ON user_activity_log(last_visited);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_deleted ON user_activity_log(deleted);
        
        -- Composite indexes for common query patterns
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_username_created_date ON user_activity_log(username, created_date);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_event_name_created_date ON user_activity_log(event_name, created_date);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_account_id_created_date ON user_activity_log(account_id, created_date);
        CREATE INDEX IF NOT EXISTS idx_user_activity_log_updated_date_deleted ON user_activity_log(updated_date, deleted);
    </sql>
  </changeSet>
</databaseChangeLog>
