/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.RoboticMilkEvaluationToolCosmos;
import com.app.cargill.cosmos.model.tools.RoboticMilkEvaluationToolItemCosmos;
import com.app.cargill.document.RoboticMilkEvaluationTool;
import com.app.cargill.document.RoboticMilkEvaluationToolItem;
import java.util.UUID;

public class RoboticMilkEvaluationToolMapper {

  private RoboticMilkEvaluationToolMapper() {}

  public static RoboticMilkEvaluationTool map(RoboticMilkEvaluationToolCosmos input) {

    CosmosToModelMapper<RoboticMilkEvaluationToolItemCosmos, RoboticMilkEvaluationToolItem>
        itemMapper =
            source ->
                RoboticMilkEvaluationToolItem.builder()
                    .outputs(source.getOutputs())
                    .selectedVisits(
                        source.getSelectedVisits() != null
                            ? source.getSelectedVisits().stream().map(UUID::fromString).toList()
                            : null)
                    .robotType(source.getRobotType())
                    .cowFlowDesign(source.getCowFlowDesign())
                    .robotsInHerd(source.getRobotsInHerd())
                    .lactatingCows(source.getLactatingCows())
                    .averageMilkYield(source.getAverageMilkYield())
                    .milkings(source.getMilkings())
                    .robotFreeTime(source.getRobotFreeTime())
                    .milkingRefusals(source.getMilkingRefusals())
                    .totalMilkingFailures(source.getTotalMilkingFailures())
                    .maximumConcentrate(source.getMaximumConcentrate())
                    .averageConcentrateFed(source.getAverageConcentrateFed())
                    .minimumConcentrate(source.getMinimumConcentrate())
                    .averageBoxTime(source.getAverageBoxTime())
                    .milkingSpeed(source.getMilkingSpeed())
                    .concentratePer100KGMilk(source.getConcentratePer100KGMilk())
                    .restFeed(source.getRestFeed())
                    .build();

    CosmosToModelMapper<RoboticMilkEvaluationToolCosmos, RoboticMilkEvaluationTool> mapper =
        source ->
            RoboticMilkEvaluationTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .visitRoboticMilkEvaluationData(
                    source.getVisitRoboticMilkEvaluationData() != null
                        ? itemMapper.map(source.getVisitRoboticMilkEvaluationData())
                        : null)
                .build();

    return mapper.map(input);
  }
}
