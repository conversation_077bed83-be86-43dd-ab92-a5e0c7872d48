/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.RumenHealthManureScoreToolGoalItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class RumenFillToolCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Pens")
  private List<RumenFillScoreToolItemCosmos> pens;

  @JsonProperty("Goals")
  private List<RumenHealthManureScoreToolGoalItem> goals;
}
