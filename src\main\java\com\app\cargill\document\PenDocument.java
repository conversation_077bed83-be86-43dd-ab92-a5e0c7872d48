/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.*;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
/// <summary>
/// A pen is contained within a barn.
/// </summary>
public class PenDocument implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  // region general
  @JsonProperty("Id")
  private UUID id;

  @JsonProperty("CustomerAccountId")
  private UUID customerAccountId;

  @JsonProperty("SiteId")
  private UUID siteId;

  @JsonProperty("BarnId")
  private UUID barnId;

  @JsonProperty("Source")
  // @Enumerated(EnumType.ORDINAL)
  private PenSource source;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Barn")
  private String barn;

  @JsonProperty("HousingSystemType")
  private HousingSystem housingSystemType;

  @JsonProperty("FeedingSystemType")
  private FeedingSystem feedingSystemType;

  // Required if the HousingSystem is type Freestall or TieStall
  @JsonProperty("NumberOfStalls")
  private Integer numberOfStalls;

  @JsonProperty("MilkingFrequency")
  /// accept decimal (2 decimal places) values from 0 to 9.
  /// accepts decimal (1 decimal place) inputs, including zero (0),
  /// and displays either kg label for Metric or lbs for Imperial unit of measure.
  private Double milkingFrequency;

  @JsonProperty("Animals")
  // endregion
  // region animals input
  /// accepts positive whole number inputs
  private Integer animals;

  @JsonProperty("DaysInMilk")
  /// accepts both positive and negative whole number inputs
  private Integer daysInMilk;

  @JsonProperty("Milk")
  /// This is the milk yield.
  /// accepts decimal (1 decimal place) inputs, including zero (0),
  /// and displays either kg label for Metric or lbs for Imperial UoM.
  private Double milk;

  @JsonProperty("DietId")
  // endregion
  // region diet
  /// The diet name and animal class can be derived from the dietId
  private UUID dietId;

  @JsonProperty("AnimalClassId")
  private UUID animalClassId;

  @JsonProperty("AnimalType")
  private AnimalClass animalType;

  @JsonProperty("DryMatterIntake")
  /// accepts decimal (1 decimal place) inputs, and displays either
  /// kg label for Metric or lbs for Imperial unit of measure.
  private Double dryMatterIntake;

  @JsonProperty("AsFedIntake")
  /// accepts decimal (1 decimal place) inputs, and displays either kg
  ///  label for Metric or lbs for Imperial unit of measure
  private Double asFedIntake;

  @JsonProperty("RationCostPerAnimal")
  /// accepts currency(2 decimal places) inputs
  private Double rationCostPerAnimal;

  @JsonProperty("IsDeleted")
  @Builder.Default
  private Boolean isDeleted = false;

  @JsonProperty("IsMapped")
  @Builder.Default
  private Boolean isMapped = false;

  @JsonProperty("AssociatedPens")
  private List<UUID> associatedPens;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("NetEnergyOfLactationDairy")
  private String netEnergyOfLactationDairy;

  @JsonProperty("DietSource")
  private DietSource dietSource;

  @JsonProperty("OptimizationType")
  private OptimizationType optimizationType;

  @JsonProperty("OptimizationId")
  private Integer optimizationId;

  @JsonProperty("GroupId")
  private String groupId;

  @JsonProperty("DDWLastUpdatedDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant dDWLastUpdatedDate;

  public PenDocument() {
    // default settings:
    housingSystemType = HousingSystem.Freestall;
    numberOfStalls = 0;
    feedingSystemType = FeedingSystem.TMR;
  }

  @JsonProperty("Selected")
  @Builder.Default
  private Boolean selected = false;

  @JsonIgnore @Builder.Default private int farOffDryMax = -21;

  @JsonIgnore @Builder.Default private int closeUpDryMax = 0;

  @JsonIgnore @Builder.Default private int freshMax = 15;

  @JsonIgnore @Builder.Default private int earlyLactationMax = 60;

  @JsonIgnore @Builder.Default private int peakMilkMax = 120;

  @JsonIgnore @Builder.Default private int midLactationMax = 200;
}
