/* Cargill Inc.(C) 2022 */
package com.app.cargill.dairymax.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum FormulateStatusMax {
  NotFormulated(0),
  Feasible(1),
  NotFeasible(2),
  Unsafe(3),
  NA(4);

  private final Integer statusCode;

  FormulateStatusMax(Integer code) {
    this.statusCode = code;
  }

  public Integer getFormulateStatus() {
    return statusCode;
  }
}
