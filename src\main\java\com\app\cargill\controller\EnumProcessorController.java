/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.EnumSerializerDto;
import com.app.cargill.dto.MultilingualEnumSerializerDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.IEnumProcessorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/enums")
@Tag(name = "Enum Information Controller", description = "Enum Information Controller")
@RequiredArgsConstructor
@SuppressWarnings("java:S125") // remove when all commented code is removed
public class EnumProcessorController extends BaseController {
  private final IEnumProcessorService enumProcessorServiceImpl;
  private final ResourceBundleMessageSource source;

  @GetMapping
  @Operation(
      summary = "Fetch enums",
      description = "This api will return all enums used application level")
  public ResponseEntity<ResponseEntityDto<EnumSerializerDto>> fetchEnums() {
    return handleSuccessResponse(enumProcessorServiceImpl.fetchEnums());
  }

  @GetMapping("/multilingual")
  @Operation(
      summary = "Fetch enums multilingual",
      description = "This api will return all enums used application level")
  public ResponseEntity<ResponseEntityDto<MultilingualEnumSerializerDto>> fetchEnumsMultilingual() {
    return handleSuccessResponse(enumProcessorServiceImpl.fetchEnumsMultilingual(source));
  }
}
