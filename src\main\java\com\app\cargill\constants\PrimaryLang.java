/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Enum naming intentional
public enum PrimaryLang {
  NotSet(0),
  Afrikaans(1),
  Albanian(2),
  Arabic(3),
  Armenian(4),
  Basque(5),
  Bengali(6),
  Bulgarian(7),
  Cambodian(8),
  Catalan(9),

  // [EnumMember(Value ( "Chinese (Mandarin)")]
  ChineseMandarin(10),
  Croatian(11),
  Czech(12),
  Danish(13),
  Dutch(14),
  English(15),
  Estonian(16),
  Fiji(17),
  Finnish(18),
  French(19),
  Georgian(20),
  German(21),
  Greek(22),
  Gujarati(23),
  Hebrew(24),
  Hindi(25),
  Hungarian(26),
  Icelandic(27),
  Indonesian(28),
  Irish(29),
  Italian(30),
  Japanese(31),
  Javanese(32),
  Korean(33),
  Latin(34),
  Latvian(35),
  Lithuanian(36),
  Macedonian(37),
  Malay(38),
  Malayalam(39),
  Maltese(40),
  <PERSON><PERSON>(41),
  Marathi(42),
  Mongolian(43),
  Nepali(44),
  Norwegian(45),
  Persian(46),
  Polish(47),
  Portuguese(48),
  Punjabi(49),
  Quechua(50),
  Romanian(51),
  Russian(52),
  Samoan(53),
  Serbian(54),
  Slovak(55),
  Slovenian(56),
  Spanish(57),
  Swahili(58),
  Swedish(59),
  Tamil(60),
  Tatar(61),
  Telugu(62),
  Thai(63),
  Tibetan(64),
  Tonga(65),
  Turkish(66),
  Ukrainian(67),
  Urdu(68),
  Uzbek(69),
  Vietnamese(70),
  Welsh(71),
  Xhosa(72);

  private final Integer languageCode;

  PrimaryLang(Integer code) {
    this.languageCode = code;
  }

  public Integer getLanguageCode() {
    return languageCode;
  }
}
