/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.ISelectDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.VisitDto;
import com.app.cargill.dto.VisitPublishDto;
import com.app.cargill.dto.VisitPublishResponseDto;
import com.app.cargill.dto.VisitSearchDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IVisitService;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping("/visit")
@Tag(name = "Visit Information Controller", description = "Visit Information Controller")
@RequiredArgsConstructor
public class VisitController extends BaseController {

  private final IVisitService visitServiceImpl;
  private final ResourceBundleMessageSource resourceBundleMessageSource;

  @GetMapping("/paginated")
  @Operation(
      summary = "Get all visits Paginated",
      description = "This api returns Paginated visits in offline mode.")
  public ResponseEntity<ResponseEntityDto<Page<VisitDto>>> fetchAllVisitsPaginatedInOfflineMode(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        visitServiceImpl.getAllVisitsByCurrentLoggedInUser(
            page, size, sortBy, lastSyncTime, sorting));
  }

  @PostMapping
  @Operation(summary = "save Visit", description = "This api will save the Visit")
  public ResponseEntity<ResponseEntityDto<Object>> save(
      @RequestBody VisitDto visitDto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws JsonProcessingException, NotFoundDEException, AlreadyExistsDEException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {
    try {
      Locale locale = Locale.forLanguageTag(localeString);
      LocaleContextHolder.setLocale(locale);
      return handleSuccessResponse(
          visitServiceImpl.save(visitDto, locale, resourceBundleMessageSource));
    } catch (CustomDEExceptions e) {
      return handleResponse(
          "Error: Something happened.",
          e.getLocalizedMessage(),
          ResponseStatus.FAILED,
          HttpStatus.NOT_ACCEPTABLE);
    }
  }

  @GetMapping("/paginated/online")
  @Operation(
      summary = "Get all visits Paginated",
      description = "This api returns Paginated visits in Online mode.")
  public ResponseEntity<ResponseEntityDto<Page<VisitDto>>> fetchAllVisitsPaginatedInOnlineMode(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting,
      VisitSearchDto visitSearchDto) {

    return handleSuccessResponse(
        visitServiceImpl.getAllVisitsBySearchPaginated(
            page, size, sortBy, lastSyncTime, sorting, visitSearchDto));
  }

  @PutMapping
  @Operation(summary = "Update a Visit", description = "This api will update the Visit")
  public ResponseEntity<ResponseEntityDto<Object>> update(
      @RequestBody VisitDto visitDto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws JsonProcessingException, NotFoundDEException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    try {
      Locale locale = Locale.forLanguageTag(localeString);
      LocaleContextHolder.setLocale(locale);
      return handleSuccessResponse(
          visitServiceImpl.update(visitDto, locale, resourceBundleMessageSource));
    } catch (CustomDEExceptions e) {
      return handleResponse(
          "Error: Something happened.",
          e.getLocalizedMessage(),
          ResponseStatus.FAILED,
          HttpStatus.NOT_ACCEPTABLE);
    }
  }

  @PostMapping("/autopublish")
  @Operation(summary = "Autopublish Visits", description = "This api will AutoPublish the visits")
  public ResponseEntity<ResponseEntityDto<VisitPublishResponseDto>> autoPublish(
      @RequestBody @Valid List<VisitPublishDto> visitPublishDto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    return handleSuccessResponse(
        visitServiceImpl.autoPublish(visitPublishDto, locale, resourceBundleMessageSource));
  }

  @GetMapping("/visitIdAndNameList/paginated")
  @Operation(
      summary = "Get all visit id and visit names Paginated",
      description = "This api returns Paginated visit id and names for online filters.")
  public ResponseEntity<ResponseEntityDto<Page<ISelectDto>>> getVisitIdAndNameList(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        visitServiceImpl.getVisitIdAndNameList(page, size, sortBy, lastSyncTime, sorting));
  }
}
