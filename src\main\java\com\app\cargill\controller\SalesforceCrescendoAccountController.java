/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.sf.crescendo.model.AccountCrescendo;
import com.app.cargill.sf.crescendo.service.CrescendoAccountService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/account")
@Tag(
    name = "Salesforce Crescendo Account",
    description = "Controller related to actions over Account objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoAccountController {

  private final CrescendoAccountService accountService;

  @GetMapping
  @Operation(
      summary = "Get list of accounts in Crescendo format",
      description =
          "Get list of accounts in Crescendo format modified since the provided timestamp")
  public List<AccountCrescendo> getAccounts(@RequestParam(required = false) Instant from) {
    log.debug("CRESCENDO_ACCOUNTS_GET_REQUEST {}", StringEscapeUtils.escapeJava(from.toString()));
    List<AccountCrescendo> result = accountService.getAccountsForSync(from);
    log.debug("CRESCENDO_ACCOUNTS_GET_RESULT {}", result);
    return result;
  }

  @PostMapping
  @Operation(
      summary = "Update accounts from Crescendo",
      description = "Update accounts from Crescendo")
  public ResponseEntity<Void> upsertAccounts(@RequestBody List<AccountCrescendo> accounts) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.registerModule(new JavaTimeModule());
      objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
      String requestBody = objectMapper.writeValueAsString(accounts);
      log.debug("CRESCENDO_ACCOUNTS_POST_REQUEST {}", StringEscapeUtils.escapeJava(requestBody));
    } catch (JsonProcessingException ex) {
      log.error("CANNOT_PROCESS_CRESCENDO_ACCOUNTS_JSON", ex);
    }
    accountService.updateAccounts(accounts);
    return ResponseEntity.accepted().build();
  }
}
