/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkSoldMilkProcessorToolItem implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("PickUpIndex")
  private Integer pickUpIndex;

  @JsonProperty("MilkSold")
  private Double milkSold;

  @JsonProperty("AnimalsInTank")
  private Integer animalsInTank;

  @JsonProperty("DaysInTank")
  private Integer daysInTank;

  @JsonProperty("MilkFatPer")
  private Double milkFatPer;

  @JsonProperty("MilkProteinPer")
  private Double milkProteinPer;

  @JsonProperty("MUN")
  private Double mun;

  @JsonProperty("SomaticCellCount")
  private Integer somaticCellCount;

  @JsonProperty("BacteriaCellCount")
  private Double bacteriaCellCount;

  @JsonProperty("NonFatSolid")
  private Double nonFatSolid;

  @JsonProperty("Mastitis")
  private Double mastitis;
}
