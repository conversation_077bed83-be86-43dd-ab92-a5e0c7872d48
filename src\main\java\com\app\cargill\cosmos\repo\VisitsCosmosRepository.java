/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.VisitCosmos;
import com.azure.spring.data.cosmos.repository.Query;
import com.azure.spring.data.cosmos.repository.ReactiveCosmosRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface VisitsCosmosRepository extends ReactiveCosmosRepository<VisitCosmos, String> {

  @Query("Select * from c where c.RumenHealthManureScore !=null")
  Flux<VisitCosmos> findByRumenHealthManureScore();

  @Query("Select * from c where c.CustomerId = @accountId")
  Flux<VisitCosmos> findByCustomerId(@Param("accountId") String accountId);

  @Query("Select * from c")
  Flux<VisitCosmos> findAll();
}
