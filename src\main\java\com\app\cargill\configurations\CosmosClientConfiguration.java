/* Cargill Inc.(C) 2022 */
package com.app.cargill.configurations;

import com.app.cargill.confproperties.CosmosProperties;
import com.azure.cosmos.CosmosClientBuilder;
import com.azure.cosmos.GatewayConnectionConfig;
import com.azure.spring.data.cosmos.config.AbstractCosmosConfiguration;
import com.azure.spring.data.cosmos.config.CosmosConfig;
import com.azure.spring.data.cosmos.repository.config.EnableReactiveCosmosRepositories;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@EnableConfigurationProperties(CosmosProperties.class)
@EnableReactiveCosmosRepositories(basePackages = {"com.app.cargill.cosmos"})
@Slf4j
@RequiredArgsConstructor
@Profile({"!mock-cosmos"})
public class CosmosClientConfiguration extends AbstractCosmosConfiguration {

  private final CosmosProperties properties;

  @Bean
  public CosmosClientBuilder cosmosClientBuilder() {
    GatewayConnectionConfig gatewayConnectionConfig = GatewayConnectionConfig.getDefaultConfig();
    return new CosmosClientBuilder()
        .endpoint(properties.getUri())
        .key(properties.getKey())
        .gatewayMode(gatewayConnectionConfig);
  }

  @Override
  @Bean
  public CosmosConfig cosmosConfig() {
    return CosmosConfig.builder().enableQueryMetrics(properties.isQueryMetricsEnabled()).build();
  }

  @Override
  protected String getDatabaseName() {
    return properties.getDatabase();
  }
}
