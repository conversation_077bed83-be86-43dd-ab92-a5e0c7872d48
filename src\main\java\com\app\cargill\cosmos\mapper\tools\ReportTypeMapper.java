/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.constants.VisitReportType;
import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.ReportTypeCosmos;
import com.app.cargill.document.ReportType;
import java.util.UUID;

public class ReportTypeMapper {

  private ReportTypeMapper() {}

  public static ReportType map(ReportTypeCosmos input) {

    CosmosToModelMapper<ReportTypeCosmos, ReportType> mapper =
        source ->
            ReportType.builder()
                .mediaId(source.getMediaId() != null ? UUID.fromString(source.getMediaId()) : null)
                .accountId(
                    source.getAccountId() != null ? UUID.fromString(source.getAccountId()) : null)
                .type(VisitReportType.fromId(source.getType()))
                .build();

    return mapper.map(input);
  }
}
