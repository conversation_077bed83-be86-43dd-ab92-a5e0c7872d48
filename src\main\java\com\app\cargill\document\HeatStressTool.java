/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class HeatStressTool extends EditableDocumentBase {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("VisitId")
  public UUID visitId;

  @JsonProperty("AvgMilkWeightInkg")
  public Double avgMilkWeightInkg;

  @JsonProperty("AvgDMIWeightInkg")
  public Double avgDMIWeightInkg;

  @JsonProperty("AvgNELWeightInkg")
  public Double avgNELWeightInkg;

  @JsonProperty("AvgMilkFatPercent")
  public Double avgMilkFatPercent;

  @JsonProperty("AvgMilkProteinPercent")
  public Double avgMilkProteinPercent;

  @JsonProperty("TemperatureInCelsius")
  public Double temperatureInCelsius;

  @JsonProperty("HumidityPercent")
  public Double humidityPercent;

  @JsonProperty("HoursExposedToSun")
  public Integer hoursExposedToSun;

  @JsonProperty("TemperatureHumidityInCelsius")
  public Double temperatureHumidityInCelsius;

  @JsonProperty("IntakeAdjustmentPercent")
  public Double intakeAdjustmentPercent;

  @JsonProperty("DMIReductionPercent")
  public Double dmiReductionPercent;

  @JsonProperty("EstimatedDryMatterIntakeWeightInkg")
  public Double estimatedDryMatterIntakeWeightInkg;

  @JsonProperty("ReductionInDMIWeightInkg")
  public Double reductionInDMIWeightInkg;

  @JsonProperty("LossOfEnergyConsumedInMcal")
  public Double lossOfEnergyConsumedInMcal;

  @JsonProperty("EnergyEquivalentMilkLossWeightInkg")
  public Double energyEquivalentMilkLossWeightInkg;

  // Added the below fields for HeatStressTool Changes
  @JsonProperty("AvgLactatingAnimals")
  public Double avgLactatingAnimals;

  @JsonProperty("AvgCurrentMilkPrice")
  public Double avgCurrentMilkPrice;

  @JsonProperty("milkValueLossPerDay")
  public Double milkValueLossPerDay;
}
