/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkSoldEvaluation implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("VisitMilkEvaluationData")
  private VisitMilkEvaluationData visitMilkEvaluationData;

  private UUID id;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private Boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private LastModifiedTimeUtc lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private Boolean isNew;
}
