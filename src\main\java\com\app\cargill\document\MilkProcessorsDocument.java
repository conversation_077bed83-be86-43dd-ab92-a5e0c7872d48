/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.cosmos.model.MilkProcessorDataCosmos;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class MilkProcessorsDocument extends EditableDocumentBase implements Serializable {

  @JsonProperty("UserId")
  private String userId;

  @JsonProperty("ComponentProcessors")
  private List<MilkProcessorDataCosmos> componentProcessors;

  @JsonProperty("ConcentrationProcessors")
  private List<MilkProcessorDataCosmos> concentrationProcessors;
}
