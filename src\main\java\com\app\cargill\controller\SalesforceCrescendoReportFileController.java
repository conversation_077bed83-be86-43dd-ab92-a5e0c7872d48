/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.sf.crescendo.model.ReportFileCrescendo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/report-file")
@Tag(
    name = "Salesforce Crescendo Report File",
    description = "Controller related to actions over ReportFile objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoReportFileController {
  @GetMapping
  @Operation(
      summary = "Get list of report files in Crescendo format",
      description =
          "Get list of report files in Crescendo format modified since the provided timestamp")
  public List<ReportFileCrescendo> getReportFiles(@RequestParam(required = false) Instant from) {
    return new ArrayList<>();
  }
}
