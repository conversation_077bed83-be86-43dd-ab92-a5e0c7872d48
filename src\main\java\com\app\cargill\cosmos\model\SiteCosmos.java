/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.MilkingSystem;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.converter.MilkingSystemDeserializer;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.DateEpoch;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import org.springframework.data.annotation.Id;

@Container(containerName = "Sites")
@Getter
public class SiteCosmos {

  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("ExternalAccountId")
  private String externalAccountId;

  @JsonProperty("SiteName")
  private String siteName;

  @JsonProperty("CurrentMilkPrice")
  private Double currentMilkPrice;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("MilkingSystemType")
  @JsonDeserialize(using = MilkingSystemDeserializer.class)
  private MilkingSystem milkingSystemType;

  @JsonProperty("DryMatterIntake")
  private Double dryMatterIntake;

  @JsonProperty("LactatingAnimal")
  private Integer lactatingAnimal;

  @JsonProperty("Milk")
  private Double milk;

  @JsonProperty("MilkFatPercent")
  private Double milkFatPercent;

  @JsonProperty("MilkProteinPercent")
  private Double milkProteinPercent;

  @JsonProperty("MilkOtherSolidsPercent")
  private Double milkOtherSolidsPercent;

  @JsonProperty("MilkSomaticCellCount")
  private Integer milkSomaticCellCount;

  @JsonProperty("BacteriaCellCount")
  private Integer bacteriaCellCount;

  @JsonProperty("NetEnergyOfLactationDairy")
  private Double netEnergyOfLactationDairy;

  @JsonProperty("RationCost")
  private Double rationCost;

  @JsonProperty("Barns")
  private List<BarnCosmos> barns = new ArrayList<>();

  @JsonProperty("Visits")
  private List<SiteVisitCosmos> visits = new ArrayList<>();

  @JsonProperty("AsFedIntake")
  private Double asFedIntake;

  @JsonProperty("SiteMappings")
  private List<DataSourceMapping> dataSourceMappings = new ArrayList<>();

  @JsonProperty("Origination")
  private String origination;

  @JsonProperty("DDWLastUpdatedDate")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant dDWLastUpdatedDate;

  @JsonProperty("NumberOfParlorStalls")
  private Integer numberOfParlorStalls;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private boolean isNew;
}
