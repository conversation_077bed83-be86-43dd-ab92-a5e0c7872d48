/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class VisitWalkThroughReportsCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Pens")
  private List<WalkThroughReportCosmos> pens;

  @JsonProperty("FinalObservations")
  private WalkThroughHerdFinalObservationsCosmos finalObservations;
}
