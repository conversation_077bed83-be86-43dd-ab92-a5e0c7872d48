/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.CalfHeiferScorecardSection;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class CalfHeiferScorecardCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Sections")
  private List<CalfHeiferScorecardSection> sections;
}
