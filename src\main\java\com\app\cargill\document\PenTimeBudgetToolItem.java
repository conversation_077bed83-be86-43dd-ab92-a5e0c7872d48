/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class PenTimeBudgetToolItem extends EditableToolPenEntityBase {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("VisitForComparison")
  private UUID visitForComparison;

  @JsonProperty("SelectedRestingVisits")
  private List<UUID> selectedRestingVisits;

  @JsonProperty("SelectedMilkVisits")
  private List<UUID> selectedMilkVisits;

  @JsonProperty("StallsInPen")
  private Integer stallsInPen;

  @JsonProperty("WalkingTimeToParlor")
  private Double walkingTimeToParlor;

  @JsonProperty("TimeInParlor")
  private Double timeInParlor;

  @JsonProperty("WalkingTimeFromParlor")
  private Double walkingTimeFromParlor;

  @JsonProperty("StallsInParlor")
  private Double stallsInParlor;

  @JsonProperty("TimeInLockUp")
  private Double timeInLockUp;

  @JsonProperty("OtherNonRestTime")
  private Double otherNonRestTime;

  @JsonProperty("RestingRequirement")
  private Double restingRequirement;

  @JsonProperty("EatingTime")
  private Double eatingTime;

  @JsonProperty("DrinkingGroomingTime")
  private Double drinkingGroomingTime;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("Animals")
  private Double animals;

  @JsonProperty("MilkingFrequency")
  public Double milkingFrequency;

  @JsonProperty("RestingDifference")
  public Double restingDifference;

  @JsonProperty("PotentialMilkGainLoss")
  public Double potentialMilkGainLoss;

  @JsonProperty("TimePerMilking")
  public Double timePerMilking;

  @JsonProperty("Overcrowding")
  public Double overcrowding;

  @JsonProperty("TotalTimeMilking")
  public Double totalTimeMilking;

  @JsonProperty("WalkingTimeToStall")
  public Double walkingTimeToStall;

  @JsonProperty("RestingRemaining")
  public Double restingRemaining;

  @JsonProperty("ParlorTurnsPerHour")
  public Double parlorTurnsPerHour;

  @JsonProperty("CowsMilkedPerHour")
  public Double cowsMilkedPerHour;

  @JsonProperty("WalkingToFindStall")
  public Double walkingToFindStall;

  @JsonProperty("TotalNonRestingTime")
  public Double totalNonRestingTime;

  @JsonProperty("TimeRemainingForResting")
  public Double timeRemainingForResting;

  @JsonProperty("TimeRequiredForResting")
  public Double timeRequiredForResting;

  @JsonProperty("EnergyChange")
  public Double energyChange;

  @JsonProperty("BodyWeightChange")
  public Double bodyWeightChange;

  @JsonProperty("BodyConditionScoreChange")
  public Double bodyConditionScoreChange;
}
