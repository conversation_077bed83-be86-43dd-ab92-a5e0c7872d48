/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder(toBuilder = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class ManureScreenerToolItem extends EditableToolPenEntityBase {
  @JsonProperty("VisitsSelected")
  private List<UUID> visitsSelected;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("MSTScoreId")
  private UUID mstScoreId;

  @JsonProperty("TopScaleAmountInGrams")
  private Double topScaleAmountInGrams;

  @JsonProperty("TopGoalMinimumPercent")
  private Double topGoalMinimumPercent;

  @JsonProperty("TopGoalMaximumPercent")
  private Double topGoalMaximumPercent;

  @JsonProperty("MidScaleAmountInGrams")
  private Double midScaleAmountInGrams;

  @JsonProperty("MidGoalMinimumPercent")
  private Double midGoalMinimumPercent;

  @JsonProperty("MidGoalMaximumPercent")
  private Double midGoalMaximumPercent;

  @JsonProperty("BottomScaleAmountInGrams")
  private Double bottomScaleAmountInGrams;

  @JsonProperty("BottomGoalMinimumPercent")
  private Double bottomGoalMinimumPercent;

  @JsonProperty("BottomGoalMaximumPercent")
  private Double bottomGoalMaximumPercent;

  @JsonProperty("MSTScoreName")
  private String mstScoreName;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;

  @JsonProperty("Observation")
  public String observation;

  @JsonProperty("TotalScaleAmount")
  public Double totalScaleAmount;

  @JsonProperty("TopScalePercentage")
  public Double topScalePercentage;

  @JsonProperty("MidScalePercentage")
  public Double midScalePercentage;

  @JsonProperty("BottomScalePercentage")
  public Double bottomScalePercentage;
}
