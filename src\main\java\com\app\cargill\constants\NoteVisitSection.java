/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings({"java:S115"})
public enum NoteVisitSection {
  // Comfort region
  ComfortTools,
  // Heat Stress Evaluation
  HeatstressChart,
  HeatstressData,
  HeatstressCalculations,
  // Pen Time
  PenTimePenSelection,
  PenTimeInputs,
  PenTimeComparison,
  PenTimeResults,

  // Health Tools
  HealthTools,
  // Rumen Health Cud Chewing
  RumenHealthCudChewingLanding,
  CudChewing,
  CudCalculators,
  CudChewingMaster,
  CudChewingDataEntry,
  CudChewingResults,
  RumenHealthNumberOfChewsInput,
  RumenHealthNumberOfChewsResults,
  RumenHealthCudChewHerdHerdAnalyasis,
  RumenHealthCudChewHerdGoals,
  // Rumen Health TMR Particle Score
  ParticleScoreLanding,
  ParticleScoreSelectPen,
  ParticleScoreSelectScorer,
  ParticleScorePenScorerInputs,
  ParticleScorePenScorerResults,
  RumenHealthTMRPenScorerInputs,
  RumenHealthTMRPenScorerResults,
  ParticleScoreHerdAnalysisInputs,
  ParticleScoreHerdAnalysisResults,
  ParticleScoreHerdAnalysisEdit,
  // Rumen Health Manure Score
  ManureScoreLanding,
  ManureScoresChart,
  ManureScoresResults,
  ManurePenSelection,
  ManureEditScores,
  ManureScoreHerdAnalysisInputs,
  ManureScoreHerdAnalysisGoals,
  ManureScoreHerdAnalysisResults,
  // Locomotion Score
  LocomotionLanding,
  LocomotionPenSelection,
  LocomotionPenInputs,
  LocomotionPenResults,
  LocomotionEditTable,
  LocomotionHerdInputs,
  LocomotionHerdRevenue,
  LocomotionHerdResults,
  // BodyCOndition Score
  BodyConditionLanding,
  BodyConditionInputs,
  BodyConditionResults,
  BodyConditionEditTable,
  BodyConditionHerdInputs,
  BodyConditionHerdGoals,
  BodyConditionHerdResults,
  BCSHerdAnalysisInputs,
  BCSHerdAnalysisGoals,
  BCSHerdAnalysisResults,
  BCSSelectPointScale,
  BodyConditionPenSelection,
  // Metabolic Incidence
  MetabolicIncidenceInputs,
  MetabolicIncidenceOutputs,
  MetabolicIncidenceCharts,

  // Nutrition
  NutritionTools,
  // Forage Audit
  ForageAudit,
  ForageAuditScorecard,
  ForageAuditSurveyOfForages,
  ForageAuditBaleage,
  ForageAuditBunkersPiles,
  ForageAuditHarvest,
  ForageAuditMaintainingQuality,
  ForageAuditSilageBags,
  ForageAuditTowerSilos,
  // Pile And Bunker Capacity
  PileAndBunkerCapacity,
  PileCapacity,
  PileFeedOutRate,
  BunkerCapacity,
  BunkerFeedOutRate,

  // Productivity
  ProductivityLanding,
  // Milking Procedure Revenue Calculator
  MilkingProcessRevenueInputs,
  MilkingProcessRevenueResults,
  MilkingProcessRevenueResources,
  // Milk Sold Evaluation
  MilkSoldEvaluationInputs,
  MilkSoldEvaluationOutputs,
  MilkSoldEvaluationCharts,
  MilkSoldEvaluationEditPickup,
  // Robotic Milk Ealuation
  RoboticMilkEvaluationInputs,
  RoboticMilkEvaluationOutputs,
  RoboticMilkEvaluationAnalysis,
  RoboticMilkEvaluationTrend,
  RoboticMilkEvaluationCharts,

  // Urine pH
  UrinePHPenSelection,
  UrinePHInputs,
  UrinePHResults,
  UrinePHEditGoals,

  Visit,

  RevenueCalculator,
  RumenHealthLanding,
  EditDatesForComparison,
  HerdAnalysis,
  HerdAnalysisGoals,
  HerdGoals,
  HerdReporting,
  VisitSummary,
  WalkthroughReportLanding,
  WalkthroughReport,
  WalkthroughReportPen,
  ReadyToMilkInputs,
  ReadyToMilkOutputs,
  ReadyToMilkCharts,

  // Calf & heifer
  CalfHeiferTools,
  CalfHeiferScorecardLanding,
  CalfHeiferColostrum,
  CalfHeiferPreweaned,
  CalfHeiferPostweaned,
  CalfHeiferGrowerPuberty,
  CalfHeiferKeyBenchmarks,
  CalfHeiferResources,
  CalfHeiferResults,
  CalfHeiferResponse,

  // Rumen Health Manure Screener
  ManureScreenerSelectPen,
  ManureScreenerPenInputs,
  ManureScreenerPenResults,
  ManureScreenerPenEditScore,
  ManureScreenerPenEditGoals,

  // Rumen Fill Manure
  RumenFillLanding,
  RumenFillInputs,
  RumenFillResults,
  RumenFillPenSelection,
  RumenFillScoresChart,
  RumenFillScoresResults,
  RumenFillScoreHerdAnalysisInputs,
  RumenFillScoreHerdAnalysisGoals,
  RumenFillScoreHerdAnalysisResults;

  public static String findNameByIndex(Integer sectionIndex) {
    if (sectionIndex != null
        && sectionIndex >= 0
        && sectionIndex < NoteVisitSection.values().length) {
      return NoteVisitSection.values()[sectionIndex].name();
    }
    return null;
  }
}
