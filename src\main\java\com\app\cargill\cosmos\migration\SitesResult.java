/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.model.Pens;
import com.app.cargill.model.Sites;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * This is a very specific class used for getting both Sites and Pens when fetching them from the
 * CosmosDB for the purpose of migrating them into the new app database
 */
@NoArgsConstructor
@Getter
@Setter
public class SitesResult {
  private List<Sites> sites = new ArrayList<>();
  private List<Pens> pens = new ArrayList<>();
}
