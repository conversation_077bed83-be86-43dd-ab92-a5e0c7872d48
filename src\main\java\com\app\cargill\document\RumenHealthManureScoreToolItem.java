/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class RumenHealthManureScoreToolItem extends EditableToolPenEntityBase
    implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("ManureScoreVisitsSelected")
  private List<UUID> manureScoreVisitsSelected;

  @JsonProperty("ManureScores")
  private RumenHealthManureScores manureScores;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;
}
