/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.BeddingQuality;
import com.app.cargill.constants.ProfitabilityAnalysisQuality;
import com.app.cargill.constants.WaterQuality;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeedingInformation implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("CommercialConcentrate")
  private Double commercialConcentrate;

  @JsonProperty("CommercialConcentrateToggle")
  private Boolean commercialConcentrateToggle;

  @JsonProperty("MineralBaseMix")
  private Boolean mineralBaseMix;

  @JsonProperty("MineralBaseMixValue")
  private Double mineralBaseMixValue;

  @JsonProperty("Nutritek")
  private Boolean nutritek;

  @JsonProperty("XpcUltra")
  private Boolean xpcUltra;

  @JsonProperty("ActiforBoost")
  private Boolean actiforBoost;

  @JsonProperty("Buffer")
  private Boolean buffer;

  @JsonProperty("NutrigorduraLac")
  private Boolean nutrigorduraLac;

  @JsonProperty("Ice")
  private Boolean ice;

  @JsonProperty("EnergyIce")
  private Boolean energyIce;

  @JsonProperty("Monensin")
  private Boolean monensin;

  @JsonProperty("SoyPassBr")
  private Boolean soyPassBr;

  @JsonProperty("ConcentrateTotalConsumed")
  private Double concentrateTotalConsumed;

  @JsonProperty("Silage")
  private ProfitabilityAnalysisQuality silage;

  @JsonProperty("Haylage")
  private ProfitabilityAnalysisQuality haylage;

  @JsonProperty("Hay")
  private ProfitabilityAnalysisQuality hay;

  @JsonProperty("Pasture")
  private ProfitabilityAnalysisQuality pasture;

  @JsonProperty("WaterQuality")
  private WaterQuality waterQuality;

  @JsonProperty("BeddingQuality")
  private BeddingQuality beddingQuality;

  @JsonProperty("Ventilation")
  private Boolean ventilation;

  @JsonProperty("Sprinkler")
  private Boolean sprinkler;

  @JsonProperty("TemperatureInC")
  private Double temperatureInC;

  @JsonProperty("AirRuPercentage")
  private Double airRuPercentage;

  @JsonProperty("THI")
  private Double THI;

  @JsonProperty("RespiratoryMovement")
  private Double respiratoryMovement;

  @JsonProperty("CowLayingDownPercentage")
  private Double cowLayingDownPercentage;

  @JsonProperty("TotalDietCost")
  private Double totalDietCost;

  @JsonProperty("RevenuePerCowPerDay")
  private Double revenuePerCowPerDay;
}
