/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum AccountType {

  // [EnumMember(Value = "Standard")]
  Prospect(0),

  // [EnumMember(Value = "Commercial Customer")]
  Customer(1),

  // [EnumMember(Value = "Third Party")]
  Thirdparty(2),

  // [EnumMember(Value = "Consumer")]
  Consumer(3),

  // [EnumMember(Value = "Competitor")]
  Competitor(4);

  private final Integer accountTypeValue;

  AccountType(Integer code) {
    this.accountTypeValue = code;
  }

  public Integer getAccountTypeValue() {
    return accountTypeValue;
  }

  public static AccountType fromId(int id) {
    for (AccountType type : values()) {
      if (type.getAccountTypeValue() == id) {
        return type;
      }
    }
    return null;
  }
}
