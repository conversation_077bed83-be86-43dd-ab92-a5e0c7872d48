/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.ContentDetailsCosmos;
import com.azure.spring.data.cosmos.repository.Query;
import com.azure.spring.data.cosmos.repository.ReactiveCosmosRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface ContentDetailsCosmosRepository
    extends ReactiveCosmosRepository<ContentDetailsCosmos, String> {
  @Query("Select * from c where c.AccountId = @accountId")
  Flux<ContentDetailsCosmos> findAllByAccountId(@Param("accountId") String accountId);
}
