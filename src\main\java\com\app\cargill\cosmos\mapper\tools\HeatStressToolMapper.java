/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.HeatStressToolCosmos;
import com.app.cargill.document.HeatStressTool;
import java.util.UUID;

public class HeatStressToolMapper {

  private HeatStressToolMapper() {}

  public static HeatStressTool map(HeatStressToolCosmos input) {

    CosmosToModelMapper<HeatStressToolCosmos, HeatStressTool> mapper =
        source ->
            HeatStressTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .avgMilkWeightInkg(source.getAvgMilkWeightInkg())
                .avgDMIWeightInkg(source.getAvgDMIWeightInkg())
                .avgNELWeightInkg(source.getAvgNELWeightInkg())
                .avgMilkFatPercent(source.getAvgMilkFatPercent())
                .avgMilkProteinPercent(source.getAvgMilkProteinPercent())
                .temperatureInCelsius(source.getTemperatureInCelsius())
                .humidityPercent(source.getHumidityPercent())
                .hoursExposedToSun(source.getHoursExposedToSun())
                .temperatureHumidityInCelsius(source.getTemperatureHumidityInCelsius())
                .intakeAdjustmentPercent(source.getIntakeAdjustmentPercent())
                .dmiReductionPercent(source.getDmiReductionPercent())
                .estimatedDryMatterIntakeWeightInkg(source.getEstimatedDryMatterIntakeWeightInkg())
                .reductionInDMIWeightInkg(source.getReductionInDMIWeightInkg())
                .lossOfEnergyConsumedInMcal(source.getLossOfEnergyConsumedInMcal())
                .energyEquivalentMilkLossWeightInkg(source.getEnergyEquivalentMilkLossWeightInkg())
                .avgLactatingAnimals(source.getAvgLactatingAnimals())
                .avgCurrentMilkPrice(source.getAvgCurrentMilkPrice())
                .build();

    return mapper.map(input);
  }
}
