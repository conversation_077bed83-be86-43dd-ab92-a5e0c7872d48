/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.SilageDto;
import com.app.cargill.service.ISilageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/silage")
@Tag(name = "Silage Information Controller", description = "Silage Information Controller")
@RequiredArgsConstructor
public class SilageController extends BaseController {

  private final ISilageService silageServiceImpl;

  @GetMapping("/paginated")
  @Operation(
      summary = "Get all Silage based on current logged in User",
      description = "This method will return all Ear Tags based on current Logged in user")
  public ResponseEntity<ResponseEntityDto<Page<SilageDto>>> getAllSilagesPaginated(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", name = "sortBy") String sortBy,
      @RequestParam(
              name = "lastSyncTime",
              defaultValue = "${app.configurations.default-utc-timestamp}")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        silageServiceImpl.getAllSilagesPaginated(page, size, sortBy, sorting, lastSyncTime));
  }

  @PostMapping
  @Operation(
      summary = "Save Silage",
      description = "This api will save Silage WRT to CustomerId and SiteId")
  public ResponseEntity<ResponseEntityDto<List<SilageDto>>> save(@RequestBody SilageDto silageDto) {
    return handleSuccessResponse(silageServiceImpl.save(silageDto));
  }

  @PutMapping
  @Operation(
      summary = "Update Silage",
      description = "This api will Update Silage WRT to Silage ID")
  public ResponseEntity<ResponseEntityDto<List<SilageDto>>> update(
      @RequestBody SilageDto silageDto) {
    return handleSuccessResponse(silageServiceImpl.update(silageDto));
  }
}
