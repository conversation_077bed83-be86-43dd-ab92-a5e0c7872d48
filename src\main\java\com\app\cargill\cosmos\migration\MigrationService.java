/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** This Service is responsible for handling migrations of all data starting from user email */
@Service
@RequiredArgsConstructor
@Slf4j
public class MigrationService {

  private final VisitsMigration visitsMigration;

  public CompletableFuture<MigrationResult> fixAllPensTmrPenId() {
    return visitsMigration.fixAllPensTmrPenId();
  }
}
