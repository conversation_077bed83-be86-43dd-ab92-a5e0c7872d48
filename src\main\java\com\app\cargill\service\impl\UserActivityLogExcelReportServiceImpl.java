/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.dto.UserActivityLogExportDto;
import com.app.cargill.dto.UserActivityLogResponseDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;

@Service("userActivityLogExcelReportServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class UserActivityLogExcelReportServiceImpl implements IExcelReportService {

    private final ModelMapper modelMapper;
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public String getFileName(Object data) {
        UserActivityLogExportDto dto = modelMapper.map(data, UserActivityLogExportDto.class);
        return dto.getFileName() != null ? dto.getFileName() : "UserJourney_Analytics_Export.xlsx";
    }

    @Override
    public ByteArrayResource prepareExportToExcel(Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
        log.debug("Preparing User Journey Analytics Excel export");

        UserActivityLogExportDto dto = modelMapper.map(data, UserActivityLogExportDto.class);

        try (XSSFWorkbook wb = new XSSFWorkbook()) {
            // Create main sheet
            XSSFSheet sheet = wb.createSheet("User Journey Analytics");

            // Create cell styles
            XSSFCellStyle headerStyle = ExcelUtils.applyCellStyle(
                    wb, IndexedColors.DARK_BLUE, FillPatternType.SOLID_FOREGROUND,
                    HorizontalAlignment.CENTER,
                    ExcelUtils.getFont(wb, false, true, IndexedColors.WHITE));

            XSSFCellStyle centerStyle = ExcelUtils.applyCellStyle(
                    wb, null, null,
                    HorizontalAlignment.CENTER,
                    ExcelUtils.getFont(wb, false, false, IndexedColors.BLACK));

            XSSFCellStyle leftStyle = ExcelUtils.applyCellStyle(
                    wb, null, null,
                    HorizontalAlignment.LEFT,
                    ExcelUtils.getFont(wb, false, false, IndexedColors.BLACK));

            XSSFCellStyle dateStyle = ExcelUtils.applyCellStyle(
                    wb, null, null,
                    HorizontalAlignment.CENTER,
                    ExcelUtils.getFont(wb, false, false, IndexedColors.BLACK));

            AtomicInteger rowNumber = new AtomicInteger(0);

            // Add export metadata
            addExportMetadata(sheet, dto, headerStyle, centerStyle, rowNumber);

            // Add empty row
            rowNumber.incrementAndGet();

            // Add headers
            addHeaders(sheet, headerStyle, rowNumber);

            // Add data rows
            addDataRows(sheet, dto.getActivityLogs(), centerStyle, leftStyle, dateStyle, rowNumber);

            // Auto-size columns (only 4 columns now)
            for (int i = 0; i < 4; i++) {
                sheet.autoSizeColumn(i);
            }

            return ExcelUtils.finalizeWorkbook(wb, 4);

        } catch (IOException e) {
            log.error("Error creating User Journey Analytics Excel export: {}", e.getMessage(), e);
            throw new IOException("Failed to create Excel export: " + e.getMessage());
        }
    }

    @Override
    public ByteArrayResource prepareExportToImage(Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException, URISyntaxException {
        // Not implemented for this report type
        throw new UnsupportedOperationException("Image export not supported for User Journey Analytics");
    }

    private void addExportMetadata(XSSFSheet sheet, UserActivityLogExportDto dto, XSSFCellStyle headerStyle, XSSFCellStyle centerStyle, AtomicInteger rowNumber) {
        // Export Information Header
        XSSFRow titleRow = sheet.createRow(rowNumber.getAndIncrement());
        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("User Journey Analytics Export");
        titleCell.setCellStyle(headerStyle);

        // Export Date
        XSSFRow exportDateRow = sheet.createRow(rowNumber.getAndIncrement());
        exportDateRow.createCell(0).setCellValue("Export Date:");
        XSSFCell exportDateCell = exportDateRow.createCell(1);
        exportDateCell.setCellValue(dto.getExportDate().toString().replace("T", " ").substring(0, 19));
        exportDateCell.setCellStyle(centerStyle);

        // Date Range
        XSSFRow dateRangeRow = sheet.createRow(rowNumber.getAndIncrement());
        dateRangeRow.createCell(0).setCellValue("Date Range:");
        XSSFCell dateRangeCell = dateRangeRow.createCell(1);
        dateRangeCell.setCellValue(dto.getDateFrom().toString().substring(0, 10) + " to " + dto.getDateTo().toString().substring(0, 10));
        dateRangeCell.setCellStyle(centerStyle);

        // Total Records
        XSSFRow totalRecordsRow = sheet.createRow(rowNumber.getAndIncrement());
        totalRecordsRow.createCell(0).setCellValue("Total Records:");
        XSSFCell totalRecordsCell = totalRecordsRow.createCell(1);
        totalRecordsCell.setCellValue(dto.getTotalRecords());
        totalRecordsCell.setCellStyle(centerStyle);

        // Filter Criteria
        XSSFRow filterRow = sheet.createRow(rowNumber.getAndIncrement());
        filterRow.createCell(0).setCellValue("Filter Criteria:");
        XSSFCell filterCell = filterRow.createCell(1);
        filterCell.setCellValue(dto.getFilterCriteria());
        filterCell.setCellStyle(centerStyle);
    }

    private void addHeaders(XSSFSheet sheet, XSSFCellStyle headerStyle, AtomicInteger rowNumber) {
        XSSFRow headerRow = sheet.createRow(rowNumber.getAndIncrement());

        // Only 4 required fields for analytics
        String[] headers = {
            "Event Name",
            "Path",
            "Username",
            "Last Visited"
        };

        for (int i = 0; i < headers.length; i++) {
            XSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private void addDataRows(XSSFSheet sheet, List<UserActivityLogResponseDto> activityLogs,
                           XSSFCellStyle centerStyle, XSSFCellStyle leftStyle, XSSFCellStyle dateStyle,
                           AtomicInteger rowNumber) {

        for (UserActivityLogResponseDto log : activityLogs) {
            XSSFRow dataRow = sheet.createRow(rowNumber.getAndIncrement());
            AtomicInteger cellNumber = new AtomicInteger(0);

            // Only 4 required fields for analytics

            // Event Name
            ExcelUtils.createAndSetCellValue(dataRow, cellNumber, leftStyle, log.getEventName());

            // Path
            ExcelUtils.createAndSetCellValue(dataRow, cellNumber, leftStyle, log.getPath());

            // Username
            ExcelUtils.createAndSetCellValue(dataRow, cellNumber, leftStyle, log.getUsername());

            // Last Visited
            String lastVisited = log.getLastVisited() != null ?
                log.getLastVisited().toString().replace("T", " ").substring(0, 19) : "";
            ExcelUtils.createAndSetCellValue(dataRow, cellNumber, dateStyle, lastVisited);
        }
    }
}
