/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum UnitOfMeasureKeys {
  Metric(0),
  Imperial(1);

  private final Integer value;

  UnitOfMeasureKeys(Integer value) {
    this.value = value;
  }

  public Integer getValue() {
    return value;
  }

  public static UnitOfMeasureKeys fromId(int unitOfMeasure) {
    for (UnitOfMeasureKeys item : values()) {
      if (item.getValue() == unitOfMeasure) {
        return item;
      }
    }
    return null;
  }
}
