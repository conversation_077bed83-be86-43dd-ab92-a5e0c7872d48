/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScorecardAnswer implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// This is the text of the answer to show the user.
  /// </summary>
  /// <value>The answer text.</value>
  /// <remarks>This will likely be a key to the string resources files.</remarks>
  @JsonProperty("AnswerText")
  private String answerText;
  /// <summary>
  /// If this answer is selected, this is how many points the answer contributes
  /// to the scorecard.
  /// </summary>
  /// <value>The point value.</value>
  @JsonProperty("PointValue")
  private Integer pointValue;

  /// <summary>
  /// Indicates where in the sequence of answers this answer will be displayed.
  /// </summary>
  /// <value>The index.</value>
  @JsonProperty("Index")
  private Integer index;
  /// <summary>
  /// Indicates whether or not a string is globalized or hard coded.
  /// </summary>
  /// <value>The globalize.</value>
  @JsonProperty("Globalize")
  private Boolean globalize;
  /// <summary>
  /// Gets or sets the is item of concern.
  /// </summary>
  /// <value>The is item of concern.</value>
  @JsonProperty("IsItemOfConcern")
  private Boolean isItemOfConcern;

  @JsonProperty("Selected")
  private Boolean selected;
}
