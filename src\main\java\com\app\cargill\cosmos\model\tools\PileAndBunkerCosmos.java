/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.FeedStorageType;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import lombok.Getter;

@Getter
public class PileAndBunkerCosmos {
  @JsonProperty("Id")
  private String id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("IsPileOrBunker")
  private FeedStorageType isPileOrBunker;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("TopWidthInMeters")
  private Double topWidthInMeters;

  @JsonProperty("BottomWidthInMeters")
  private Double bottomWidthInMeters;

  @JsonProperty("HeightInMeters")
  private Double heightInMeters;

  @JsonProperty("BottomLengthInMeters")
  private Double bottomLengthInMeters;

  @JsonProperty("TopLengthInMeters")
  private Double topLengthInMeters;

  @JsonProperty("DryMatterPercentage")
  private Double dryMatterPercentage;

  @JsonProperty("SilageDMDensityInKgPerMetersCubed")
  private Double silageDMDensityInKgPerMetersCubed;

  @JsonProperty("DryMatterOfFeedPerCowPerDay")
  private Integer dryMatterOfFeedPerCowPerDay;

  @JsonProperty("CowsToBeFed")
  private Integer cowsToBeFed;

  @JsonProperty("feedOutInclusionRate")
  private Double feedOutInclusionRate;

  @JsonProperty("TonnesOfDryMatter")
  private Double tonnesOfDryMatter;

  @JsonProperty("TonnesAsFed")
  private Double tonnesAsFed;

  @JsonProperty("FootPrintArea")
  private Double footPrintArea;

  @JsonProperty("TonnesAsFedPerMeterSquaredFootPrintArea")
  private Double tonnesAsFedPerMeterSquaredFootPrintArea;

  @JsonProperty("Slope")
  private Double slope;

  @JsonProperty("SilageAsFedDensity")
  private Integer silageAsFedDensity;

  @JsonProperty("FeedOutSurfaceAreaMetersSquared")
  private Double feedOutSurfaceAreaMetersSquared;

  @JsonProperty("CowsPerDayNeededAtLowerFeedRate")
  private Integer cowsPerDayNeededAtLowerFeedRate;

  @JsonProperty("CowsPerDayNeededAtHigherFeedRate")
  private Integer cowsPerDayNeededAtHigherFeedRate;

  @JsonProperty("KilogramsDryMatterInOneMeter")
  private Double kilogramsDryMatterInOneMeter;

  @JsonProperty("MetersPerDay")
  private Double metersPerDay;

  @JsonProperty("FilledHeightInMeters")
  private Double filledHeightInMeters;

  @JsonProperty("DiameterInMeters")
  private Double diameterInMeters;

  @JsonProperty("SilageLeftInMeters")
  private Double silageLeftInMeters;

  @JsonProperty("DryMatterPercentageSilo")
  private Double dryMatterPercentageSilo;

  @JsonProperty("LengthInMeters")
  private Double lengthInMeters;

  @JsonProperty("DiameterBagInMeters")
  private Double diameterBagInMeters;

  @JsonProperty("DryMatterPercentageBag")
  private Double dryMatterPercentageBag;

  @JsonProperty("SilageDMDensityBagKgPerMeter")
  private Double silageDMDensityBagKgPerMeter;

  @JsonProperty("SilageAsFedDensityBag")
  private Double silageAsFedDensityBag;

  @JsonProperty("StartDate")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant startDate;
}
