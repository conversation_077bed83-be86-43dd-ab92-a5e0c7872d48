/* Cargill Inc.(C) 2022 */
package com.app.cargill.authconfig;

import com.app.cargill.confproperties.AzureADProperties;
import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.dto.DiscoveryKeyDto;
import com.app.cargill.exceptions.ExceptionInstanceValidator;
import com.okta.jwt.AccessTokenVerifier;
import com.okta.jwt.JwtVerifiers;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import java.time.Duration;
import javax.net.ssl.SSLException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.util.retry.Retry;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!test")
public class AccessTokenVerifierFactory {
  @Getter @Setter private DiscoveryKeyDto azureDiscoveredKeys;
  @Getter @Setter private AccessTokenVerifier oktaAccessTokenVerifier;
  private final AzureADProperties azureADProperties;
  private final OktaProperties oktaProperties;

  @EventListener(ApplicationReadyEvent.class)
  public void getVerifier() {
    oktaAccessTokenVerifier =
        JwtVerifiers.accessTokenVerifierBuilder()
            .setIssuer(oktaProperties.getIssuer())
            .setAudience("api://CargillAuth") // defaults to 'api://default'
            .setConnectionTimeout(Duration.ofSeconds(10)) // defaults to 1s
            .setRetryMaxAttempts(3) // defaults to 3
            .setRetryMaxElapsed(Duration.ofSeconds(10)) // defaults to 10s
            .build();
  }

  @EventListener(ApplicationReadyEvent.class)
  public void getAzureDiscoveryKeys() {
    try {
      SslContext context =
          SslContextBuilder.forClient().trustManager(InsecureTrustManagerFactory.INSTANCE).build();

      HttpClient httpClient = HttpClient.create().secure(t -> t.sslContext(context));
      WebClient client =
          WebClient.builder()
              .baseUrl("https://login.microsoftonline.com/" + azureADProperties.getTenantId())
              .clientConnector(new ReactorClientHttpConnector(httpClient))
              .build();

      Mono<DiscoveryKeyDto> stringMono =
          client
              .get()
              .uri(
                  builder ->
                      builder
                          .path("/discovery/keys")
                          .queryParam("appid", azureADProperties.getClientId())
                          .build())
              .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString())
              .retrieve()
              .bodyToMono(DiscoveryKeyDto.class);
      azureDiscoveredKeys =
          stringMono
              .retryWhen(
                  Retry.fixedDelay(3, Duration.ofSeconds(5))
                      .filter(
                          e ->
                              ExceptionInstanceValidator.isUnknownHostExceptionError(e)
                                  || ExceptionInstanceValidator.is5xxServerError(e)))
              .block();
    } catch (WebClientResponseException.BadRequest | SSLException e) {
      log.error(e.getLocalizedMessage());
    }
  }
}
