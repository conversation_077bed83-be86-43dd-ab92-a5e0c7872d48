/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum Currencies {
  NotSet(0),
  Euro(1),
  ARA(2),
  AUD(3),
  BAM(4),
  BGL(5),
  BRR(6),
  CAD(7),
  CH<PERSON>(8),
  <PERSON>LF(9),
  CNY(10),
  COP(11),
  CZK(12),
  DKK(13),
  EGP(14),
  GBP(15),
  HKD(16),
  HRK(17),
  HUF(18),
  INR(19),
  JOD(20),
  LKR(21),
  MKD(22),
  MXN(23),
  NOK(24),
  PLN(25),
  ROL(26),
  RUB(27),
  SGD(28),
  UAH(29),
  USD(30),
  VND(31),
  ZAR(32),
  SEK(33),
  SKK(34),
  TRY(35),
  HNL(36),
  NIO(37),
  GTQ(38),
  <PERSON><PERSON>(39),
  VEF(40),
  P<PERSON>(41),
  <PERSON><PERSON>(42),
  ID<PERSON>(43),
  THB(44),
  MY<PERSON>(45),
  <PERSON><PERSON><PERSON>(46),
  <PERSON><PERSON>(47),
  JPY(48),
  NG<PERSON>(49),
  DZ<PERSON>(50),
  ARS(51),
  BRL(52),
  CLP(53),
  PON(54),
  SAR(55),
  SRD(56),
  KZT(57);

  private final Integer value;

  Currencies(Integer value) {
    this.value = value;
  }

  public Integer getValue() {
    return value;
  }

  public static Currencies fromId(Integer id) {
    for (Currencies c : Currencies.values()) {
      if (c.getValue().equals(id)) {
        return c;
      }
    }
    throw new IllegalArgumentException(String.format("Unknown currency id %s", id));
  }
}
