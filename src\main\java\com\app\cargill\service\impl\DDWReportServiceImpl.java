/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.amazonaws.util.StringUtils;
import com.app.cargill.confproperties.SharepointProperties;
import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.dto.Pair;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.ExceptionInstanceValidator;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.service.IDDWReportService;
import java.time.Duration;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

@Slf4j
@Service("dDWReportServiceImpl")
@RequiredArgsConstructor
public class DDWReportServiceImpl implements IDDWReportService {
  private static final String GRANT_TYPE = "grant_type";
  private static final String CLIENT_ID = "client_id";
  private static final String CLIENT_SECRET = "client_secret";
  private static final String RESOURCE = "resource";
  public static final String FILE_PATH_SPLITTER = "/";
  public static final String FILE_NOT_FOUND_MESSAGE =
      "Unable to download the file. Please contact your data steward to check mapping.";
  public static final String REPORT_NOT_AVILABLE_MESSAGE =
      "Report is not available at the moment, please try again later.";
  public static final String GETFILEBYSERVERRELATIVEURL_PATH =
      "%s/_api/web/getfilebyserverrelativeurl('%s')/$value";
  public static final int MAX_RETRY_ATTEMPTS = 5;
  public static final int MAX_RETRY_ATTEMPTS_DURATION = 2;
  private final SiteMappingsRepository siteMappingsRepository;
  private final WebClient.Builder builder;
  private final SharepointProperties sharepointProperties;

  @Override
  public Pair<String, ByteArrayResource> getLatestDetailedReport(UUID siteId)
      throws CustomDEExceptions {
    String ddwHerdId = getDDWHerdId(siteId);

    String detailedReportFilePath = sharepointProperties.getFileUrl();
    String detailedReportFileName;
    if (sharepointProperties.isDetailedFileNameWithSuffix()) {
      detailedReportFilePath =
          detailedReportFilePath + FILE_PATH_SPLITTER + ddwHerdId + "_detailed.pdf";
      detailedReportFileName = ddwHerdId + "_detailed.pdf";
    } else {
      detailedReportFilePath = detailedReportFilePath + FILE_PATH_SPLITTER + ddwHerdId + ".pdf";
      detailedReportFileName = ddwHerdId + ".pdf";
    }

    var requestUrl =
        String.format(
            GETFILEBYSERVERRELATIVEURL_PATH,
            sharepointProperties.getWebUrl(),
            detailedReportFilePath);
    Oauth2Dto oauth2Dto = fetchAccessToken();
    byte[] bytes = downloadReport(requestUrl, oauth2Dto);
    return new Pair<>(
        detailedReportFileName,
        new ByteArrayResource(bytes == null ? new byte[] {} : bytes, detailedReportFileName));
  }

  @Override
  public Pair<String, ByteArrayResource> getLatestSummaryReport(UUID siteId)
      throws CustomDEExceptions {
    String ddwHerdId = getDDWHerdId(siteId);

    String filePath = sharepointProperties.getFileUrl();
    String fileName;
    if (sharepointProperties.isSummaryFileNameWithSuffix()) {
      filePath = filePath + FILE_PATH_SPLITTER + ddwHerdId + "_summary.pdf";
      fileName = ddwHerdId + "_summary.pdf";
    } else {
      filePath = filePath + FILE_PATH_SPLITTER + ddwHerdId + ".pdf";
      fileName = ddwHerdId + ".pdf";
    }

    var requestUrl =
        String.format(GETFILEBYSERVERRELATIVEURL_PATH, sharepointProperties.getWebUrl(), filePath);
    Oauth2Dto oauth2Dto = fetchAccessToken();
    byte[] report = downloadReport(requestUrl, oauth2Dto);
    return new Pair<>(
        fileName, new ByteArrayResource(report == null ? new byte[] {} : report, fileName));
  }

  public Oauth2Dto fetchAccessToken() {
    MultiValueMap<String, String> bodyMap = new LinkedMultiValueMap<>();
    bodyMap.add(GRANT_TYPE, sharepointProperties.getGrantType());
    bodyMap.add(
        CLIENT_ID,
        sharepointProperties.getClientId().concat("@").concat(sharepointProperties.getTenantId()));
    bodyMap.add(CLIENT_SECRET, sharepointProperties.getClientSecret());
    bodyMap.add(RESOURCE, sharepointProperties.getResource());

    log.info("created body : {} for token", bodyMap);
    WebClient client = WebClient.create(sharepointProperties.getAuthority());
    Mono<Oauth2Dto> stringMono =
        client
            .post()
            .contentType(MediaType.APPLICATION_FORM_URLENCODED)
            .header("Accept", "application/json")
            .body(BodyInserters.fromFormData(bodyMap))
            .retrieve()
            .bodyToMono(Oauth2Dto.class);
    log.info("returning response from sharepoint");
    return stringMono
        .retryWhen(
            Retry.fixedDelay(MAX_RETRY_ATTEMPTS, Duration.ofSeconds(MAX_RETRY_ATTEMPTS_DURATION))
                .filter(
                    e ->
                        ExceptionInstanceValidator.isUnknownHostExceptionError(e)
                            || ExceptionInstanceValidator.is5xxServerError(e)
                            || ExceptionInstanceValidator.isIOException(e)))
        .block();
  }

  public byte[] downloadReport(String requestUrl, Oauth2Dto oauth2Dto) throws CustomDEExceptions {
    log.debug("Report Download Started, URL = " + requestUrl);

    log.debug("Report Access token Fetch Complete.");
    if (oauth2Dto == null || StringUtils.isNullOrEmpty(oauth2Dto.getAccessToken())) {
      throw new CustomDEExceptions(
          REPORT_NOT_AVILABLE_MESSAGE, HttpStatus.METHOD_NOT_ALLOWED.value());
    }
    Mono<byte[]> mono =
        builder
            .baseUrl(requestUrl)
            .build()
            .get()
            .header(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON_VALUE)
            .header(HttpHeaders.AUTHORIZATION, "Bearer ".concat(oauth2Dto.getAccessToken()))
            .exchangeToMono(
                clientResponse -> {
                  log.debug("SharePoint Response Status Code: {}", clientResponse.statusCode());
                  log.debug(
                      "SharePoint Response Headers: {}", clientResponse.headers().asHttpHeaders());
                  if (clientResponse.statusCode().isError()) {
                    log.error(
                        "SharePoint returned error status: {} for URL: {}",
                        clientResponse.statusCode(),
                        requestUrl);
                    return Mono.error(
                        new CustomDEExceptions(
                            "SharePoint error: " + clientResponse.statusCode(),
                            clientResponse.statusCode().value()));
                  } else {
                    return clientResponse.bodyToMono(byte[].class);
                  }
                })
            .onErrorResume(
                e -> {
                  log.error("Error downloading report from SharePoint: {}", e.getMessage(), e);
                  return Mono.error(
                      new CustomDEExceptions(
                          "Report download failed: " + e.getMessage(),
                          HttpStatus.INTERNAL_SERVER_ERROR.value()));
                });
    log.debug("Report Download Complete, URL = " + requestUrl);
    return mono.retryWhen(
            Retry.fixedDelay(MAX_RETRY_ATTEMPTS, Duration.ofSeconds(MAX_RETRY_ATTEMPTS_DURATION)))
        .block();
  }

  private String getDDWHerdId(UUID siteId) throws CustomDEExceptions {
    String ddwHerdId = "";
    SiteMappings bySiteId = siteMappingsRepository.findBySiteId(siteId.toString());
    if (bySiteId != null
        && bySiteId.getSiteMappingDocument() != null
        && bySiteId.getSiteMappingDocument().getDdwHerdId() != null) {
      ddwHerdId = bySiteId.getSiteMappingDocument().getDdwHerdId();
    }
    if (StringUtils.isNullOrEmpty(ddwHerdId)) {
      throw new CustomDEExceptions(FILE_NOT_FOUND_MESSAGE, HttpStatus.NOT_FOUND.value());
    }
    return ddwHerdId;
  }
}
