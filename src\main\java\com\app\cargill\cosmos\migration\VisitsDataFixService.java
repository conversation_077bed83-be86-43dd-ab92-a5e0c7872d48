/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.document.*;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Notes;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.VisitsRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * This service handles all Visits data quality fixes required to run after migration of users or
 * whenever a recalculation is needed
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VisitsDataFixService {

  private final VisitsRepository visitsRepository;
  private final NotesRepository notesRepository;
  private final DietRepository dietRepository;
  private final PensRepository pensRepository;
  private int mappingFixCount;
  private int mappingFailedCount;

  private int count = 0;

  private static final String LOCOMOTION_SCORE = "LocomotionScore";

  private static final String ANIMAL_ANALYSIS = "AnimalAnalysis";

  private static final String BODY_CONDITION = "BodyCondition";

  private static final String TMRPARTICLE_SCORE = "TMRParticleScore";
  private static final String RUMENFILL_SCORE = "RumenFIllScore";

  private static final String RUMENHEALTHMANURE_SCORE = "RumenHealthManureScore";

  private static final String CUD_CHEWING = "CudChewing";

  private static final String MANURESCREEN_TOOL = "ManureScreenerTool";

  private static final String PENTIMEBUDGET_TOOL = "PenTimeBudgetTool";

  public Mono<Visits> updateMobileLastUpdatedDate() {
    return Mono.fromCallable(visitsRepository::findVisitsByLastMobileUpdatedDate)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(this::updateMobileDate);
  }

  public Mono<Visits> updateMobileDate(List<Visits> visits) {
    if (visits.isEmpty()) {
      log.debug("No Records found");
      return Mono.just(new Visits());
    }
    mappingFailedCount = 0;
    mappingFixCount = 0;
    log.debug("Total count: {}", visits.size());
    visits.parallelStream()
        .forEach(
            visit -> {
              try {
                mappingFixCount++;
                log.debug(
                    "Fixing LAST MOBILE UPDATED DATE FOR VISIT {}: {} ",
                    mappingFixCount,
                    visit.getVisitDocument().getId());
                visit
                    .getVisitDocument()
                    .setMobileLastUpdatedTime(visit.getVisitDocument().getLastModifiedTimeUtc());
                visitsRepository.save(visit);
              } catch (Exception ex) {
                mappingFailedCount++;
                log.error(
                    "Error while updating LAST Mobile Updated date for visit: {} REASON: {} ",
                    visit.getVisitDocument().getId(),
                    ex.getLocalizedMessage());
              }
            });
    log.debug("Success: {} \n Failure: {}", mappingFixCount, mappingFailedCount);
    return Mono.just(visits.get(0));
  }

  public Mono<Visits> tmrPenStateIndexesFix() {
    return Mono.fromCallable(visitsRepository::findVisitsWithTmrPennState)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(this::fixAndSaveTmrPenStateIndexes);
  }

  public Mono<Visits> fixAndSaveTmrPenStateIndexes(List<Visits> visits) {
    log.info("Visits found: {}", visits.size());
    mappingFixCount = 1;
    if (!visits.isEmpty()) {
      visits.forEach(
          visit -> {
            log.info("Fixing for visit: {} ", visit.getVisitDocument().getId());
            if (visit.getVisitDocument().getTmrParticleScore() != null) {
              log.info("TMR Particle Score object found for visit number: {}", mappingFixCount);
              HashMap<UUID, Integer> penIdsMap = new HashMap<>();
              visit
                  .getVisitDocument()
                  .getTmrParticleScore()
                  .getTmrScores()
                  .forEach(
                      score -> {
                        if (score.getPenId() != null) {
                          if (!penIdsMap.containsKey(score.getPenId())) {
                            penIdsMap.put(score.getPenId(), 1);
                          } else {
                            penIdsMap.put(score.getPenId(), penIdsMap.get(score.getPenId()) + 1);
                          }
                          score.setTmrScoreIndex(Integer.toString(penIdsMap.get(score.getPenId())));
                        }
                      });
              visitsRepository.save(visit);
              mappingFixCount++;
            }
          });
      log.info("visits saved successfully");
    }

    return Mono.just(!visits.isEmpty() ? visits.get(0) : new Visits());
  }

  public void updateVisitsNotesDietsForDeletedSiteIds(SitesDeletedTransform sites) {
    sites.getSitesFrom().parallelStream()
        .forEach(
            siteId -> {
              List<Visits> visits = visitsRepository.getVisitsDocBySiteId(siteId.toString());
              if (!visits.isEmpty()) {
                visits.parallelStream()
                    .forEach(
                        visit -> {
                          visit.getVisitDocument().setSiteId(UUID.fromString(sites.getSitesTo()));
                          visitsRepository.save(visit);
                        });
              }
            });

    updateNotesForDeletedSiteIds(sites.getSitesFrom(), sites.getSitesTo());
    updateDietsForDeletedSiteIds(sites.getSitesFrom(), sites.getSitesTo());
  }

  public void updateNotesForDeletedSiteIds(List<UUID> sitesFrom, String siteTo) {

    sitesFrom.parallelStream()
        .forEach(
            siteId -> {
              List<Notes> notes = notesRepository.findByNotesBySiteId(siteId.toString());
              if (!notes.isEmpty()) {
                notes.parallelStream()
                    .forEach(
                        note -> {
                          note.getNotesDocument().setSiteId(UUID.fromString(siteTo));
                          notesRepository.save(note);
                        });
              }
            });
  }

  public void updateDietsForDeletedSiteIds(List<UUID> sitesFrom, String siteTo) {
    sitesFrom.parallelStream()
        .forEach(
            siteId -> {
              List<Diets> diets = dietRepository.findAllBySiteId(siteId.toString());
              if (!diets.isEmpty()) {
                diets.parallelStream()
                    .forEach(
                        diet -> {
                          diet.getDietDocument().setSiteId(UUID.fromString(siteTo));
                          dietRepository.save(diet);
                        });
              }
            });
  }

  public Flux<Visits> updateLocomotionScorePensData() {
    return Mono.fromCallable(visitsRepository::findAllLocomotionScorePublishedVisits)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateLocomotionScorePensData(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updateLocomotionScorePensData(Visits visit) {

    log.info(
        "Updating data for locomotion of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);
    LocomotionTool locomotionScore = visit.getVisitDocument().getLocomotionScore();

    if (locomotionScore != null && locomotionScore.getPens() != null) {
      locomotionScore.getPens().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenId(penById.getPenDocument().getId());
                    pen.setPenName(penById.getPenDocument().getName());
                    pen.setMilkProductionInKg(penById.getPenDocument().getMilk());
                  }
                }
              });
      checkPensUsed(visit, penIds, pensUsed, LOCOMOTION_SCORE);
    }
    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setLocomotionScore(locomotionScore);
    return visitsRepository.save(visit);
  }

  public Visits updateAnimalAnalysisPensData(Visits visit) {
    log.info("Updating data for Animal Analysis of visit: {}", visit.getVisitDocument().getId());

    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    AnimalAnalysisTool animalAnalysis = visit.getVisitDocument().getAnimalAnalysis();

    if (animalAnalysis != null && animalAnalysis.getAnimals() != null) {

      animalAnalysis.getAnimals().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });

      checkPensUsed(visit, penIds, pensUsed, ANIMAL_ANALYSIS);
    }
    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setAnimalAnalysis(animalAnalysis);
    return visitsRepository.save(visit);
  }

  public Flux<Visits> updateAnimalAnalysisPensData() {
    return Mono.fromCallable(visitsRepository::findAllAnimalAnalysisPublishedVisits)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateAnimalAnalysisPensData(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Flux<Visits> fixVisitName() {
    return Mono.fromCallable(visitsRepository::findAllVisitsByVisitNameWithBackSlashes)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .doOnNext(
            visit ->
                log.info(
                    "Processing visit: {} ", visit.getVisitDocument().getId())) // Log each visit
        .flatMap(
            visit ->
                Mono.fromCallable(() -> fixVisitName(visit))
                    .subscribeOn(Schedulers.boundedElastic()))
        .doOnComplete(
            () ->
                log.info(
                    "Job completed for fixing visit names")); // Log when processing is complete
  }

  private Visits fixVisitName(Visits visit) {
    String visitName = visit.getVisitDocument().getVisitName().replace('/', '-');
    visit.getVisitDocument().setVisitName(visitName);
    return visitsRepository.save(visit);
  }

  public Flux<Visits> updateBodyConditionPensData() {
    return Mono.fromCallable(visitsRepository::getBodyConditionForVisits)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateBodyConditionPensData(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updateBodyConditionPensData(Visits visit) {

    log.info(
        "Updating data for bodyCondition of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    BodyConditionTool bodyCondition = visit.getVisitDocument().getBodyCondition();

    if (bodyCondition != null && bodyCondition.getPens() != null) {
      bodyCondition.getPens().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });

      checkPensUsed(visit, penIds, pensUsed, BODY_CONDITION);
    }

    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setBodyCondition(bodyCondition);
    return visitsRepository.save(visit);
  }

  private Map<String, List<UUID>> getPensUsed(Visits visit) {
    Map<String, List<UUID>> pensUsed = visit.getVisitDocument().getPensUsed();
    if (pensUsed == null || pensUsed.isEmpty()) {
      pensUsed = new HashMap<>();
    }
    return pensUsed;
  }

  public Flux<Visits> updateTmrParticleScore() {
    return Mono.fromCallable(visitsRepository::getTmrParticleScoreForVisits)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateTmrParticleScore(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updateTmrParticleScore(Visits visit) {

    log.info(
        "Updating data for Tmr Particle Score of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    RumenHealthTMRParticleScoreTool tmrParticleScoreTool =
        visit.getVisitDocument().getTmrParticleScore();

    if (tmrParticleScoreTool != null && tmrParticleScoreTool.getTmrScores() != null) {
      tmrParticleScoreTool.getTmrScores().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });

      checkPensUsed(visit, penIds, pensUsed, TMRPARTICLE_SCORE);
    }

    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setTmrParticleScore(tmrParticleScoreTool);
    return visitsRepository.save(visit);
  }

  public Flux<Visits> updateRumenHealthManureScore() {
    return Mono.fromCallable(visitsRepository::getRumenHealthManureScoreForVisits)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateRumenHealthManureScore(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updateRumenHealthManureScore(Visits visit) {

    log.info(
        "Updating data for RumenHealthManureScore of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    RumenHealthManureScoreTool rumenHealthManureScoreTool =
        visit.getVisitDocument().getRumenHealthManureScore();

    if (rumenHealthManureScoreTool != null && rumenHealthManureScoreTool.getPens() != null) {
      rumenHealthManureScoreTool.getPens().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });

      checkPensUsed(visit, penIds, pensUsed, RUMENHEALTHMANURE_SCORE);
    }

    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setRumenHealthManureScore(rumenHealthManureScoreTool);
    return visitsRepository.save(visit);
  }

  public Flux<Visits> updateRumenFillManureScore() {
    return Mono.fromCallable(visitsRepository::getRumenFillManureScoreForVisits)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateRumenFillManureScore(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updateRumenFillManureScore(Visits visit) {

    log.info(
        "Updating data for RumenFillManureScore of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    RumenFillTool rumenFillTool = visit.getVisitDocument().getRumenFillManureScore();

    if (rumenFillTool != null && rumenFillTool.getPens() != null) {
      rumenFillTool.getPens().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });

      checkPensUsed(visit, penIds, pensUsed, RUMENFILL_SCORE);
    }

    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setRumenFillManureScore(rumenFillTool);
    return visitsRepository.save(visit);
  }

  public Flux<Visits> updateCudChewing() {
    return Mono.fromCallable(visitsRepository::getCudChewingForVisits)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateCudChewing(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updateCudChewing(Visits visit) {

    log.info(
        "Updating data for CudChewing of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    CudChewingTool cudChewing = visit.getVisitDocument().getCudChewing();
    if (cudChewing != null && cudChewing.getCudChewingReports() != null) {
      cudChewing.getCudChewingReports().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });
      checkPensUsed(visit, penIds, pensUsed, CUD_CHEWING);
    }

    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setCudChewing(cudChewing);
    return visitsRepository.save(visit);
  }

  public Flux<Visits> updateManureScreenerTool() {
    return Mono.fromCallable(visitsRepository::getManureScreenerTool)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updateManureScreenerTool(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updateManureScreenerTool(Visits visit) {
    log.info(
        "Updating data for ManureScreenerTool of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    ManureScreenerTool manureScreenerTool = visit.getVisitDocument().getManureScreenerTool();
    if (manureScreenerTool != null && manureScreenerTool.getMstScores() != null) {
      manureScreenerTool.getMstScores().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });
      checkPensUsed(visit, penIds, pensUsed, MANURESCREEN_TOOL);
    }
    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setManureScreenerTool(manureScreenerTool);
    return visitsRepository.save(visit);
  }

  public Flux<Visits> updatePenTimeBudgetTool() {
    return Mono.fromCallable(visitsRepository::getManureScreenerTool)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            visit ->
                Mono.fromCallable(() -> updatePenTimeBudgetTool(visit))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Visits updatePenTimeBudgetTool(Visits visit) {
    log.info(
        "Updating data for PenTimeBudgetTool of visit: {} AND count: {}",
        visit.getVisitDocument().getId(),
        count++);
    List<UUID> penIds = new ArrayList<>();
    Map<String, List<UUID>> pensUsed = getPensUsed(visit);

    PenTimeBudgetTool penTimeBudgetTool = visit.getVisitDocument().getPenTimeBudgetTool();
    if (penTimeBudgetTool != null && penTimeBudgetTool.getPens() != null) {
      penTimeBudgetTool.getPens().stream()
          .forEach(
              pen -> {
                if (!Objects.isNull(pen.getPenId())) {
                  Pens penById = pensRepository.findByPenId(pen.getPenId().toString());
                  penIds.add(pen.getPenId());
                  if (!Objects.isNull(penById)) {
                    pen.setPenName(penById.getPenDocument().getName());
                  }
                }
              });
      checkPensUsed(visit, penIds, pensUsed, PENTIMEBUDGET_TOOL);
    }
    visit.getVisitDocument().setPensUsed(pensUsed);
    visit.getVisitDocument().setPenTimeBudgetTool(penTimeBudgetTool);
    return visitsRepository.save(visit);
  }

  public void checkPensUsed(
      Visits visit, List<UUID> penIds, Map<String, List<UUID>> pensUsed, String tmrparticleScore) {
    if (Objects.isNull(visit.getVisitDocument().getPensUsed())) {

      pensUsed.put(tmrparticleScore, penIds);
    } else if (visit.getVisitDocument().getPensUsed().isEmpty()) {
      pensUsed.put(tmrparticleScore, penIds);
    } else {
      if (pensUsed.containsKey(tmrparticleScore)) {
        pensUsed.remove(tmrparticleScore);
      }
      pensUsed.put(tmrparticleScore, penIds);
    }
  }
}
