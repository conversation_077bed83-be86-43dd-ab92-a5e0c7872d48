/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.UserActivityLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface UserActivityLogRepository extends JpaRepository<UserActivityLog, Long>, JpaSpecificationExecutor<UserActivityLog> {

    /**
     * Find activity logs by username
     */
    Page<UserActivityLog> findByUsernameOrderByCreatedDateDesc(String username, Pageable pageable);

    /**
     * Find activity logs by event name
     */
    Page<UserActivityLog> findByEventNameOrderByCreatedDateDesc(String eventName, Pageable pageable);

    /**
     * Find activity logs by date range
     */
    @Query("SELECT u FROM UserActivityLog u WHERE u.createdDate BETWEEN :startDate AND :endDate ORDER BY u.createdDate DESC")
    Page<UserActivityLog> findByDateRange(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate, Pageable pageable);

    /**
     * Find activity logs by username and date range
     */
    @Query("SELECT u FROM UserActivityLog u WHERE u.username = :username AND u.createdDate BETWEEN :startDate AND :endDate ORDER BY u.createdDate DESC")
    Page<UserActivityLog> findByUsernameAndDateRange(@Param("username") String username, @Param("startDate") Instant startDate, @Param("endDate") Instant endDate, Pageable pageable);

    /**
     * Find activity logs by event name and date range
     */
    @Query("SELECT u FROM UserActivityLog u WHERE u.eventName = :eventName AND u.createdDate BETWEEN :startDate AND :endDate ORDER BY u.createdDate DESC")
    Page<UserActivityLog> findByEventNameAndDateRange(@Param("eventName") String eventName, @Param("startDate") Instant startDate, @Param("endDate") Instant endDate, Pageable pageable);

    /**
     * Find activity logs by path pattern
     */
    @Query("SELECT u FROM UserActivityLog u WHERE u.path LIKE %:pathPattern% ORDER BY u.createdDate DESC")
    Page<UserActivityLog> findByPathContaining(@Param("pathPattern") String pathPattern, Pageable pageable);

    /**
     * Find activity logs by account ID
     */
    Page<UserActivityLog> findByAccountIdOrderByCreatedDateDesc(String accountId, Pageable pageable);

    /**
     * Find activity logs by site ID
     */
    Page<UserActivityLog> findBySiteIdOrderByCreatedDateDesc(String siteId, Pageable pageable);

    /**
     * Find activity logs by feature used
     */
    Page<UserActivityLog> findByFeatureUsedOrderByCreatedDateDesc(String featureUsed, Pageable pageable);

    /**
     * Find activity logs by module name
     */
    Page<UserActivityLog> findByModuleNameOrderByCreatedDateDesc(String moduleName, Pageable pageable);

    /**
     * Get activity count by event name for analytics
     */
    @Query("SELECT u.eventName, COUNT(u) FROM UserActivityLog u GROUP BY u.eventName ORDER BY COUNT(u) DESC")
    List<Object[]> getEventNameAnalytics();

    /**
     * Get activity count by path for analytics
     */
    @Query("SELECT u.path, COUNT(u) FROM UserActivityLog u GROUP BY u.path ORDER BY COUNT(u) DESC")
    List<Object[]> getPathAnalytics();

    /**
     * Get activity count by username for analytics
     */
    @Query("SELECT u.username, COUNT(u) FROM UserActivityLog u GROUP BY u.username ORDER BY COUNT(u) DESC")
    List<Object[]> getUserAnalytics();

    /**
     * Get daily activity count for analytics
     */
    @Query("SELECT DATE(u.createdDate), COUNT(u) FROM UserActivityLog u WHERE u.createdDate BETWEEN :startDate AND :endDate GROUP BY DATE(u.createdDate) ORDER BY DATE(u.createdDate)")
    List<Object[]> getDailyActivityAnalytics(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Find all activity logs for export (without pagination)
     */
    @Query("SELECT u FROM UserActivityLog u WHERE u.createdDate BETWEEN :startDate AND :endDate ORDER BY u.createdDate DESC")
    List<UserActivityLog> findAllForExport(@Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

    /**
     * Find activity logs by multiple criteria for export
     */
    @Query("SELECT u FROM UserActivityLog u WHERE " +
           "(:username IS NULL OR u.username = :username) AND " +
           "(:eventName IS NULL OR u.eventName = :eventName) AND " +
           "(:accountId IS NULL OR u.accountId = :accountId) AND " +
           "u.createdDate BETWEEN :startDate AND :endDate " +
           "ORDER BY u.createdDate DESC")
    List<UserActivityLog> findByMultipleCriteriaForExport(
            @Param("username") String username,
            @Param("eventName") String eventName,
            @Param("accountId") String accountId,
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate);

    /**
     * Find activity logs by multiple criteria for export with lastUpdatedTime filter
     * Only includes records where updatedDate is after the specified lastUpdatedTime
     */
    @Query("SELECT u FROM UserActivityLog u WHERE " +
           "(:username IS NULL OR u.username = :username) AND " +
           "(:eventName IS NULL OR u.eventName = :eventName) AND " +
           "(:accountId IS NULL OR u.accountId = :accountId) AND " +
           "u.updatedDate > :lastUpdatedTime AND " +
           "u.createdDate <= :endDate " +
           "ORDER BY u.createdDate DESC")
    List<UserActivityLog> findByMultipleCriteriaForExportAfterLastUpdate(
            @Param("username") String username,
            @Param("eventName") String eventName,
            @Param("accountId") String accountId,
            @Param("lastUpdatedTime") Instant lastUpdatedTime,
            @Param("endDate") Instant endDate);
}
