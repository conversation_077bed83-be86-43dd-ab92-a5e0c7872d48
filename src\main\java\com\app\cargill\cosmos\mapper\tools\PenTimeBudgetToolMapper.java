/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.PenTimeBudgetToolCosmos;
import com.app.cargill.cosmos.model.tools.PenTimeBudgetToolItemCosmos;
import com.app.cargill.document.PenTimeBudgetTool;
import com.app.cargill.document.PenTimeBudgetToolItem;
import java.time.Instant;
import java.util.UUID;

public class PenTimeBudgetToolMapper {

  private PenTimeBudgetToolMapper() {}

  public static PenTimeBudgetTool map(PenTimeBudgetToolCosmos input) {

    CosmosToModelMapper<PenTimeBudgetToolItemCosmos, PenTimeBudgetToolItem> itemMapper =
        source ->
            PenTimeBudgetToolItem.builder()
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(
                    source.getLastModifiedTimeUtc() != null
                        ? source.getLastModifiedTimeUtc().getDate()
                        : Instant.MIN)
                .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
                .penName(source.getPenName())
                .visitForComparison(
                    source.getVisitForComparison() != null
                        ? UUID.fromString(source.getVisitForComparison())
                        : null)
                .selectedRestingVisits(
                    source.getSelectedRestingVisits() != null
                        ? source.getSelectedRestingVisits().stream().map(UUID::fromString).toList()
                        : null)
                .selectedMilkVisits(
                    source.getSelectedMilkVisits() != null
                        ? source.getSelectedMilkVisits().stream().map(UUID::fromString).toList()
                        : null)
                .stallsInPen(source.getStallsInPen())
                .walkingTimeToParlor(source.getWalkingTimeToParlor())
                .timeInParlor(source.getTimeInParlor())
                .walkingTimeFromParlor(source.getWalkingTimeFromParlor())
                .stallsInParlor(source.getStallsInParlor())
                .timeInLockUp(source.getTimeInLockUp())
                .otherNonRestTime(source.getOtherNonRestTime())
                .restingRequirement(source.getRestingRequirement())
                .eatingTime(source.getEatingTime())
                .drinkingGroomingTime(source.getDrinkingGroomingTime())
                .toolStatus(source.getToolStatus())
                .animals(source.getAnimals())
                .milkingFrequency(source.getMilkingFrequency())
                .build();

    CosmosToModelMapper<PenTimeBudgetToolCosmos, PenTimeBudgetTool> mapper =
        source ->
            PenTimeBudgetTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .build();

    return mapper.map(input);
  }
}
