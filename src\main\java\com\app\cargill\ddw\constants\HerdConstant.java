/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.constants;

import java.util.UUID;

public class HerdConstant {
  private HerdConstant() {}

  public static final String HEIFER = "HEIFERS";
  public static final String DRY_COWS = "DRYCOWS";
  public static final String LACTATING_COWS = "LACTATINGCOWS";

  public static final String UNKNOWN = "UNKNOWN";

  public static final String HEIFERS_DIET = "HEIFERS/HEIFERS";
  public static final String DRY_DIET = "DRY/FAROFF";
  public static final String LACTATING_DIET = "LACTATING/MILKING";

  public static final UUID LACTATING_MILKING_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000002");
  public static final UUID HEIFER_ID = UUID.fromString("00000000-0000-0000-0000-000000000011");
  public static final UUID DRY_FAR_OFF_ID = UUID.fromString("00000000-0000-0000-0000-000000000008");
}
