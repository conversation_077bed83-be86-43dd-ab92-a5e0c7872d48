/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

@Container(containerName = "MilkProcessors")
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
public class MilkProcessorCosmos {

  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("UserId")
  private String userId;

  @JsonProperty("ComponentProcessors")
  private List<MilkProcessorDataCosmos> componentProcessors = new ArrayList<>();

  @JsonProperty("ConcentrationProcessors")
  private List<MilkProcessorDataCosmos> concentrationProcessors = new ArrayList<>();

  @JsonProperty("IsDeleted")
  private boolean isDeleted = false;
}
