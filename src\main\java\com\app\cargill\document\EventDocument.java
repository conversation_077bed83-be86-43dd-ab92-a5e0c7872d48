/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@ToString
public class EventDocument extends EditableDocumentBase implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("EventId")
  public String eventId;

  @JsonProperty("Business__c")
  public String businessC;

  @JsonProperty("IsReminderSet")
  public Boolean isReminderSet = true;

  @JsonProperty("StartDateTime")
  public String startDateTime;

  @JsonProperty("EndDateTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public String endDateTime;

  @JsonProperty("ActivityDateTime")
  public String activityDateTime;

  @JsonProperty("Subject")
  public String subject;

  @JsonProperty("IsAllDayEvent")
  public Boolean isAllDayEvent;

  @JsonProperty("Type__c")
  public String typeC;

  @JsonProperty("DE_Activity_External_ID__c")
  public String deActivityExternalIdC;

  @JsonProperty("DE_Site_Visit_ID__c")
  public String deSiteVisitIdC;

  @JsonProperty("Report_Link__c")
  public String reportLinkC;

  @JsonProperty("WhatId")
  public String whatId;

  @JsonProperty("OwnerId")
  public String ownerId;

  @JsonProperty("DE_Site__c")
  public String deSiteId;
}
