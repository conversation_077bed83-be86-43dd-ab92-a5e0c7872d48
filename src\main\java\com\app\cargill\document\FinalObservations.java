/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FinalObservations implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("PositiveTrends")
  private String positiveTrends;

  @JsonProperty("Opportunities")
  private String opportunities;

  @JsonProperty("Comments")
  private String comments;

  private UUID id;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private Boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private Instant lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private Boolean isNew;
}
