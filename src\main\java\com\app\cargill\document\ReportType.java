/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.VisitReportType;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportType implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant creationDateTime;

  private String url;

  @JsonProperty("MediaId")
  private UUID mediaId;

  @JsonProperty("AccountId")
  private UUID accountId;

  @JsonProperty("Type")
  private VisitReportType type;
}
