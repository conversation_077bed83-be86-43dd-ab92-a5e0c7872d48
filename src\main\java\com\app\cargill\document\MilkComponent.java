/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MilkComponent implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("PricePerKg")
  private Double pricePerKg;

  @JsonProperty("PricePerHl")
  private Double pricePerHl;

  @JsonProperty("PricePerKgPerCow")
  private Double pricePerKgPerCow;
}
