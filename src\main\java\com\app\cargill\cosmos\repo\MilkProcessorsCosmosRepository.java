/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.MilkProcessorCosmos;
import com.azure.spring.data.cosmos.repository.Query;
import com.azure.spring.data.cosmos.repository.ReactiveCosmosRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface MilkProcessorsCosmosRepository
    extends ReactiveCosmosRepository<MilkProcessorCosmos, String> {
  @Query(value = "Select * FROM c where STRINGEQUALS(@userId, c.UserId, true)")
  Flux<MilkProcessorCosmos> getMilkProcessorsByUserId(@Param("userId") String userId);
}
