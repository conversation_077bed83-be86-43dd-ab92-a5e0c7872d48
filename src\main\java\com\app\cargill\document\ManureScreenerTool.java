/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder(toBuilder = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class ManureScreenerTool extends EditableDocumentBase {

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("MSTScores")
  private List<ManureScreenerToolItem> mstScores;

  @JsonProperty("MSTGoal")
  private ManureScreenerScoreGoalToolItem mstGoal;

  @JsonProperty("Observation")
  private String observation;
}
