/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.LactationStage;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RumenHealthManureScoreToolGoalItem implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("LactationStage")
  public LactationStage lactationStage;

  @JsonProperty("GoalMin")
  public Double goalMin;

  @JsonProperty("GoalMax")
  public Double goalMax;
}
