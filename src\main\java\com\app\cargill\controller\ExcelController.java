/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@SuppressWarnings("java:S125")
public class ExcelController {

  //  @Autowired private ExcelService excelService;
  //
  //  @PostMapping("/upload")
  //  public ResponseEntity<?> uploadExcelFile(@RequestParam("file") MultipartFile file) {
  //    try {
  //      File outputFile = excelService.processExcel(file);
  //      return ResponseEntity.ok()
  //          .header("Content-Disposition", "attachment; filename=output.json.txt")
  //          .body(outputFile);
  //    } catch (IOException e) {
  //      e.printStackTrace();
  //      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
  //          .body("Error processing the file");
  //    }
  //  }
  //
  //  @PostMapping("/convert")
  //  public ResponseEntity<byte[]> convertJsonToExcel(@RequestBody JsonNode rootNode)
  //      throws IOException {
  //    String[] headers = {
  //      "Key",
  //      "English",
  //      "Italian",
  //      "Polish",
  //      "Português",
  //      "Russian",
  //      "Chinese",
  //      "French - Fr",
  //      "French - Ca",
  //      "Korean"
  //    };
  //
  //    // Create a new workbook and sheet
  //    Workbook workbook = new XSSFWorkbook();
  //    Sheet sheet = workbook.createSheet("Translations");
  //
  //    // Create the header row
  //    Row headerRow = sheet.createRow(0);
  //    for (int i = 0; i < headers.length; i++) {
  //      Cell cell = headerRow.createCell(i);
  //      cell.setCellValue(headers[i]);
  //    }
  //
  //    int rowNum = 1;
  //    rowNum = processJsonNode(rootNode, sheet, rowNum);
  //
  //    // Write the workbook to a ByteArrayOutputStream
  //    ByteArrayOutputStream bos = new ByteArrayOutputStream();
  //    workbook.write(bos);
  //    workbook.close();
  //
  //    byte[] excelBytes = bos.toByteArray();
  //    bos.close();
  //
  //    HttpHeaders headers1 = new HttpHeaders();
  //    headers1.setContentType(MediaType.APPLICATION_OCTET_STREAM);
  //    headers1.setContentDispositionFormData("attachment", "translations.xlsx");
  //
  //    return ResponseEntity.ok().headers(headers1).body(excelBytes);
  //  }
  //
  //  private int processJsonNode(JsonNode rootNode, Sheet sheet, int rowNum) {
  //    Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
  //
  //    while (fields.hasNext()) {
  //      Map.Entry<String, JsonNode> field = fields.next();
  //      String key = field.getKey();
  //      JsonNode value = field.getValue();
  //
  //      if (value.isArray()) {
  //        for (JsonNode arrayElement : value) {
  //          rowNum = processJsonNode(arrayElement, sheet, rowNum);
  //        }
  //      } else if (value.isObject()) {
  //        Row row = sheet.createRow(rowNum++);
  //        row.createCell(0).setCellValue(key);
  //        row.createCell(1).setCellValue(value.path("EN").asText(""));
  //        row.createCell(2).setCellValue(value.path("IT").asText(""));
  //        row.createCell(3).setCellValue(value.path("PL").asText(""));
  //        row.createCell(4).setCellValue(value.path("PT").asText(""));
  //        row.createCell(5).setCellValue(value.path("RU").asText(""));
  //        row.createCell(6).setCellValue(value.path("ZH").asText(""));
  //        row.createCell(7).setCellValue(value.path("FR").asText(""));
  //        row.createCell(8).setCellValue(value.path("FRCA").asText(""));
  //        row.createCell(9).setCellValue(value.path("KO").asText(""));
  //      }
  //    }
  //    return rowNum;
  //  }
}
