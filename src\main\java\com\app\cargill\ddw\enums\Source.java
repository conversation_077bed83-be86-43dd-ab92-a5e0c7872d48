/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum Source {
  MAX(0),
  USER_CREATED(1),
  DDW(2),
  SYSTEM_GENERATED(3);

  private final Integer value;

  @JsonCreator
  Source(Integer value) {
    this.value = value;
  }

  @JsonValue
  public Integer getValue() {
    return value;
  }
}
