/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.MilkPickup;
import com.app.cargill.constants.MilkUreaMeasure;
import com.app.cargill.document.MilkSoldEvaluationToolOutputToolItem;
import com.app.cargill.document.MilkSoldMilkProcessorToolItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class MilkSoldEvaluationToolItemCosmos {
  @JsonProperty("Outputs")
  private MilkSoldEvaluationToolOutputToolItem outputs;

  @JsonProperty("Pickups")
  private List<MilkSoldMilkProcessorToolItem> pickups;

  @JsonProperty("SelectedVisits")
  private List<String> selectedVisits;

  @JsonProperty("LactatingAnimals")
  private Integer lactatingAnimals;

  @JsonProperty("AnimalsinTank")
  private Integer animalsinTank;

  @JsonProperty("MilkPickup")
  private MilkPickup milkPickup;

  @JsonProperty("DryMatterIntake")
  private Double dryMatterIntake;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("MilkUreaMeasure")
  private MilkUreaMeasure milkUreaMeasure;
}
