/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.model.analytics.AnalyticsDataResponse;
import com.app.cargill.model.analytics.AnalyticsDataResponse.DataPointContainer;
import com.app.cargill.service.analytics.AnalyticsDataService;
import com.azure.core.annotation.QueryParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/analytics")
@Tag(
    name = "Analytics Reports Controller",
    description =
        "The purpose of this controller is to serve the data that has been collect for analytics"
            + " means")
@RequiredArgsConstructor
public class AnalyticsReportController {

  private final AnalyticsDataService analyticsDataService;

  @GetMapping("/getAll")
  @Operation(summary = "Get data for all data points")
  public AnalyticsDataResponse getAllData(
      @QueryParam("from") Instant from, @QueryParam("to") Instant to) {
    AnalyticsDataResponse analyticsDataResponse = new AnalyticsDataResponse();
    List<DataPointContainer> results = analyticsDataService.getAllDataPoints(from, to);
    analyticsDataResponse.setTotalRecords(
        results.stream().mapToInt(DataPointContainer::getTotalRecords).sum());
    analyticsDataResponse.setData(results);
    return analyticsDataResponse;
  }
}
