/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.RumenHealthTmrScores;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class ForagePennStateTool extends EditableDocumentBase implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("Scorer")
  private RumenHealthTmrScores scorer;

  @JsonProperty("Inputs")
  private List<ForagePennStateToolItem> inputs;

  @JsonProperty("Goals")
  private List<ForagePennStateToolGoalItem> goals;

  @JsonProperty("AvgTopOnScreenPercentage")
  public Double avgTopOnScreenPercentage;

  @JsonProperty("AvgMid1OnScreenPercentage")
  public Double avgMid1OnScreenPercentage;

  @JsonProperty("AvgMid2OnScreenPercentage")
  public Double avgMid2OnScreenPercentage;

  @JsonProperty("AvgTrayOnScreenPercentage")
  public Double avgTrayOnScreenPercentage;
}
