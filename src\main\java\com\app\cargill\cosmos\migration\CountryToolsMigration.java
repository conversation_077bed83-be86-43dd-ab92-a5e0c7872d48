/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Tool;
import com.app.cargill.constants.ToolGroup;
import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.migration.CosmosDataMigration.MigrationType;
import com.app.cargill.cosmos.model.CountryToolCosmos;
import com.app.cargill.cosmos.repo.CountryToolCosmosRepository;
import com.app.cargill.document.CountryToolDocument;
import com.app.cargill.model.CountryTools;
import com.app.cargill.repository.CountryToolsRepository;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CountryToolsMigration implements CosmosDataMigration {

  private final CountryToolCosmosRepository cosmosRepository;
  private final CountryToolsRepository countryToolsRepository;
  private Integer failed;
  private Integer success;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("CountryTools");
    try {
      List<CountryTools> countryToolsList = fetchAll(migrationResult);
      countryToolsRepository.saveAll(countryToolsList);
      log.info(
          "CountryTools migration completed. {} records copied to PostgresSQL",
          countryToolsList.size());
    } catch (Exception e) {
      log.error("Error occurred during CountryTools migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  private List<CountryTools> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    Iterator<CountryToolCosmos> cosmosIterator = cosmosRepository.findAll().iterator();
    List<CountryTools> countryToolsList = new ArrayList<>();
    int failures = 0;
    int recordsFetched = 0;
    while (cosmosIterator.hasNext()) {
      try {
        recordsFetched++;
        CountryTools countryTools = new CountryTools(countryToolsMapper.map(cosmosIterator.next()));
        countryTools.setLocalId(countryTools.getCountryToolDocument().getId().toString());
        countryToolsList.add(countryTools);
      } catch (Exception e) {
        log.error("There was an error fetching a User from CosmosDB", e);
        failures++;
      }
    }
    log.info("{} Users fetched from CosmosDB", recordsFetched);
    if (failures > 0) {
      log.warn("{} users failed to map during the fetching process", failures);
    }
    migrationResult.setSucceeded(countryToolsList.size());
    migrationResult.setFailed(failures);
    return countryToolsList;
  }

  @Async
  @Override
  public CompletableFuture<MigrationResult> postMigration(String migrationType) {
    MigrationResult migrationResult = new MigrationResult("Country Tools Post Migration");
    if (migrationType != null
        && (migrationType.equalsIgnoreCase(MigrationType.COUNTRY_TOOLS.name())
            || migrationType.equalsIgnoreCase(MigrationType.ALL.name()))) {
      log.info("STARTING POST MIGRATION TASKS FOR SITES : " + DateTime.now());
      try {
        updatRumenHealthTmrParticleScore();
      } catch (Exception e) {
        log.error("Could not perform tasks due to: " + e.getLocalizedMessage());
        return CompletableFuture.completedFuture(migrationResult);
      }
      migrationResult.setFailed(failed);
      migrationResult.setSucceeded(success);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  public void updatRumenHealthTmrParticleScore() {

    success = 0;
    failed = 0;
    List<CountryTools> countryTools =
        countryToolsRepository.findByToolId(Tool.TMRParticleScore.toString());

    if (!countryTools.isEmpty()) {
      countryTools.stream()
          .forEach(
              countryTool -> {
                countryTool.getCountryToolDocument().setToolGroupId(ToolGroup.Nutrition);
                success++;
              });
      countryToolsRepository.saveAll(countryTools);
    }
  }

  private final CosmosToModelMapper<CountryToolCosmos, CountryToolDocument> countryToolsMapper =
      source ->
          CountryToolDocument.builder()
              .countryId(Business.fromId(source.getCountryId()))
              .id(UUID.fromString(source.getId()))
              .toolGroupId(ToolGroup.fromId(source.getToolGroupId()))
              .toolId(Tool.fromId(source.getToolId()))
              .createUser(source.getCreateUser())
              .isDeleted(source.isDeleted())
              .lastModifyUser(source.getLastModifyUser())
              .createTimeUtc(source.getCreateTimeUtc())
              .lastModifiedTimeUtc(
                  Instant.ofEpochSecond(source.getLastModifiedTimeUtc().getEpoch()))
              .lastSyncTimeUtc(source.getLastSyncTimeUtc())
              .isNew(source.isNew())
              .build();
}
