/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class RevenueInputItemCosmos {
  @JsonProperty("ScenarioTitle")
  public Integer scenarioTitle;

  @JsonProperty("ScenarioTitleText")
  public String scenarioTitleText;

  @JsonProperty("ScenarioOneValue")
  public Double scenarioOneValue;

  @JsonProperty("ScenarioTwoValue")
  public Double scenarioTwoValue;

  @JsonProperty("VisitId")
  public String visitId;

  @JsonProperty("ToolStatus")
  public ToolStatuses toolStatus;
}
