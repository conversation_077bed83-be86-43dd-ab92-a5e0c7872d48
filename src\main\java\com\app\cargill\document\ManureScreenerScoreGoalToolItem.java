/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ManureScreenerScoreGoalToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("VisitsSelected")
  private List<UUID> visitsSelected;

  @JsonProperty("PenId")
  private UUID penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("GoalTitle")
  private String goalTitle;

  @JsonProperty("MidGoalMinimumPercent")
  private Double midGoalMinimumPercent;

  @JsonProperty("MidGoalMaximumPercent")
  private Double midGoalMaximumPercent;

  @JsonProperty("BottomGoalMinimumPercent")
  private Double bottomGoalMinimumPercent;

  @JsonProperty("BottomGoalMaximumPercent")
  private Double bottomGoalMaximumPercent;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("TopGoalMinimumPercent")
  private Double topGoalMinimumPercent;

  @JsonProperty("TopGoalMaximumPercent")
  private Double topGoalMaximumPercent;
}
