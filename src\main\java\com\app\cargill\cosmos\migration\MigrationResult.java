/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@ToString
public class MigrationResult implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private final String name;
  private int succeeded = 0;
  private int failed = 0;

  public MigrationResult(String name) {
    this.name = name;
  }
}
