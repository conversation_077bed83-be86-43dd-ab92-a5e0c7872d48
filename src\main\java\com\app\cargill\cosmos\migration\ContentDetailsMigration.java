/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.model.ContentDetailsCosmos;
import com.app.cargill.cosmos.repo.ContentDetailsCosmosRepository;
import com.app.cargill.document.ContentDetailsDocument;
import com.app.cargill.model.ContentDetails;
import com.app.cargill.repository.ContentDetailsRepository;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.PeriodType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContentDetailsMigration implements CosmosDataMigration {

  private final ContentDetailsCosmosRepository cosmosRepository;

  private final ContentDetailsRepository contentDetailsRepository;

  @Override
  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult(MigrationType.CONTENT_DETAILS.name());

    DateTime start = DateTime.now();
    AtomicInteger failedDetails = new AtomicInteger(0);
    Flux<ContentDetails> contentDetailsFlux = fetchAll(failedDetails);
    return processRecords(contentDetailsFlux, failedDetails)
        .reduce(0, (accumulator, a) -> accumulator + 1)
        .map(
            v -> {
              DateTime end = DateTime.now();
              Duration duration = new Duration(start, end);
              migrationResult.setFailed(failedDetails.get());
              migrationResult.setSucceeded(v);
              log.info(
                  "Time taken for ContentDetails migration: {} start: {} end: {}",
                  duration.toPeriod().normalizedStandard(PeriodType.standard()),
                  start,
                  end);

              log.info(
                  "ContentDetails migration completed in {} {} ",
                  duration.toPeriod().normalizedStandard(PeriodType.standard()),
                  migrationResult);
              return migrationResult;
            })
        .toFuture();
  }

  public Flux<ContentDetails> moveRecords(String accountId) {
    AtomicInteger failedRecords = new AtomicInteger(0);
    Flux<ContentDetails> cosmosRecords = fetchAccountRecords(accountId, failedRecords);
    return processRecords(cosmosRecords, failedRecords);
  }

  @Override
  public MigrationType migrationType() {
    return MigrationType.CONTENT_DETAILS;
  }

  private Flux<ContentDetails> fetchAll(AtomicInteger failedRecords) {
    return processCosmosRecords(cosmosRepository.findAll(), failedRecords);
  }

  private Flux<ContentDetails> fetchAccountRecords(String accountId, AtomicInteger failedRecords) {
    return processCosmosRecords(cosmosRepository.findAllByAccountId(accountId), failedRecords);
  }

  private Flux<ContentDetails> processRecords(
      Flux<ContentDetails> contentDetailsFlux, AtomicInteger failedDetails) {
    return contentDetailsFlux
        .filterWhen(
            cd ->
                Mono.fromCallable(() -> checkIfExists(failedDetails, cd))
                    .subscribeOn(Schedulers.boundedElastic()))
        .flatMap(
            cd ->
                Mono.fromCallable(() -> contentDetailsRepository.save(cd))
                    .subscribeOn(Schedulers.boundedElastic())
                    .onErrorResume(
                        t -> {
                          failedDetails.incrementAndGet();
                          log.error(
                              "CONTENT_DETAILS_MIGRATION_SAVE_IN_DB {} {}", t.getMessage(), cd);
                          return Mono.empty();
                        }))
        .onErrorContinue(
            (t, o) -> {
              failedDetails.incrementAndGet();
              log.error("CONTENT_DETAILS_MIGRATION_PROCESS {}", o, t);
            });
  }

  private Flux<ContentDetails> processCosmosRecords(
      Flux<ContentDetailsCosmos> cosmosFlux, AtomicInteger failedDocuments) {
    return cosmosFlux
        .filter(this::filterCosmosData)
        .map(this::cosmosToDocumentMapper)
        .map(
            contentDetailsDocument -> {
              ContentDetails contentDetails = new ContentDetails(contentDetailsDocument);
              contentDetails.setLocalId(contentDetailsDocument.getId().toString());
              return contentDetails;
            })
        .onErrorContinue(
            (throwable, object) -> {
              failedDocuments.incrementAndGet();
              log.error("CONTENT_DETAILS_MIGRATION {} {}", throwable.getMessage(), object);
            });
  }

  private boolean filterCosmosData(ContentDetailsCosmos cdCosmos) {
    boolean result = true;

    if (cdCosmos.getAccountId() == null) {
      log.warn("Missing accountId for ContentDetailsCosmos");
      log.warn("{}", cdCosmos);
      return false;
    }

    try {
      UUID.fromString(cdCosmos.getAccountId());
    } catch (IllegalArgumentException exception) {
      log.warn("Invalid accountId for ContentDetailsCosmos");
      log.warn("{}", cdCosmos);
      return false;
    }

    return result;
  }

  private ContentDetailsDocument cosmosToDocumentMapper(ContentDetailsCosmos cdCosmos) {
    ContentDetailsDocument cdDocument = new ContentDetailsDocument();
    cdDocument.setId(UUID.fromString(cdCosmos.getId()));
    cdDocument.setAccountId(UUID.fromString(cdCosmos.getAccountId()));
    cdDocument.setLabyrinthVisitId(cdCosmos.getLabyrinthVisitId());
    cdDocument.setLabyrinthContentType(cdCosmos.getLabyrinthContentType());
    cdDocument.setName(cdCosmos.getName());
    cdDocument.setReportType(cdCosmos.getReportType());
    cdDocument.setCreateUser(cdCosmos.getCreateUser());
    cdDocument.setDeleted(cdCosmos.isDeleted());
    cdDocument.setLastModifyUser(cdCosmos.getLastModifyUser());

    return cdDocument;
  }

  private Boolean checkIfExists(AtomicInteger failedDetails, ContentDetails cd) {
    try {
      return contentDetailsRepository.findByDocumentId(
              cd.getContentDetailsDocument().getId().toString())
          == null;
    } catch (Exception e) {
      failedDetails.incrementAndGet();
      log.error("CONTENT_DETAILS_MIGRATION_CHECK_IF_EXISTS {} {}", e.getMessage(), cd);
      return false;
    }
  }
}
