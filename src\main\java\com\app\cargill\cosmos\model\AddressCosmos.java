/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;

@Getter
public class AddressCosmos implements Serializable {

  @JsonProperty("Street")
  private String street;

  @JsonProperty("City")
  private String city;

  @JsonProperty("StateOrProvince")
  private String stateOrProvince;

  @JsonProperty("PostalCode")
  private String postalCode;

  @JsonProperty("Country")
  private String country;

  @JsonProperty("AddressID")
  private String addressID;

  @JsonProperty("CountyCommunity")
  private String countyCommunity;
}
