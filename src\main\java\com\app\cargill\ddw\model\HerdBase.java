/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdBase {

  @JsonProperty("categoryId")
  private String categoryId;

  @JsonProperty("EndDate")
  private Date endDate;

  @JsonProperty("EndDateId")
  private String endDateId;

  @JsonProperty("ExternalHerdId")
  private String externalHerdId;

  @JsonProperty("HerdProfileId")
  private String herdProfileId;

  @JsonProperty("PeriodType")
  private String periodType;

  @JsonProperty("StartDate")
  private Date startDate;

  @JsonProperty("StartDateId")
  private String startDateId;

  @JsonProperty("GroupCategory")
  private String groupCategory;

  @JsonProperty("GroupId")
  private String groupId;

  @JsonProperty("GroupName")
  private String groupName;
}
