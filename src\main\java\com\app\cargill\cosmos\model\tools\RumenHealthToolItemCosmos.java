/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.CudChewingCount;
import com.app.cargill.document.CudChewingCowCount;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class RumenHealthToolItemCosmos {
  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("CudChewsCount")
  private List<CudChewingCount> cudChewsCount;

  @JsonProperty("CudChewingCowsCount")
  private CudChewingCowCount cudChewingCowsCount;
}
