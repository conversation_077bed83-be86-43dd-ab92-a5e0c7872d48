/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

public enum OptimizationType {
  ANALYZE(0),
  FORMULATE(1);

  private final Integer value;

  OptimizationType(Integer optimizationType) {
    this.value = optimizationType;
  }

  public Integer getOptimizationType() {
    return value;
  }

  public static OptimizationType fromId(Integer id) {
    for (OptimizationType type : values()) {
      if (type.getOptimizationType().equals(id)) {
        return type;
      }
    }
    return null;
  }
}
