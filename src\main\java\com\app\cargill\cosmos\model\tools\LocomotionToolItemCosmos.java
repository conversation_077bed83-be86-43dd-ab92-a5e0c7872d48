/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.LocomotionToolItemCategoryItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class LocomotionToolItemCosmos {

  @JsonProperty("VisitsSelected")
  private List<String> visitsSelected;

  @JsonProperty("MilkScoreThree")
  private Double milkScoreThree = 5.1;

  @JsonProperty("MilkScoreFour")
  private Double milkScoreFour = 16.8;

  @JsonProperty("MilkScoreFive")
  private Double milkScoreFive = 36.0;

  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("TotalAnimalsInPen")
  private Integer totalAnimalsInPen;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("MilkProductionInKg")
  private Double milkProductionInKg;

  @JsonProperty("Categories")
  private List<LocomotionToolItemCategoryItem> categories;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;
}
