/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class StateDocument {
  private UUID id;

  @JsonProperty("StateCode")
  private String stateCode;

  @JsonProperty("StateName")
  private String stateName;

  @JsonProperty("CountryCode")
  private String countryCode;

  ;
}
