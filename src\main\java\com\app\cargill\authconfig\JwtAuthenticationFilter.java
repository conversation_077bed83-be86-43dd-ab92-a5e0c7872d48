/* Cargill Inc.(C) 2022 */
package com.app.cargill.authconfig;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

import com.app.cargill.confproperties.AzureADProperties;
import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.dto.DiscoveryKeyDto;
import com.app.cargill.dto.Pair;
import com.app.cargill.dto.UserDetailsDto;
import com.app.cargill.model.User;
import com.app.cargill.repository.UserRepository;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jwt.SignedJWT;
import com.okta.jwt.AccessTokenVerifier;
import com.okta.jwt.Jwt;
import com.okta.jwt.JwtVerificationException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {
  private final OktaProperties oktaProperties;
  private final AzureADProperties azureADProperties;
  private final AccessTokenVerifierFactory accessTokenVerifierFactory;

  private final UserRepository userRepository;

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {
    // Get authorization header and validate
    final String header = request.getHeader(HttpHeaders.AUTHORIZATION);
    if (isEmpty(header) || !header.startsWith("Bearer ")) {
      filterChain.doFilter(request, response);
      return;
    }

    // Get jwt token and validate
    final String token = header.split(" ")[1].trim();
    Pair<Map<String, Object>, JWSHeader> mapJWSHeaderPair = parseJWT(token);

    if (mapJWSHeaderPair == null) {
      filterChain.doFilter(request, response);
      return;
    }

    // validate token first
    Pair<Boolean, AuthenticationPlatform> validate = validate(token, mapJWSHeaderPair);
    if (Boolean.TRUE.equals(validate.left())) {
      SecurityContextHolder.getContext()
          .setAuthentication(prepareUserContext(mapJWSHeaderPair, request, validate.right()));
      if (request.getHeader(HttpHeaders.ACCEPT_LANGUAGE) != null) {
        String languageTag = request.getHeader(HttpHeaders.ACCEPT_LANGUAGE).split(",")[0];
        Locale locale = Locale.forLanguageTag(languageTag);
        LocaleContextHolder.setLocale(locale);
      }
    }
    // update security context

    filterChain.doFilter(request, response);
  }

  public static UsernamePasswordAuthenticationToken prepareUserContext(
      Pair<Map<String, Object>, JWSHeader> mapJWSHeaderPair,
      HttpServletRequest request,
      AuthenticationPlatform authPlatform) {
    // Get user identity and set it on the spring security context
    Map<String, Object> left = mapJWSHeaderPair.left();
    List<CustomGrantedAuthority> authorities = List.of(new CustomGrantedAuthority(authPlatform));
    UserDetails userDetails =
        UserDetailsDto.builder()
            .username(
                left.getOrDefault(
                        "unique_name", left.getOrDefault("upn", left.getOrDefault("sub", "")))
                    .toString()
                    .toLowerCase())
            .authorities(authorities)
            .build();

    UsernamePasswordAuthenticationToken authentication =
        new UsernamePasswordAuthenticationToken(
            userDetails, null, userDetails == null ? List.of() : userDetails.getAuthorities());

    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
    return authentication;
  }

  private Pair<Boolean, AuthenticationPlatform> validate(
      String token, Pair<Map<String, Object>, JWSHeader> mapJWSHeaderPair) {
    Map<String, Object> body = mapJWSHeaderPair.left();
    if (body.getOrDefault("iss", "").toString().contentEquals(oktaProperties.getIssuer())
        && body.getOrDefault("cid", "").toString().contentEquals(oktaProperties.getClientId())) {
      AccessTokenVerifier jwtVerifier = accessTokenVerifierFactory.getOktaAccessTokenVerifier();
      try {
        Jwt decodedToken = jwtVerifier.decode(token);
        return new Pair<>(
            validateUserExists((String) decodedToken.getClaims().getOrDefault("sub", "")),
            AuthenticationPlatform.OKTA);
      } catch (JwtVerificationException e) {
        log.error(e.getLocalizedMessage());
        return new Pair<>(false, AuthenticationPlatform.OKTA);
      }
    } else if (body.getOrDefault("iss", "")
        .toString()
        .contentEquals(azureADProperties.getIssuer())) {
      Map<String, Object> tokenHeader = mapJWSHeaderPair.right().toJSONObject();
      tokenHeader.put("aud", body.get("aud"));
      Long exp = (Long) body.get("exp");
      DiscoveryKeyDto azureDiscoveryKeys = accessTokenVerifierFactory.getAzureDiscoveredKeys();
      boolean found =
          headerKeyFound(
              azureDiscoveryKeys, tokenHeader, "api://".concat(azureADProperties.getClientId()));
      if (!found
          || LocalDateTime.ofEpochSecond(exp, 0, ZoneOffset.UTC)
              .isBefore(LocalDateTime.now(ZoneOffset.UTC))) {
        return new Pair<>(false, AuthenticationPlatform.AZURE_AD);
      }

      String tokenMail = (String) body.getOrDefault("upn", body.getOrDefault("unique_name", ""));
      // If there is no user provided but the token is still valid it's probably an integration
      // token
      String email =
          tokenMail == null || tokenMail.isEmpty()
              ? String.format("%s@integration", body.getOrDefault("oid", ""))
              : tokenMail;

      return new Pair<>(validateUserExists(email), AuthenticationPlatform.AZURE_AD);
    }
    return new Pair<>(false, AuthenticationPlatform.NOT_SET);
  }

  boolean validateUserExists(String email) {
    User user = userRepository.findByUserName(email.toLowerCase());
    if (user != null) {
      user.getUserDocument().setLastLoginDateTime(Instant.now());
      userRepository.save(user);
    }
    return user != null;
  }

  public boolean headerKeyFound(
      DiscoveryKeyDto azureDiscoveryKeys, Map<String, Object> tokenHeader, String aud) {
    return azureDiscoveryKeys.getKeys().parallelStream()
                .filter(
                    m ->
                        m.get("x5t")
                                .toString()
                                .contentEquals((String) tokenHeader.getOrDefault("x5t", ""))
                            && m.get("kid")
                                .toString()
                                .contentEquals((String) tokenHeader.getOrDefault("kid", "")))
                .count()
            > 0
        && aud.contentEquals((String) tokenHeader.getOrDefault("aud", ""));
  }

  public static Pair<Map<String, Object>, JWSHeader> parseJWT(String accessToken) {
    try {
      var decodedJWT = SignedJWT.parse(accessToken);
      var header = decodedJWT.getHeader();
      var payload = decodedJWT.getPayload().toJSONObject();
      return new Pair<>(payload, header);
    } catch (Exception e) {
      log.error("Invalid token!");
    }
    return null;
  }
}
