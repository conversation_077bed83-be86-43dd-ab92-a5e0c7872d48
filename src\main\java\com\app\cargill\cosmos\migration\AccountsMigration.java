/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.app.cargill.constants.SubTypeId;
import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.mapper.UuidMapper;
import com.app.cargill.cosmos.model.AccountCosmos;
import com.app.cargill.cosmos.model.AdditionalInformationCosmos;
import com.app.cargill.cosmos.model.AddressCosmos;
import com.app.cargill.cosmos.model.ContactCosmos;
import com.app.cargill.cosmos.repo.AccountsCosmosRepository;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.AdditionalInformation;
import com.app.cargill.document.Address;
import com.app.cargill.document.Contact;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SegmentsRepository;
import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.PeriodType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class AccountsMigration implements CosmosDataMigration {

  private final AccountsCosmosRepository cosmosRepository;
  private final AccountsRepository postgresAccountsRepository;
  private final SegmentsRepository segmentsRepository;
  private final CosmosToModelMapper<AdditionalInformationCosmos, AdditionalInformation>
      additionalInfoMapper =
          source ->
              AdditionalInformation.builder()
                  .id(UuidMapper.getNullableUuid(source.getId()))
                  .vat(source.getVAT())
                  .duns(source.getDuns())
                  .canBusinessID(source.getCANBusinessID())
                  .customerServiceNotes(source.getCustomerServiceNotes())
                  .paymentTerms(source.getPaymentTerms())
                  .statisticalId(source.getStatisticalID())
                  .vatFarmer(source.getVATFarmer())
                  .build();
  private final CosmosToModelMapper<AddressCosmos, Address> addressMapper =
      source ->
          Address.builder()
              .street(source.getStreet())
              .country(source.getCountry())
              .city(source.getCity())
              .postalCode(source.getPostalCode())
              .stateOrProvince(source.getStateOrProvince())
              .countyCommunity(source.getCountyCommunity())
              .build();
  private final CosmosToModelMapper<ContactCosmos, Contact> contactMapper =
      source ->
          Contact.builder()
              .accountId(UuidMapper.getNullableUuid(source.getAccountId()))
              .contactId(UuidMapper.getNullableUuid(source.getContactId()))
              .emailAddress(source.getEmailAddress())
              .firstName(source.getFirstName())
              .lastName(source.getLastName())
              .sFDCContactId(source.getSFDCContactId())
              .goldenRecordAcountId(source.getGoldenRecordAcountId())
              .phoneNumber(source.getPhoneNumber())
              .createTimeUtc(source.getCreateTimeUtc())
              .createUser(source.getCreateUser())
              .lastModifyUser(source.getLastModifyUser())
              .lastModifiedTimeUtc(source.getLastModifiedTimeUtc())
              .lastSyncTimeUtc(source.getLastSyncTimeUtc())
              .lastUpdateDateTime(source.getLastUpdateDateTime())
              .needsSync(false)
              .assitant(source.getAssitant())
              .assitantPhone(source.getAssitantPhone())
              .businessId(source.getBusinessId())
              .businessUnit(source.getBusinessUnit())
              .comments(source.getComments())
              .dataDotComKey(source.getDataDotComKey())
              .department(source.getDepartment())
              .description(source.getDescription())
              .doNotCall(source.getDoNotCall())
              .emailId(source.getEmailId())
              .emailOptOut(source.getEmailOptOut())
              .externalReportsToID(UuidMapper.getNullableUuid(source.getExternalReportsToID()))
              .flowName(source.getFlowName())
              .function(source.getFunction())
              .functionId(source.getFunctionId())
              .homePhone(source.getHomePhone())
              .isDeleted(isTrue(source.getIsDeleted()))
              .isNew(source.getIsNew())
              .languages(source.getLanguages())
              .level(source.getLevel())
              .leadSource(source.getLeadSource())
              .mailingAddress(
                  source.getMailingAddress() == null
                      ? null
                      : addressMapper.map(source.getMailingAddress()))
              .marketingId(source.getMarketingId())
              .name(source.getFlowName())
              .optIn(source.getOptIn())
              .lastStayInTouchRequestDate(source.getLastStayInTouchRequestDate())
              .mobileFirst(source.getMobileFirst())
              .build();

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("Accounts");
    DateTime start = DateTime.now();
    List<Accounts> accountsList = fetchAll(migrationResult);
    // filter to new accounts only
    accountsList =
        accountsList.stream()
            .filter(
                a ->
                    postgresAccountsRepository.findByAccountId(
                            a.getAccountDocument().getId().toString())
                        == null)
            .toList();
    try {
      log.info("Saving all accounts");
      postgresAccountsRepository.saveAll(accountsList);
      log.info(
          "Accounts migration completed. {} records copied to PostgresSQL", accountsList.size());
    } catch (Exception e) {
      log.error("Error occurred during Accounts migration", e);
    }
    DateTime end = DateTime.now();
    Duration duration = new Duration(start, end);
    log.info(
        "Time taken for accounts migration: {} start: {} end: {}",
        duration.toPeriod().normalizedStandard(PeriodType.standard()),
        start,
        end);
    return CompletableFuture.completedFuture(migrationResult);
  }

  public Flux<Accounts> moveUserAccounts(String email) {
    return Flux.fromIterable(() -> cosmosRepository.getAccountsByUserEmail(email).iterator())
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(this::mapCosmosAccount)
        .filter(a -> !a.isDeleted())
        .flatMap(this::processAccount)
        .onErrorResume(Mono::error);
  }

  public Flux<Accounts> moveAccountById(String id) {
    return Flux.fromIterable(() -> cosmosRepository.getAccountsById(id).iterator())
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(this::mapCosmosAccount)
        .filter(a -> !a.isDeleted())
        .flatMap(this::processAccount)
        .onErrorResume(Mono::error);
  }

  @Override
  public MigrationType migrationType() {
    return MigrationType.ACCOUNTS;
  }

  private Mono<Accounts> mapCosmosAccount(AccountCosmos accountCosmos) {
    try {
      Accounts account = new Accounts(accountsMapper().map(accountCosmos));
      if (account.getAccountDocument().getGoldenRecordId() == null) {
        log.warn("Missing golden record id! Skipping account: {}", account);
      }
      account.setDeleted(accountCosmos.isDeleted());
      account.setLocalId(account.getAccountDocument().getId().toString());
      return Mono.just(account);
    } catch (Exception e) {
      return Mono.error(
          new MigrationException("There was an error fetching an account from CosmosDB", e));
    }
  }

  private Mono<Accounts> processAccount(Accounts input) {
    return Mono.fromCallable(
            () -> {
              try {
                Accounts existingAccount =
                    postgresAccountsRepository.findByAccountId(
                        input.getAccountDocument().getId().toString());
                return Objects.requireNonNullElseGet(
                    existingAccount, () -> postgresAccountsRepository.save(input));
              } catch (Exception e) {
                throw new MigrationException("Error during account process", e);
              }
            })
        .subscribeOn(Schedulers.boundedElastic())
        .onErrorResume(
            t -> {
              log.error("Error during account process", t);
              return Mono.error(t);
            });
  }

  private List<Accounts> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    try {
      Iterator<AccountCosmos> allAccountsIterator = cosmosRepository.findAll().iterator();
      List<Accounts> accountsList = new ArrayList<>();
      int failures = 0;
      while (allAccountsIterator.hasNext()) {
        try {
          Accounts account = new Accounts(accountsMapper().map(allAccountsIterator.next()));
          if (account.getAccountDocument().getGoldenRecordId() == null) {
            log.warn("Missing golden record id! Skipping account: {}", account);
            continue;
          }
          account.setLocalId(account.getAccountDocument().getId().toString());
          accountsList.add(account);
        } catch (Exception e) {
          log.error("There was an error fetching an account from CosmosDB", e);
          failures++;
        }
      }
      log.info("{} accounts fetched from CosmosDB", accountsList.size());
      if (failures > 0) {
        log.warn("{} accounts failed to map during the fetching process", failures);
      }
      migrationResult.setSucceeded(accountsList.size());
      migrationResult.setFailed(failures);
      return accountsList;
    } catch (Exception e) {
      log.error("Error in accounts migration", e);
      throw e;
    }
  }

  private CosmosToModelMapper<AccountCosmos, AccountDocument> accountsMapper() {
    return source ->
        AccountDocument.builder()
            .id(UUID.fromString(source.getId()))
            .goldenRecordId(source.getGoldenRecordId())
            .accountName(source.getAccountName())
            .accountType(source.getAccountType())
            .customerCode(source.getCustomerCode())
            .isFavourite(source.isFavourite())
            .accountNumber(source.getAccountNumber())
            .accountStatus(source.getAccountStatus())
            .users(combineUsers(source))
            .userRoles(source.getUserRoles())
            .accountCurrency(source.getAccountCurrency())
            .accountValidated(source.getAccountValidated())
            .segmentStepOneId(processSegmentStepOneId(source.getSegmentStepOneId()))
            .subTypeID(processSubTypeId(source))
            .active(source.getActive())
            .additionalInfo(
                source.getAdditionalInfo() == null
                    ? null
                    : additionalInfoMapper.map(source.getAdditionalInfo()))
            .brandId(source.getBrandId())
            .assets(source.getAssets())
            .autoValidate(source.getAutoValidate())
            .approvalStatus(source.getApprovalStatus())
            .businessID(source.getBusinessID())
            .changeAccountType(source.getChangeAccountType())
            .contacts(
                source.getContacts() == null
                    ? null
                    : source.getContacts().stream().map(contactMapper::map).toList())
            .businessSolutionFlag(source.getBusinessSolutionFlag())
            .buyingGroupID(source.getBuyingGroupID())
            .courtId(source.getCourtId())
            .companyEmail(source.getCompanyEmail())
            .consumerStatus(source.getConsumerStatus())
            .creditFlag(source.getCreditFlag())
            .correspondenceAddress(
                source.getCorrespondenceAddress() == null
                    ? null
                    : addressMapper.map(source.getCorrespondenceAddress()))
            .availabilityOnMarket(source.getAvailabilityOnMarket())
            .customerStatus(source.getCustomerStatus())
            .dateOfLastCall(source.getDateOfLastCall())
            .dateOfLastVisit(source.getDateOfLastVisit())
            .description(source.getDescription())
            .erpIdLength(source.getErpIdLength())
            .defaultCargillPlantID(source.getDefaultCargillPlantID())
            .defaultCustServiceID(source.getDefaultCustServiceID())
            .erpPayerId(source.getErpPayerId())
            .deliveryInstructions(source.getDeliveryInstructions())
            .erpIdLengthValidatorIsError(source.getErpIdLengthValidatorIsError())
            .erpPayerIdLength(source.getErpPayerIdLength())
            .erpShipToId(source.getErpShipToId())
            .erpShiptoIdLength(source.getErpShiptoIdLength())
            .isDuplicate(source.getIsDuplicate())
            .externalParentAccountID(
                UuidMapper.getNullableUuid(source.getExternalParentAccountId()))
            .externalBuyingGroupID(UuidMapper.getNullableUuid(source.getExternalBuyingGroupID()))
            .externalLeadSourceID(source.getExternalLeadSourceID())
            .isMobileFirst(source.getIsMobileFirst())
            .isServicedbyCSPro(source.getIsServicedbyCSPro())
            .lastAdminUpdate(source.getLastAdminUpdate())
            .lastInvoiceDate(source.getLastInvoiceDate())
            .lastInvoicesInfo(source.getLastInvoicesInfo())
            .lastModificationDate(source.getLastModificationDate())
            .lastModifiedBy(source.getLastModifiedBy())
            .legalName(source.getLegalName())
            .lastOrderDate(source.getLastOrderDate())
            .additionalInfo(
                source.getAdditionalInfo() == null
                    ? null
                    : additionalInfoMapper.map(source.getAdditionalInfo()))
            .lastOrdersInfo(source.getLastOrdersInfo())
            .lstOtherBU(source.getLstOtherBU())
            .lastUpdateUserId(source.getLastUpdateUserId())
            .liabilities(source.getLiabilities())
            .needsSync(false)
            .newAccountType(source.getNewAccountType())
            .limitChangeReasonId(source.getLimitChangeReasonId())
            .marginEstimate(
                source.getMarginEstimate() == null
                    ? null
                    : new BigInteger(source.getMarginEstimate()))
            .otherFlag(source.getOtherFlag())
            .ownerId(source.getOwnerId())
            .parentAccountID(source.getParentAccountID())
            .marketInfluencer(source.getMarketInfluencer())
            .phone(source.getPhone())
            .nineBoxStepTwoID(source.getNineBoxStepTwoID())
            .photoId(source.getPhotoId())
            .personalID(source.getPersonalID())
            .otherActivityProduction(source.getOtherActivityProduction())
            .ownerProfileNameandId(source.getOwnerProfileNameandId())
            .performanceFlag(source.getPerformanceFlag())
            .physicalAddress(
                source.getPhysicalAddress() == null
                    ? null
                    : addressMapper.map(source.getPhysicalAddress()))
            .portfolioFlag(source.getPortfolioFlag())
            .previousStatus(source.getPreviousStatus())
            .priceFlag(source.getPriceFlag())
            .primaryContactId(UUID.fromString(source.getPrimaryContactId()))
            .qualityFlag(source.getQualityFlag())
            .primaryContactPhoneNumber(source.getPrimaryContactPhoneNumber())
            .primaryContactTitle(source.getPrimaryContactTitle())
            .prospectStatus(source.getProspectStatus())
            .securities(source.getSecurities())
            .reasonDescription(source.getReasonDescription())
            .reqProcessingLog(source.getReqProcessingLog())
            .type(source.getType())
            .salesTerritory(source.getSalesTerritory())
            .serviceFlag(source.getServiceFlag())
            .subBrandId(source.getSubBrandId())
            .socialMediaAddress(source.getSocialMediaAddress())
            .sourceSystem(source.getSourceSystem())
            .veterinaryId(source.getVeterinaryId())
            .volumeEstimate(
                source.getVolumeEstimate() == null
                    ? null
                    : new BigInteger(source.getVolumeEstimate()))
            .webSiteAddress(source.getWebSiteAddress())
            .wonLostId(source.getWonLostId())
            .wonLostComments(source.getWonLostComments())
            .wonLost(source.getWonLost())
            .wonLostReasonCode(source.getWonLostReasonCode())
            .currentUserProfileNameandId(source.getCurrentUserProfileNameandId())
            .createTimeUtc(source.getCreateTimeUtc())
            .lastModifiedTimeUtc(Instant.ofEpochSecond(source.getLastModifiedTimeUtc().getEpoch()))
            .lastSyncTimeUtc(source.getLastSyncTimeUtc())
            .isNew(source.isNew())
            .build();
  }

  private String processSegmentStepOneId(String segmentStepOneId) {
    if (Objects.isNull(segmentStepOneId)) {
      return null;
    }
    // 0 means not set, we provide the default value then which is Noah as per the business team.
    if (segmentStepOneId.equalsIgnoreCase("0")) {
      return segmentsRepository.findValueByDefaultValue();
    }
    return segmentsRepository.findValueByKey(segmentStepOneId);
  }

  private SubTypeId processSubTypeId(AccountCosmos source) {
    return source.getSubTypeID() != null
        ? SubTypeId.fromId(source.getSubTypeID())
        : SubTypeId.FarmProducer;
  }

  private Set<String> combineUsers(AccountCosmos accountCosmos) {
    HashSet<String> users = new HashSet<>();
    if (accountCosmos.getUsers() != null) {
      users.addAll(accountCosmos.getUsers());
    }
    users.add(accountCosmos.getOwnerId());
    return users;
  }
}
