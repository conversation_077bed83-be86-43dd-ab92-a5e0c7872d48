/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class ManureScreenerToolItemCosmos extends EditableToolPenEntityBaseCosmos {
  @JsonProperty("VisitsSelected")
  private List<String> visitsSelected;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("MSTScoreId")
  private String mstScoreId;

  @JsonProperty("TopScaleAmountInGrams")
  private Double topScaleAmountInGrams;

  @JsonProperty("TopGoalMinimumPercent")
  private Double topGoalMinimumPercent;

  @JsonProperty("TopGoalMaximumPercent")
  private Double topGoalMaximumPercent;

  @JsonProperty("MidScaleAmountInGrams")
  private Double midScaleAmountInGrams;

  @JsonProperty("MidGoalMinimumPercent")
  private Double midGoalMinimumPercent;

  @JsonProperty("MidGoalMaximumPercent")
  private Double midGoalMaximumPercent;

  @JsonProperty("BottomScaleAmountInGrams")
  private Double bottomScaleAmountInGrams;

  @JsonProperty("BottomGoalMinimumPercent")
  private Double bottomGoalMinimumPercent;

  @JsonProperty("BottomGoalMaximumPercent")
  private Double bottomGoalMaximumPercent;

  @JsonProperty("MSTScoreName")
  private String mstScoreName;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;

  @JsonProperty("Observation")
  private String observation;
}
