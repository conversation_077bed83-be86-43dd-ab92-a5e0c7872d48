/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller.admin;

import com.app.cargill.dto.admin.User;
import com.app.cargill.service.admin.UserDetailsAdminService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/user-details")
@Tag(name = "User details controller")
@RequiredArgsConstructor
public class UserDetailsAdminController {
  private final UserDetailsAdminService userDetailsAdminService;

  @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
  @PreAuthorize(("authentication.name == @securityConfiguration.getApiAdminId()"))
  public User getUserDetails(@PathVariable("id") UUID id) {
    return userDetailsAdminService.getUserDetails(id);
  }
}
