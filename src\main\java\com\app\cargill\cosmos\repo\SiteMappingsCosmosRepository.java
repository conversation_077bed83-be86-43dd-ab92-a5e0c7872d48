/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.SiteMappingCosmos;
import com.azure.spring.data.cosmos.repository.Query;
import com.azure.spring.data.cosmos.repository.ReactiveCosmosRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface SiteMappingsCosmosRepository
    extends ReactiveCosmosRepository<SiteMappingCosmos, String> {
  @Query("Select * from sm")
  Flux<SiteMappingCosmos> findAllSiteMappings();

  @Query("Select * from c where c.LabyrinthSiteId = @siteId")
  Flux<SiteMappingCosmos> findAllBySiteId(@Param("siteId") String siteId);
}
