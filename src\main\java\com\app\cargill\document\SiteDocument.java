/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.MilkingSystem;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
// List is not Serializable but ArrayList is
// It is resolved in runtime, so this should not be an issue as long as we use Serializable List(s)
@SuppressWarnings("java:S1948")
public class SiteDocument extends EditableDocumentBase {

  @JsonProperty("AccountId")
  public UUID accountId;

  @JsonProperty("ExternalId")
  public String externalId;

  @JsonProperty("ExternalAccountId")
  public String externalAccountId;

  @JsonProperty("SiteName")
  public String siteName;

  @JsonProperty("CurrentMilkPrice")
  public Double currentMilkPrice;

  @JsonProperty("DaysInMilk")
  public Integer daysInMilk;

  @JsonProperty("MilkingSystemType")
  public MilkingSystem milkingSystemType;

  @JsonProperty("DryMatterIntake")
  public Double dryMatterIntake;

  @JsonProperty("LactatingAnimal")
  public Integer lactatingAnimal;

  @JsonProperty("Milk")
  public Double milk;

  @JsonProperty("MilkFatPercent")
  public Double milkFatPercent;

  @JsonProperty("MilkProteinPercent")
  public Double milkProteinPercent;

  @JsonProperty("MilkOtherSolidsPercent")
  public Double milkOtherSolidsPercent;

  @JsonProperty("MilkSomaticCellCount")
  public Integer milkSomaticCellCount;

  @JsonProperty("BacteriaCellCount")
  public Integer bacteriaCellCount;

  @JsonProperty("NetEnergyOfLactationDairy")
  public Double netEnergyOfLactationDairy;

  @JsonProperty("RationCost")
  public Double rationCost;

  @JsonProperty("Barns")
  @Builder.Default
  public List<Barn> barns = new ArrayList<>();

  @JsonProperty("Visits")
  @Builder.Default
  public List<SiteVisit> visits = new ArrayList<>();

  @JsonProperty("AsFedIntake")
  public Double asFedIntake;

  @JsonProperty("SiteMappings")
  @Builder.Default
  public List<DataSourceMapping> dataSourceMappings = new ArrayList<>();

  @JsonProperty("Origination")
  @Builder.Default
  public String origination = "LM_SITE";

  @JsonProperty("DateOfLastVisit")
  public Instant dateOfLastVisit;

  @JsonProperty("DDWLastUpdatedDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant dDWLastUpdatedDate;

  @JsonProperty("NumberOfParlorStalls")
  public Integer numberOfParlorStalls;

  @JsonProperty("NeedsSync")
  private Boolean needsSync;

  @JsonProperty("DataSource")
  @Builder.Default
  private DataSource dataSource = DataSource.UNKNOWN;

  @JsonProperty("Keys")
  @Builder.Default
  private Map<String, String> keys = new HashMap<>();

  @JsonProperty("HerdStatusReport")
  private String herdStatusReport;

  @JsonProperty("HerdSummaryReport")
  private String herdSummaryReport;

  @JsonProperty("HasReport")
  private Boolean hasReport;
}
