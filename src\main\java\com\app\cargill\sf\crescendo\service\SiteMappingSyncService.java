/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SiteMappingSyncService {

  private final SiteMappingsRepository siteMappingsRepository;
  private final SitesRepository sitesRepository;

  public Accounts processMappings(Accounts a) {
    try {
      List<SiteMappings> existingSiteMappings =
          siteMappingsRepository.findAllByLabyrinthAccountId(
              a.getAccountDocument().getId().toString());
      List<SiteMappings> updatedMappings =
          calculateMappings(
              a.getAccountDocument().getId(),
              existingSiteMappings,
              a.getAccountDocument().getApplicationMappings());
      siteMappingsRepository.saveAll(updatedMappings);
      updateSiteMappings(updatedMappings);

    } catch (Exception e) {
      log.error("CRESCENDO_SITE_MAPPING_ERROR", e);
    }
    return a;
  }

  private List<SiteMappings> calculateMappings(
      UUID accountId,
      List<SiteMappings> existingSiteMappings,
      List<ExternalDataSource> externalDataSources) {
    boolean dcgoflag = false;
    boolean ddwflag = false;
    boolean mpflag = false;
    boolean maxflag = false;
    for (ExternalDataSource eds : externalDataSources) {
      SiteMappings siteMapping =
          existingSiteMappings.stream()
              .filter(
                  sm ->
                      sm.getSiteMappingDocument()
                          .getLabyrinthSiteId()
                          .toString()
                          .equals(eds.getSiteId()))
              .findAny()
              .orElseGet(
                  () -> {
                    SiteMappingDocument siteMappingDocument = new SiteMappingDocument();
                    siteMappingDocument.setId(UUID.randomUUID());
                    siteMappingDocument.setLabyrinthSiteId(UUID.fromString(eds.getSiteId()));
                    siteMappingDocument.setLabyrinthAccountId(accountId);
                    SiteMappings sm = new SiteMappings(siteMappingDocument);
                    existingSiteMappings.add(sm);
                    return sm;
                  });

      if (siteMapping.getSiteMappingDocument().getId() == null) {
        siteMapping.getSiteMappingDocument().setId(UUID.randomUUID());
      }
      if (ApplicationMapping.DDW_SYSTEM_NAME.equals(eds.getSystem())) {
        siteMapping.getSiteMappingDocument().setDdwHerdId(eds.getUniqueExternalKey());
        ddwflag = true;
      } else if (ApplicationMapping.MAX_SYSTEM_NAME.equals(eds.getSystem())) {
        siteMapping
            .getSiteMappingDocument()
            .setMaxSiteId(UUID.fromString(eds.getUniqueExternalKey()));
        maxflag = true;
      } else if (ApplicationMapping.DCGO_SYSTEM_NAME.equals(eds.getSystem())) {
        siteMapping.getSiteMappingDocument().setDcgoId(eds.getUniqueExternalKey());
        dcgoflag = true;
      } else if (ApplicationMapping.MP_SYSTEM_NAME.equals(eds.getSystem())) {
        siteMapping.getSiteMappingDocument().setMilkProcessorId(eds.getUniqueExternalKey());
        mpflag = true;
      }

      checkDCGO(siteMapping, dcgoflag, eds);
      checkDDW(siteMapping, ddwflag, eds);
      checkMP(siteMapping, mpflag, eds);
      checkMAX(siteMapping, maxflag, eds);
    }

    return existingSiteMappings;
  }

  private void updateSiteMappings(List<SiteMappings> siteMappings) {
    for (SiteMappings siteMapping : siteMappings) {
      Sites site =
          sitesRepository.findBySiteId(
              siteMapping.getSiteMappingDocument().getLabyrinthSiteId().toString());
      if (site != null) {
        SiteMappingDocument smd = siteMapping.getSiteMappingDocument();
        List<DataSourceMapping> dsm = constructDsm(smd);
        site.getSiteDocument().setDataSourceMappings(dsm);
        site.getSiteDocument()
            .setHasReport(siteMapping.getSiteMappingDocument().getDdwHerdId() != null);
        sitesRepository.save(site);
      }
    }
  }

  private List<DataSourceMapping> constructDsm(SiteMappingDocument smd) {
    List<DataSourceMapping> dsm = new ArrayList<>();
    if (smd.getLabyrinthSiteId() != null) {
      dsm.add(
          DataSourceMapping.builder()
              .systemName(ApplicationMapping.LM_SITE_SYSTEM_NAME)
              .systemId(smd.getLabyrinthSiteId().toString())
              .build());
    }

    if (smd.getDdwHerdId() != null) {
      dsm.add(
          DataSourceMapping.builder()
              .systemName(ApplicationMapping.DDW_SYSTEM_NAME)
              .systemId(smd.getDdwHerdId())
              .build());
    }

    if (smd.getMaxSiteId() != null) {
      dsm.add(
          DataSourceMapping.builder()
              .systemName(ApplicationMapping.MAX_SYSTEM_NAME)
              .systemId(smd.getMaxSiteId().toString())
              .build());
    }

    if (smd.getMilkProcessorId() != null) {
      dsm.add(
          DataSourceMapping.builder()
              .systemName(ApplicationMapping.MP_SYSTEM_NAME)
              .systemId(smd.getMilkProcessorId())
              .build());
    }

    if (smd.getDcgoId() != null) {
      dsm.add(
          DataSourceMapping.builder()
              .systemName(ApplicationMapping.DCGO_SYSTEM_NAME)
              .systemId(smd.getDcgoId())
              .build());
    }
    return dsm;
  }

  public void checkDCGO(SiteMappings siteMappings, boolean dcgoflag, ExternalDataSource eds) {
    if (siteMappings.getSiteMappingDocument().getDcgoId() != null
        && !eds.getSystem().equals(ApplicationMapping.DCGO_SYSTEM_NAME)
        && !dcgoflag) {
      // check if eds also contains ddw mapping or else delete it from siteMappings
      siteMappings.getSiteMappingDocument().setDcgoId(null);
    }
  }

  public void checkDDW(SiteMappings siteMappings, boolean ddwflag, ExternalDataSource eds) {
    if (siteMappings.getSiteMappingDocument().getDdwHerdId() != null
        && null != eds.getSystem()
        && !eds.getSystem().equals(ApplicationMapping.DDW_SYSTEM_NAME)
        && !ddwflag) {
      // check if eds also contains ddw mapping or else delete it from siteMappings

      siteMappings.getSiteMappingDocument().setDdwHerdId(null);
    }
  }

  public void checkMP(SiteMappings siteMappings, boolean mpflag, ExternalDataSource eds) {
    if (siteMappings.getSiteMappingDocument().getMilkProcessorId() != null
        && !eds.getSystem().equals(ApplicationMapping.MP_SYSTEM_NAME)
        && !mpflag) {
      // check if eds also contains ddw mapping or else delete it from siteMappings

      siteMappings.getSiteMappingDocument().setMilkProcessorId(null);
    }
  }

  public void checkMAX(SiteMappings siteMappings, boolean maxflag, ExternalDataSource eds) {
    if (siteMappings.getSiteMappingDocument().getMaxSiteId() != null
        && !eds.getSystem().equals(ApplicationMapping.MAX_SYSTEM_NAME)
        && !maxflag) {
      // check if eds also contains ddw mapping or else delete it from siteMappings

      siteMappings.getSiteMappingDocument().setMaxSiteId(null);
    }
  }
}
