/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.app.cargill.constants.Business;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BusinessIdDeserializer extends JsonDeserializer<Integer> {

  @Override
  public Integer deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    try {
      return Integer.parseInt(node.asText());
    } catch (IllegalArgumentException e) {
      if (node.isNull()) {
        return Business.Global.getBusinessId();
      }
      if ("United States".equals(node.asText())) {
        return Business.US.getBusinessId();
      }
      try {
        return Business.valueOf(node.asText()).getBusinessId();
      } catch (IllegalArgumentException ex) {
        log.warn("Unknown BusinessID: {}", node.asText());
      }
      return null;
    }
  }
}
