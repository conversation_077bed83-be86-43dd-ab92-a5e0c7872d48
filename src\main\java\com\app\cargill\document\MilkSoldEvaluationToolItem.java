/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.MilkPickup;
import com.app.cargill.constants.MilkUreaMeasure;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkSoldEvaluationToolItem implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Outputs")
  private MilkSoldEvaluationToolOutputToolItem outputs;

  @JsonProperty("Pickups")
  private List<MilkSoldMilkProcessorToolItem> pickups;

  @JsonProperty("SelectedVisits")
  private List<UUID> selectedVisits;

  @JsonProperty("LactatingAnimals")
  private Integer lactatingAnimals;

  @JsonProperty("AsFedIntake")
  private Double asFedIntake;

  @JsonProperty("NetEnergyOfLactationDairy")
  private Double netEnergyOfLactationDairy;

  @JsonProperty("RationCost")
  private Double rationCost;

  @JsonProperty("CurrentMilkPrice")
  private Double currentMilkPrice;

  @JsonProperty("AnimalsinTank")
  private Integer animalsinTank;

  @JsonProperty("MilkPickup")
  private MilkPickup milkPickup;

  @JsonProperty("DryMatterIntake")
  private Double dryMatterIntake;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("MilkUreaMeasure")
  private MilkUreaMeasure milkUreaMeasure;
}
