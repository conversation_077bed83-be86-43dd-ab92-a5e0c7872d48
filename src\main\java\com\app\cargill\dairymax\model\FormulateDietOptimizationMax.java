/* Cargill Inc.(C) 2022 */
package com.app.cargill.dairymax.model;

import com.app.cargill.dairymax.constants.FormulateStatusMax;
import com.app.cargill.document.DietOptimization;
import com.app.cargill.document.NelDaiKg;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class FormulateDietOptimizationMax extends DietOptimization {
  @JsonProperty("Status")
  public FormulateStatusMax status;
  // Max Ehnc
  @JsonProperty("NELDAIKG")
  public NelDaiKg nELDAIKG;

  @JsonProperty("AsFedAmount")
  public Double asFedAmount;

  @JsonProperty("DryMatterAmount")
  public Double dryMatterAmount;

  @JsonProperty("TotalCost")
  public Double totalCost;

  @JsonProperty("OptimizationId")
  public Integer optimizationId;
}
