/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScorecardSilage implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Silage type.
  /// </summary>
  /// <value>Type of the silage.</value>
  /// <remarks>This will differentiate section silage type.</remarks>
  @JsonProperty("SectionIndex")
  private Integer sectionIndex;
  /// <summary>
  /// Silage type.
  /// </summary>
  /// <value>Type of the silage.</value>
  /// <remarks>This will differentiate section silage type.</remarks>
  @JsonProperty("SectionSilageType")
  private Integer sectionSilageType;
  /// <summary>
  /// The name of the silage type to display to the user.
  /// </summary>
  /// <value>The name of the silage type.</value>
  /// <remarks>This will be a key to the string resources file.</remarks>
  @JsonProperty("SilageTypeName")
  private String silageTypeName;
  /// <summary>
  /// A set of questions that make up this section of a scorecard.
  /// </summary>
  /// <value>The questions.</value>
  @JsonProperty("Questions")
  private List<ScorecardQuestion> questions;
  /// <summary>
  /// Indicates if this sections score should be included in the overall scorecard
  /// score
  /// </summary>
  /// <value><c>true</c> if include in overall forage score; otherwise,
  /// <c>false</c>.</value>
  @JsonProperty("IncludeInOverallForageScore")
  private Boolean includeInOverallForageScore;

  @JsonProperty("Status")
  private ToolStatuses status;

  @JsonProperty("AnswersPositivePercent")
  private Double answersPositivePercent;

  @JsonProperty("AnswersNegativePercent")
  private Double answersNegativePercent;
}
