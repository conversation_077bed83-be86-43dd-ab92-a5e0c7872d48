/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.SilageType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ForagePennStateToolGoalItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Silage")
  private SilageType silage;

  @JsonProperty("SilageId")
  private UUID silageId;

  @JsonProperty("SilageRange")
  private String silageRange;

  @JsonProperty("GoalMax")
  private Double goalMax;

  @JsonProperty("GoalMin")
  private Double goalMin;
}
