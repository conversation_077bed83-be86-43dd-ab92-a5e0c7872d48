/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.ManureScreenerScoreGoalToolItemCosmos;
import com.app.cargill.cosmos.model.tools.ManureScreenerToolCosmos;
import com.app.cargill.cosmos.model.tools.ManureScreenerToolItemCosmos;
import com.app.cargill.document.ManureScreenerScoreGoalToolItem;
import com.app.cargill.document.ManureScreenerTool;
import com.app.cargill.document.ManureScreenerToolItem;
import java.util.UUID;

public class ManureScreenerToolMapper {

  private ManureScreenerToolMapper() {}

  public static ManureScreenerTool map(ManureScreenerToolCosmos input) {

    return manureScreenerToolMapper(manureScreenerToolItemMapper()).map(input);
  }

  private static CosmosToModelMapper<ManureScreenerToolItemCosmos, ManureScreenerToolItem>
      manureScreenerToolItemMapper() {
    return source ->
        ManureScreenerToolItem.builder()
            .createTimeUtc(source.getCreateTimeUtc())
            .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
            .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
            .penName(source.getPenName())
            .visitsSelected(
                source.getVisitsSelected() != null
                    ? source.getVisitsSelected().stream().map(UUID::fromString).toList()
                    : null)
            .isToolItemNew(source.getIsToolItemNew())
            .mstScoreId(
                source.getMstScoreId() != null ? UUID.fromString(source.getMstScoreId()) : null)
            .topScaleAmountInGrams(source.getTopScaleAmountInGrams())
            .topGoalMinimumPercent(source.getTopGoalMinimumPercent())
            .topGoalMaximumPercent(source.getTopGoalMaximumPercent())
            .midScaleAmountInGrams(source.getMidScaleAmountInGrams())
            .midGoalMinimumPercent(source.getMidGoalMinimumPercent())
            .midGoalMaximumPercent(source.getMidGoalMaximumPercent())
            .bottomScaleAmountInGrams(source.getBottomScaleAmountInGrams())
            .bottomGoalMinimumPercent(source.getBottomGoalMinimumPercent())
            .bottomGoalMaximumPercent(source.getBottomGoalMaximumPercent())
            .mstScoreName(source.getMstScoreName())
            .toolStatus(source.getToolStatus())
            .isFirstTimeWithScore(source.getIsFirstTimeWithScore())
            .observation(source.getObservation())
            .build();
  }

  @SuppressWarnings("java:S3252") // false positive when using @SuperBuilder
  private static CosmosToModelMapper<ManureScreenerToolCosmos, ManureScreenerTool>
      manureScreenerToolMapper(
          CosmosToModelMapper<ManureScreenerToolItemCosmos, ManureScreenerToolItem> itemMapper) {

    return source ->
        ManureScreenerTool.builder()
            .id(UUID.fromString(source.getId()))
            .createUser(source.getCreateUser())
            .isDeleted(source.isDeleted())
            .lastModifyUser(source.getLastModifyUser())
            .createTimeUtc(source.getCreateTimeUtc())
            .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
            .lastSyncTimeUtc(source.getLastSyncTimeUtc())
            .isNew(source.isNew())
            .visitId(UUID.fromString(source.getVisitId()))
            .mstScores(
                source.getMstScores() != null
                    ? source.getMstScores().stream().map(itemMapper::map).toList()
                    : null)
            .mstGoal(
                source.getMstGoal() != null
                    ? manureScreenerScoreGoalMapper().map(source.getMstGoal())
                    : null)
            .observation(source.getObservation())
            .build();
  }

  private static CosmosToModelMapper<
          ManureScreenerScoreGoalToolItemCosmos, ManureScreenerScoreGoalToolItem>
      manureScreenerScoreGoalMapper() {
    return source ->
        ManureScreenerScoreGoalToolItem.builder()
            .visitsSelected(
                source.getVisitsSelected() != null
                    ? source.getVisitsSelected().stream().map(UUID::fromString).toList()
                    : null)
            .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
            .penName(source.getPenName())
            .isToolItemNew(source.getIsToolItemNew())
            .goalTitle(source.getGoalTitle())
            .topGoalMinimumPercent(source.getTopGoalMinimumPercent())
            .topGoalMaximumPercent(source.getTopGoalMaximumPercent())
            .midGoalMinimumPercent(source.getMidGoalMinimumPercent())
            .midGoalMaximumPercent(source.getMidGoalMaximumPercent())
            .bottomGoalMinimumPercent(source.getBottomGoalMinimumPercent())
            .bottomGoalMaximumPercent(source.getBottomGoalMaximumPercent())
            .toolStatus(source.getToolStatus())
            .build();
  }
}
