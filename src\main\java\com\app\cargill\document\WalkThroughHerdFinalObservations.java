/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class WalkThroughHerdFinalObservations extends EditableDocumentBase {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("PositiveTrend")
  public String positiveTrend;

  @JsonProperty("Opportunities")
  public String opportunities;

  @JsonProperty("Comment")
  public String comment;
}
