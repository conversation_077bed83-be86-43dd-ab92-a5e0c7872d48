/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.DietSource;
import com.app.cargill.dairymax.model.MaxDiet;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DietDocument implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private UUID id;

  @JsonProperty("LabyrinthAccountId")
  private UUID labyrinthAccountId;

  @JsonProperty("SiteId")
  private UUID siteId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("BreedName")
  private String breedName;

  @JsonProperty("BreedId")
  private UUID breedId;

  @JsonProperty("StartDate")
  private Instant startDate;

  @JsonProperty("EndDate")
  private Instant endDate;

  @JsonProperty("AnimalType")
  private AnimalClass animalType;

  @JsonProperty("BarnId")
  private UUID barnId;

  @JsonProperty("EnvironmentId")
  private UUID environmentId;

  @JsonProperty("EnvironmentName")
  private String environmentName;

  @JsonProperty("ReportMilkWeight")
  private Double reportMilkWeight;

  @JsonProperty("NumberOfAnimals")
  private Integer numberOfAnimals;

  @JsonProperty("AnalyzeOptimization")
  private AnalyzeDietOptimization analyzeOptimization;

  @JsonProperty("FormulateOptimization")
  private FormulateDietOptimization formulateOptimization;

  @JsonProperty("Source")
  private DietSource source;

  @JsonProperty("Selected")
  @Builder.Default
  private Boolean selected = false;

  @JsonProperty("SelectedPenGuids")
  private List<UUID> selectedPenGuids;

  /** Added this field for checking it for Animalclass and Subclasss and diet */
  @JsonProperty("IsSystemGenerated")
  @Builder.Default
  private Boolean isSystemGenerated = false;

  @JsonProperty("IsDeleted")
  @Builder.Default
  private Boolean isDeleted = false;

  @JsonProperty("IsActive")
  private Boolean isActive;

  @JsonProperty("OptimizationId")
  private Integer optimizationId;

  @JsonProperty("OptimizationType")
  private String optimizationType;

  @JsonProperty("OptimizationStatus")
  private String optimizationStatus;

  @JsonProperty("CreateUser")
  private String createUser;

  public DietDocument(MaxDiet diet) {
    this.analyzeOptimization =
        !Objects.isNull(diet.getMaxAnalyzeOptimization())
            ? new AnalyzeDietOptimization(diet.getMaxAnalyzeOptimization())
            : null;
    this.animalType = diet.getMaxAnimalType();
    this.barnId = diet.getMaxBarnId();
    this.breedId = diet.getMaxBreedId();
    this.breedName = diet.getMaxBreedName();
    this.createUser = diet.getMaxCreateUser();
    this.endDate = diet.getMaxEndDate();
    this.environmentName = diet.getMaxEnvironmentName();
    this.formulateOptimization =
        !Objects.isNull(diet.getMaxFormulateOptimization())
            ? new FormulateDietOptimization(diet.getMaxFormulateOptimization())
            : null;
    this.id = diet.getMaxId();
    this.isActive = diet.getMaxSelected();
    this.isDeleted = diet.getMaxIsDeleted();
    this.isSystemGenerated = diet.getMaxIsSystemGenerated();
    this.labyrinthAccountId = diet.getMaxLabyrinthAccountId();
    this.name = diet.getMaxName();
    this.numberOfAnimals = diet.getMaxNumberOfAnimals();
    this.optimizationId = diet.getMaxOptimizationId();
    this.optimizationStatus = diet.getMaxOptimizationStatus();
    this.optimizationType = diet.getMaxOptimizationType();
    this.reportMilkWeight = diet.getMaxReportMilkWeight();
    this.selected = diet.getMaxSelected();
    this.selectedPenGuids = diet.getMaxSelectedPenGuids();
    this.siteId = diet.getMaxSiteId();
    this.source = diet.getMaxSource();
    this.startDate = diet.getMaxStartDate();
    this.environmentId = diet.getMaxEnvironmentId();
  }

  public DietDocument(DietDocument diet) {
    this.analyzeOptimization = diet.getAnalyzeOptimization();
    this.animalType = diet.getAnimalType();
    this.barnId = diet.getBarnId();
    this.breedId = diet.getBreedId();
    this.breedName = diet.getBreedName();
    this.createUser = diet.getCreateUser();
    this.endDate = diet.getEndDate();
    this.environmentName = diet.getEnvironmentName();
    this.formulateOptimization = diet.getFormulateOptimization();
    this.id = diet.getId();
    this.isActive = diet.getIsActive();
    this.isDeleted = diet.getIsDeleted();
    this.isSystemGenerated = diet.getIsSystemGenerated();
    this.labyrinthAccountId = diet.getLabyrinthAccountId();
    this.name = diet.getName();
    this.numberOfAnimals = diet.getNumberOfAnimals();
    this.optimizationId = diet.getOptimizationId();
    this.optimizationStatus = diet.getOptimizationStatus();
    this.optimizationType = diet.getOptimizationType();
    this.reportMilkWeight = diet.getReportMilkWeight();
    this.selected = diet.getSelected();
    this.selectedPenGuids = diet.getSelectedPenGuids();
    this.siteId = diet.getSiteId();
    this.source = diet.getSource();
    this.startDate = diet.getStartDate();
    this.environmentId = diet.getEnvironmentId();
  }

  public DietDocument(UUID dietId, String dietName, DietSource dietSource, AnimalClass animalType) {
    this.id = dietId;
    this.name = dietName;
    this.source = dietSource;
    this.animalType = animalType;
  }

  public DietDocument(UUID id, String name, DietSource source) {
    this.name = name;
    this.id = id;
    this.source = source;
  }
}
