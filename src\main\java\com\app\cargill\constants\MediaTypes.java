/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import java.util.Arrays;

@SuppressWarnings({"java:S115"})
public enum MediaTypes {
  Photo(0),
  Video(1),
  PDF(2),
  FreeHandNote(3),
  VoiceNotes(4);
  private final Integer mediaTypeValue;

  MediaTypes(Integer code) {
    this.mediaTypeValue = code;
  }

  public Integer getMediaTypeValue() {
    return mediaTypeValue;
  }

  public static MediaTypes fromId(Integer id) {
    if (id == null) {
      return null;
    }
    return Arrays.stream(MediaTypes.values())
        .filter(type -> type.getMediaTypeValue().intValue() == id.intValue())
        .findFirst()
        .orElse(null);
  }
}
