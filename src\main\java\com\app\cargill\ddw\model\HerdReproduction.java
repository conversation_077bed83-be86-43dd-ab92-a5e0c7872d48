/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdReproduction extends HerdBase {

  @JsonProperty("AvgDIMFirstHeat")
  private String avgDIMFirstHeat;

  @JsonProperty("AvgDIMFirstIns")
  private String avgDIMFirstIns;

  @JsonProperty("AvgHeatInseminationInterval")
  private String avgHeatInseminationInterval;

  @JsonProperty("CR1")
  private String cR1;

  @JsonProperty("CR1Incomplete")
  private Boolean cR1Incomplete;

  @JsonProperty("CR2")
  private String cR2;

  @JsonProperty("CR2Incomplete")
  private Boolean cR2Incomplete;

  @JsonProperty("CR3Plus")
  private String cR3Plus;

  @JsonProperty("CR3PlusIncomplete")
  private Boolean cR3PlusIncomplete;

  @JsonProperty("ConceptionRisk")
  private String conceptionRisk;

  @JsonProperty("DaysFirstInsToPreg")
  private String daysFirstInsToPreg;

  @JsonProperty("DaysToPregnancy")
  private String daysToPregnancy;

  @JsonProperty("ExpectedCalvingInterval")
  private String expectedCalvingInterval;

  @JsonProperty("HeatDetectionRate")
  private String heatDetectionRate;

  @JsonProperty("HistoricalCalvingInterval")
  private String historicalCalvingInterval;

  @JsonProperty("InseminationRisk")
  private String inseminationRisk;

  @JsonProperty("NrCalvings")
  private String nrCalvings;

  @JsonProperty("NrCalvingsCows")
  private String nrCalvingsCows;

  @JsonProperty("NrCalvingsHeifers")
  private String nrCalvingsHeifers;

  @JsonProperty("NrCowsPregnantPeriodEnd")
  private String nrCowsPregnantPeriodEnd;

  @JsonProperty("NrIns")
  private String nrIns;

  @JsonProperty("NrInsPerInsCow")
  private String nrInsPerInsCow;

  @JsonProperty("NrInsPerPregnancy")
  private String nrInsPerPregnancy;

  @JsonProperty("NrPregnancies")
  private String nrPregnancies;

  @JsonProperty("PercInHeat60DIM")
  private String percInHeat60DIM;

  @JsonProperty("PercNotIns100DIM")
  private String percNotIns100DIM;

  @JsonProperty("PredictedCalvingInterval")
  private String predictedCalvingInterval;

  @JsonProperty("PregnancyRisk")
  private String pregnancyRisk;

  @JsonProperty("PreviousDaysDry")
  private String previousDaysDry;

  @JsonProperty("SuspiciousData")
  private String suspiciousData;
}
