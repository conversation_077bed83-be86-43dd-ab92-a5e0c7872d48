/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserActivityLogDto {

    @NotBlank(message = "Event name is required")
    private String eventName;

    @NotBlank(message = "Path is required")
    private String path;

    @NotBlank(message = "Username is required")
    private String username;

    private String userId;
    private String sessionId;
    private String ipAddress;
    private String userAgent;
    private String browser;
    private String operatingSystem;
    private String deviceType;
    private String screenResolution;
    private String referrerUrl;
    private String pageTitle;
    private String actionType;
    private String elementClicked;
    private String formData;
    private Long responseTimeMs;
    private String errorMessage;
    private Integer httpStatusCode;
    private String requestMethod;
    private Long requestSizeBytes;
    private Long responseSizeBytes;
    private Long timeOnPageSeconds;
    private Double scrollDepthPercentage;
    private String country;
    private String region;
    private String city;
    private String timezone;
    private String language;
    private String accountId;
    private String siteId;
    private String visitId;
    private String featureUsed;
    private String moduleName;

    @NotNull(message = "Last visited timestamp is required")
    private Instant lastVisited;

    private String additionalData;
}
