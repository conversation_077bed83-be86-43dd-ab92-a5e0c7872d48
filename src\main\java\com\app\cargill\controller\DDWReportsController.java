/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.Pair;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.AnalyticsDataPoint.DataPointName;
import com.app.cargill.service.IDDWReportService;
import com.app.cargill.service.analytics.DdwReportsAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ddwReports")
@Tag(
    name = "DDW Reports Information Controller",
    description = "DDW Reports Information Controller")
@RequiredArgsConstructor
public class DDWReportsController extends BaseController {

  private final IDDWReportService dDWReportServiceImpl;
  private final DdwReportsAnalyticsService ddwReportsAnalyticsService;

  @GetMapping("latestDetailedReport/{siteId}")
  @Operation(
      summary = "download DDW detailed report By SiteId",
      description = "This api will download DDW detailed report By SiteId")
  public ResponseEntity<ByteArrayResource> getLatestDetailedReport(
      @PathVariable("siteId") UUID siteId) throws CustomDEExceptions {

    Pair<String, ByteArrayResource> pair = dDWReportServiceImpl.getLatestDetailedReport(siteId);
    ddwReportsAnalyticsService.logDdwReport(
        siteId.toString(), DataPointName.DDW_DETAILED_REPORT_DOWNLOAD);

    return new ResponseEntity<>(
        pair.right(), initHeaders(pair.left(), pair.right()), HttpStatus.CREATED);
  }

  @GetMapping("latestSummaryReport/{siteId}")
  @Operation(
      summary = "download DDW Summary report By SiteId",
      description = "This api will download DDW Summary report By SiteId")
  public ResponseEntity<ByteArrayResource> getLatestSummaryReport(
      @PathVariable("siteId") UUID siteId) throws CustomDEExceptions {

    Pair<String, ByteArrayResource> summaryPair =
        dDWReportServiceImpl.getLatestSummaryReport(siteId);
    ddwReportsAnalyticsService.logDdwReport(
        siteId.toString(), DataPointName.DDW_SUMMARY_REPORT_DOWNLOAD);

    return new ResponseEntity<>(
        summaryPair.right(),
        initHeaders(summaryPair.left(), summaryPair.right()),
        HttpStatus.CREATED);
  }

  public static HttpHeaders initHeaders(String fileName, ByteArrayResource byteArrayResource) {
    HttpHeaders header = new HttpHeaders();
    header.setContentType(MediaType.APPLICATION_PDF);
    header.setContentLength(byteArrayResource.getByteArray().length);
    header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
    return header;
  }
}
