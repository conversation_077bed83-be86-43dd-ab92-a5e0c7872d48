/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.sf.crescendo.model.ActivityCrescendo;
import com.app.cargill.sf.crescendo.service.CrescendoActivityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/activity")
@Tag(
    name = "Salesforce Crescendo Activity",
    description = "Controller related to actions over Activity objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoActivityController {

  private final CrescendoActivityService activityService;

  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get list of activities in Crescendo format",
      description =
          "Get list of activities in Crescendo format modified since the provided timestamp")
  public ResponseEntity<List<ActivityCrescendo>> getActivities(
      @RequestParam(required = false) Instant from) {
    log.debug("CRESCENDO_ACTIVITY_GET_REQUEST {}", StringEscapeUtils.escapeJava(from.toString()));
    List<ActivityCrescendo> activities = activityService.getActivity(from);
    log.debug("CRESCENDO_ACTIVITY_GET {} activities", activities.size());
    return ResponseEntity.ok(activities);
  }
}
