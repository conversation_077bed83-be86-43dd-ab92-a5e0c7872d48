/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReadyToMilkOutputToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  // #region Goals
  @JsonProperty("RetainedPlacentaGoal")
  public Double retainedPlacentaGoal;

  @JsonProperty("MetritisGoal")
  public Double metritisGoal;

  @JsonProperty("DisplacedAbomasumGoal")
  public Double displacedAbomasumGoal;

  @JsonProperty("KetosisGoal")
  public Double ketosisGoal;

  @JsonProperty("MilkFeverGoal")
  public Double milkFeverGoal;

  @JsonProperty("DeathLossGoal")
  public Double deathLossGoal;

  @JsonProperty("DystociaGoal")
  public Double dystociaGoal;
  // #endregion

}
