/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper;

import com.app.cargill.cosmos.model.SiteMappingCosmos;
import com.app.cargill.document.SiteMappingDocument;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SiteMappingsMapper {
  private SiteMappingsMapper() {}

  public static SiteMappingDocument map(SiteMappingCosmos input) {
    try {

      CosmosToModelMapper<SiteMappingCosmos, SiteMappingDocument> siteMapper =
          source ->
              SiteMappingDocument.builder()
                  .id(UUID.fromString(source.getId()))
                  .labyrinthSiteId(UuidMapper.getNullableUuid(source.getLabyrinthSiteId()))
                  .labyrinthAccountId(UuidMapper.getNullableUuid(source.getLabyrinthAccountId()))
                  .maxSiteId(UuidMapper.getNullableUuid(source.getMaxSiteId()))
                  .ddwHerdId(source.getDdwHerdId())
                  .dcgoId(source.getDcgoId())
                  .milkProcessorId(source.getMilkProcessorId())
                  .build();

      return siteMapper.map(input);
    } catch (Exception e) {
      log.error("Error mapping SiteMappings: {} {}", e.getMessage(), input);
      throw e;
    }
  }
}
