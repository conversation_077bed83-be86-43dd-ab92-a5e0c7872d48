/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.cosmos.model.CudChewingByPenCosmos;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class CudChewingToolCosmos extends EditableDocumentBaseCosmos {

  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("CudChewingReports")
  private List<CudChewingByPenCosmos> cudChewingReports;
}
