/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LastModifiedTimeUtc implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("Date")
  public Instant date;

  @JsonProperty("Epoch")
  public Long epoch;
}
