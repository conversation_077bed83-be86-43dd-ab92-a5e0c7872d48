/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.LactationStage;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RumenHealthToolItem implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("PenId")
  private UUID penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("CudChewsCount")
  private List<CudChewingCount> cudChewsCount;

  @JsonProperty("CudChewingCowsCount")
  private CudChewingCowCount cudChewingCowsCount;

  @JsonProperty("PercentChewing")
  private Double percentChewing;

  @JsonProperty("AverageChewsPerCud")
  private Double averageChewsPerCud;

  @JsonProperty("StandardDeviationOfChewsPerCud")
  private Double standardDeviationOfChewsPerCud;

  @JsonProperty("Stage")
  private LactationStage stage;
}
