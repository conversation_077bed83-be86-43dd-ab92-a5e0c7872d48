/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.LeadSource;
import com.app.cargill.constants.Level;
import com.app.cargill.constants.MarketingClassification;
import com.app.cargill.constants.PreferredMethod;
import com.app.cargill.constants.PrimaryLang;
import com.app.cargill.converter.BusinessIdDeserializer;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.document.DateEpoch;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Instant;
import java.util.List;
import lombok.Getter;

@Getter
public class ContactCosmos {

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("ContactId")
  private String contactId;

  @JsonProperty("GoldenRecordAcountId")
  private String goldenRecordAcountId;

  @JsonProperty("SFDCContactId")
  private String sFDCContactId;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("BusinessId")
  @JsonDeserialize(using = BusinessIdDeserializer.class)
  private Integer businessId;

  @JsonProperty("FunctionId")
  private Integer functionId;

  @JsonProperty("BusinessUnit")
  private String businessUnit;

  @JsonProperty("Function")
  private String function;

  @JsonProperty("MailingAddress")
  private AddressCosmos mailingAddress;

  @JsonProperty("PhoneNumber")
  private String phoneNumber;

  @JsonProperty("MobilePhoneNumber")
  private String mobilePhoneNumber;

  @JsonProperty("EmailAddress")
  private String emailAddress;

  @JsonProperty("Comments")
  private String comments;

  @JsonProperty("LastUpdateDateTime")
  private DateEpoch lastUpdateDateTime;

  @JsonProperty("NeedsSync")
  private Boolean needsSync;

  @JsonProperty("ContactOwner")
  private String contactOwner;

  @JsonProperty("NameId")
  private Integer nameId;

  @JsonProperty("Title")
  private String title;

  @JsonProperty("OtherAddress")
  private AddressCosmos otherAddress;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("Mobile")
  private String mobile;

  @JsonProperty("EmailId")
  private String emailId;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("PrimaryOwner")
  private Boolean primaryOwner;

  @JsonProperty("PrimaryLangId")
  private PrimaryLang primaryLangId;

  @JsonProperty("TestNationalIdentificationNumber")
  private String testNationalIdentificationNumber;

  @JsonProperty("ReportsToID")
  private String reportsToID;

  @JsonProperty("ExternalReportsToID")
  private String externalReportsToID;

  @JsonProperty("SecondaryEmail")
  private String secondaryEmail;

  @JsonProperty("PreferredMethodId")
  private PreferredMethod preferredMethodId;

  @JsonProperty("OptIn")
  private Boolean optIn;

  @JsonProperty("Assitant")
  private String assitant;

  @JsonProperty("AssitantPhone")
  private String assitantPhone;

  @JsonProperty("Website")
  private String website;

  @JsonProperty("Department")
  private String department;

  @JsonProperty("CreatedBy")
  private String createdBy;

  @JsonProperty("DataDotComKey")
  private String dataDotComKey;

  @JsonProperty("DoNotCall")
  private Boolean doNotCall;

  @JsonProperty("EmailOptOut")
  private Boolean emailOptOut;

  @JsonProperty("ExternalId")
  private String externalId;

  @JsonProperty("Fax")
  private String fax;

  @JsonProperty("FaxOptOut")
  private Boolean faxOptOut;

  @JsonProperty("FlowName")
  private String flowName;

  @JsonProperty("HomePhone")
  private String homePhone;

  @JsonProperty("Languages")
  private String languages;

  @JsonProperty("LastModifiedBy")
  private String lastModifiedBy;

  @JsonProperty("LastModifiedID")
  private String lastModifiedID;

  @JsonProperty("LastStayInTouchRequestDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastStayInTouchRequestDate;

  @JsonProperty("LastStayInTouchSaveDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastStayInTouchSaveDate;

  @JsonProperty("LeadSource")
  private LeadSource leadSource;

  @JsonProperty("Level")
  private Level level;

  @JsonProperty("MobileFirst")
  private Boolean mobileFirst;

  @JsonProperty("OtherPhoneInUse")
  private String otherPhoneInUse;

  @JsonProperty("Primary")
  private Boolean primary;

  @JsonProperty("MarketingId")
  private List<MarketingClassification> marketingId;

  @JsonProperty("Salutation")
  private String salutation;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private Boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private Boolean isNew;
}
