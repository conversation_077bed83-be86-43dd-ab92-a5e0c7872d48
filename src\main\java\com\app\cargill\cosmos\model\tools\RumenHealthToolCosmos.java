/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.HerdAnalysisGoal;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class RumenHealthToolCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Pens")
  private List<RumenHealthToolItemCosmos> pens;

  @JsonProperty("Goals")
  private List<HerdAnalysisGoal> goals;
}
