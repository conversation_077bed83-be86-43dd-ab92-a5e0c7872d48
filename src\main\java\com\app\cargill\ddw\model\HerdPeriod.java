/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdPeriod {

  @JsonProperty("EndDate")
  public Date endDate;

  @JsonProperty("EndDateId")
  public String endDateId;

  @JsonProperty("ExternalHerdId")
  public String externalHerdId;

  @JsonProperty("HerdProfileId")
  public String herdProfileId;

  @JsonProperty("PeriodType")
  public String periodType;

  @JsonProperty("StartDate")
  public Date startDate;

  @JsonProperty("StartDateId")
  public String startDateId;
}
