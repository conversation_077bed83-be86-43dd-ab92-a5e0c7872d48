/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import org.joda.time.DateTime;

public class EpochDateTimeSerializer extends JsonSerializer<Object> {
  @Override
  public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers)
      throws IOException {
    if (value instanceof Long) {
      DateTime dateTime = DateTime.parse(String.valueOf(value));
      String parseDate = dateTime.toInstant().toString();
      gen.writeString(parseDate);
    } else {
      gen.writeString(value.toString());
    }
  }
}
