/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class ManureScreenerScoreGoalToolItemCosmos {
  @JsonProperty("VisitsSelected")
  private List<String> visitsSelected;

  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("GoalTitle")
  private String goalTitle;

  @JsonProperty("TopGoalMinimumPercent")
  private Double topGoalMinimumPercent;

  @JsonProperty("TopGoalMaximumPercent")
  private Double topGoalMaximumPercent;

  @JsonProperty("MidGoalMinimumPercent")
  private Double midGoalMinimumPercent;

  @JsonProperty("MidGoalMaximumPercent")
  private Double midGoalMaximumPercent;

  @JsonProperty("BottomGoalMinimumPercent")
  private Double bottomGoalMinimumPercent;

  @JsonProperty("BottomGoalMaximumPercent")
  private Double bottomGoalMaximumPercent;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;
}
