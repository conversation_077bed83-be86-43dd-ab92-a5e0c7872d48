/* Cargill Inc.(C) 2022 */
package com.app.cargill.components;

import com.app.cargill.constants.PaperSize;
import com.app.cargill.constants.TemplateExportType;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserType.LaunchOptions;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Page.ScreenshotOptions;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.Margin;
import com.microsoft.playwright.options.ScreenshotType;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Arrays;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
@SuppressWarnings("java:S125")
public class PlayWrightComponent {
  public static final String ACTIVE_PROFILE = "local";
  public static final String GENERATED_TEMPLATE_FOLDER = "/tmp/generatedTemplates/";
  private final Environment environment;

  public byte[] initAndPrepareExport(String chartNameWithPath, TemplateExportType exportType)
      throws IOException {
    try (Playwright playwright = Playwright.create()) {
      Browser browser;
      String url = "file://" + chartNameWithPath;
      LaunchOptions launchOptions = new LaunchOptions().setHeadless(true);
      if (Arrays.stream(environment.getActiveProfiles())
          .noneMatch(env -> (env.equalsIgnoreCase(ACTIVE_PROFILE)))) {
        launchOptions.setExecutablePath(Path.of("/usr", "bin", "chromium-browser"));
      }
      log.debug("Generated Url of file = " + url);
      browser = playwright.chromium().launch(launchOptions);
      Page page = browser.newPage();
      page.navigate(url);
      // page.waitForLoadState(LoadState.LOAD);
      // page.waitForLoadState(LoadState.NETWORKIDLE);
      page.waitForLoadState(LoadState.DOMCONTENTLOADED);
      page.setDefaultTimeout(3000);
      byte[] screenshot;
      try {
        // intentional wait for animations to complete
        Thread.sleep(300);
        screenshot = generateExportFile(page, exportType);
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        log.debug("Exception while taking screenshot!!!");
        log.error(e.getLocalizedMessage());
        closeBrowser(browser, page);
        screenshot = relaunchBrowserAndPrepareExport(playwright, launchOptions, url, exportType);
      } catch (Exception e) {
        log.debug("Exception while taking screenshot!!!");
        log.error(e.getLocalizedMessage());
        closeBrowser(browser, page);
        screenshot = relaunchBrowserAndPrepareExport(playwright, launchOptions, url, exportType);
      }
      closeBrowser(browser, page);

      return screenshot;
    }
  }

  private static void closeBrowser(Browser browser, Page page) {
    page.close();
    browser.close();
  }

  private byte[] relaunchBrowserAndPrepareExport(
      Playwright playwright, LaunchOptions launchOptions, String url, TemplateExportType exportType)
      throws IOException {
    log.debug(" relaunching the browser");
    try (Browser browser = playwright.chromium().launch(launchOptions)) {
      Page page = browser.newPage();
      page.navigate(url);
      return generateExportFile(page, exportType);
    }
  }

  private byte[] generateExportFile(Page page, TemplateExportType exportType) throws IOException {
    // page.waitForLoadState();

    byte[] screenshot = null;
    if (exportType == TemplateExportType.EXPORT_IMAGE) {
      log.debug("Start taking screenshot");
      screenshot =
          page.screenshot(new ScreenshotOptions().setFullPage(true).setType(ScreenshotType.PNG));
      log.debug("End taking screenshot");
    } else if (exportType == TemplateExportType.EXPORT_PDF) {
      log.debug("Start taking PDF");
      PaperSize paperSize = PaperSize.A4;
      screenshot =
          page.pdf(
              new Page.PdfOptions()
                  .setFormat(paperSize.name())
                  .setWidth(paperSize.getWidth())
                  .setHeight(paperSize.getHeight())
                  .setMargin(
                      new Margin()
                          .setLeft(paperSize.getMarginLeft())
                          .setRight(paperSize.getMarginRight())
                          .setTop(paperSize.getMarginTop())
                          .setBottom(paperSize.getMarginBottom()))
                  .setPrintBackground(true)
                  .setPreferCSSPageSize(true)
                  .setDisplayHeaderFooter(false));
      log.debug("End taking PDF");
    }

    return screenshot;
  }
}
