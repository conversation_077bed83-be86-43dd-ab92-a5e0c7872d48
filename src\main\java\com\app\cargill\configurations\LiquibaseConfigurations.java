/* Cargill Inc.(C) 2022 */
package com.app.cargill.configurations;

import javax.sql.DataSource;
import liquibase.exception.LiquibaseException;
import liquibase.integration.spring.SpringLiquibase;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;

@Configuration
@RequiredArgsConstructor
@Slf4j
@Profile({"!test", "!mock-cosmos"})
public class LiquibaseConfigurations {
  private final DataSource dataSource;

  @Value("${spring.liquibase.change-log}")
  private String changeLog;

  @Bean
  public LiquibaseProperties liquibaseProperties() {
    return new LiquibaseProperties();
  }

  public SpringLiquibase initSpringLiquibase() {
    LiquibaseProperties liquibaseProperties = liquibaseProperties();
    SpringLiquibase liquibase = new SpringLiquibase();
    liquibase.setChangeLog(changeLog);
    liquibase.setContexts(liquibaseProperties.getContexts());
    liquibase.setDataSource(dataSource);
    liquibase.setDefaultSchema(liquibaseProperties.getDefaultSchema());
    liquibase.setDropFirst(liquibaseProperties.isDropFirst());
    liquibase.setShouldRun(true);
    liquibase.setChangeLogParameters(liquibaseProperties.getParameters());
    return liquibase;
  }

  @EventListener(ApplicationReadyEvent.class)
  public SpringLiquibase runLiquibaseMigration() throws LiquibaseException {
    log.info("----Starting liquibase migration----");
    SpringLiquibase springLiquibase = initSpringLiquibase();
    springLiquibase.afterPropertiesSet();
    log.info("----Finished liquibase migration----");
    return springLiquibase;
  }
}
