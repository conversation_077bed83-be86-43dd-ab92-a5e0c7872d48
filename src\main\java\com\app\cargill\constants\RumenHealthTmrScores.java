/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import lombok.Getter;

@Getter
@SuppressWarnings("java:S115") // Constants Naming
public enum RumenHealthTmrScores {
  @JsonEnumDefaultValue
  NoneSelected(0),
  FourScreenNew(1),
  ThreeScreen(2),
  FourScreenOld(3);

  private final Integer value;

  RumenHealthTmrScores(Integer value) {
    this.value = value;
  }

  @JsonCreator
  public static RumenHealthTmrScores forName(String name) {
    for (RumenHealthTmrScores c : values()) {
      if (c.name().equals(name)) { // change accordingly
        return c;
      }
    }

    try {
      return forValue(Integer.valueOf(name));
    } catch (NumberFormatException ignored) {
    }

    return FourScreenNew;
  }

  public static RumenHealthTmrScores forValue(Integer name) {
    for (RumenHealthTmrScores c : values()) {
      if (c.getValue().equals(name)) { // change accordingly
        return c;
      }
    }

    return FourScreenNew;
  }
}
