/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.dto.BulkUserInsertDto;
import com.app.cargill.dto.IdTokenDto;
import com.app.cargill.dto.UserDto;
import com.app.cargill.dto.UserSaveDto;
import com.app.cargill.model.User;
import java.io.UnsupportedEncodingException;
import java.util.List;

public interface IUserService {
  List<UserDto> getAllUsers();

  UserDto getUserById(Long userId);

  UserDto getUserByPrincipal(String name);

  UserDto saveOrUpdate(UserDto user);

  String getCurrentLoggedInUser();

  User getLoggedUserData();

  String getCurrentLoggedInUserAsJsonObj();

  UserDto getUserByIdToken(String idToken) throws UnsupportedEncodingException;

  UserSaveDto save(UserSaveDto userDto);

  UserSaveDto update(UserSaveDto userDto);

  List<User> updateName(String code);

  UserDto fetchAndUpdateUserInfo(IdTokenDto idTokenDto) throws UnsupportedEncodingException;

  BulkUserInsertDto bulkInsertUsers(BulkUserInsertDto userDto);

  AuthenticationPlatform getCurrentLoggedInUserAuthPlatform();
}
