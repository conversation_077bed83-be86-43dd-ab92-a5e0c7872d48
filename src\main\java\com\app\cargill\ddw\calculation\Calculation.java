/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.calculation;

import com.app.cargill.ddw.model.*;
import com.azure.cosmos.implementation.Strings;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.math.NumberUtils;

public class Calculation {

  private Calculation() {}

  public static final String HERD_LACTATING_COWS = "HERDLACTATINGCOWS";

  public static double asFedIntake(
      HerdNutrition nutrition, HerdMilkProduction herdPenData, HerdData herdData) {
    double asFedintake = 0.0;
    Optional<String> herdGroupName =
        herdData.getMilkRecordings() != null && !herdData.getMilkRecordings().isEmpty()
            ? herdData.getMilkRecordings().stream()
                .filter(milkRecording -> !Strings.isNullOrEmpty(milkRecording.getGroupName()))
                .map(MilkRecording::getGroupName)
                .findFirst()
            : Optional.empty();

    if (nutrition != null
        && !Strings.isNullOrEmpty(nutrition.getAvgTMRAsFed())
        && !Strings.isNullOrEmpty(nutrition.getAvgTMRDMWeighBack())) {
      asFedintake =
          Double.parseDouble(nutrition.getAvgTMRAsFed())
              - Double.parseDouble(nutrition.getAvgTMRDMWeighBack());
      return asFedintake;

    } else if (herdGroupName.isPresent()
        && nutrition != null
        && nutrition.getAvgPMRAsFed() != null
        && herdGroupName.get().equals(herdPenData.getGroupName())) {

      double temp =
          herdData.getMilkRecordings().stream()
              .filter(
                  milkRecording ->
                      milkRecording != null
                          && !Strings.isNullOrEmpty(milkRecording.getDryMatterIntake()))
              .mapToDouble(nu -> Double.parseDouble(nu.getDryMatterIntake()))
              .sum();

      asFedintake =
          Double.parseDouble(
                  nutrition.getAvgTMRAsFed() == null ? "0.0" : nutrition.getAvgTMRAsFed())
              - Double.parseDouble(
                  nutrition.getAvgTMRDMWeighBack() == null
                      ? "0.0"
                      : nutrition.getAvgTMRDMWeighBack())
              + (temp / 0.88);

      return asFedintake;
    } else {
      return asFedintake;
    }
  }

  public static double asFedIntake(
      HerdNutrition nutrition, HerdCulling herdPenData, HerdData herdData) {
    double asFedIntake = 0.0;
    Optional<String> herdGroupName = Optional.of("");

    if (herdData != null && herdData.getMilkRecordings() != null)
      herdGroupName =
          herdData.getMilkRecordings() != null && !herdData.getMilkRecordings().isEmpty()
              ? herdData.getMilkRecordings().stream()
                  .filter(milkRecording -> !Strings.isNullOrEmpty(milkRecording.getGroupName()))
                  .map(MilkRecording::getGroupName)
                  .findFirst()
              : Optional.empty();
    if (nutrition != null
        && !Strings.isNullOrEmpty(nutrition.getAvgTMRAsFed())
        && !Strings.isNullOrEmpty(nutrition.getAvgTMRDMWeighBack())) {
      asFedIntake =
          Double.parseDouble(nutrition.getAvgTMRAsFed())
              - Double.parseDouble(nutrition.getAvgTMRDMWeighBack());
      return asFedIntake;

    } else if (herdGroupName.isPresent()
        && nutrition != null
        && nutrition.getAvgPMRAsFed() != null
        && herdData != null
        && herdData.getMilkRecordings() != null
        && (herdGroupName.get().equals(herdPenData.getGroupName()))) {

      double temp =
          herdData.getMilkRecordings().stream()
              .filter(
                  milkRecording ->
                      milkRecording != null
                          && !Strings.isNullOrEmpty(milkRecording.getDryMatterIntake()))
              .mapToDouble(nu -> Double.parseDouble(nu.getDryMatterIntake()))
              .sum();

      asFedIntake =
          Double.parseDouble(
                  Strings.isNullOrEmpty(nutrition.getAvgTMRAsFed())
                      ? "0.0"
                      : nutrition.getAvgTMRAsFed())
              - Double.parseDouble(
                  Strings.isNullOrEmpty(nutrition.getAvgTMRDMWeighBack())
                      ? "0.0"
                      : nutrition.getAvgTMRDMWeighBack())
              + (temp / 0.88);

      return asFedIntake;
    } else {
      return asFedIntake;
    }
  }

  public static double dryMatterIntakeForPen(HerdNutrition herdNutrition) {

    if (herdNutrition != null) {
      return (NumberUtils.toDouble(herdNutrition.getAvgTMRDMAsFed())
              - NumberUtils.toDouble(herdNutrition.getAvgTMRDMWeighBack()))
          + (NumberUtils.toDouble(herdNutrition.getAvgPMRDMAsFed())
              - NumberUtils.toDouble(herdNutrition.getAvgPMRDMWeighBack()));
    }
    return 0.0;
  }

  public static double rationCostCalculationForPen(HerdNutrition nutrition) {

    if (nutrition != null) {
      return (NumberUtils.toDouble(nutrition.getAvgTMRasFedCosts())
              - (NumberUtils.toDouble(nutrition.getAvgTMRWBCosts())))
          + NumberUtils.toDouble(nutrition.getAvgPMRasFedCosts());
    }
    return 0.0;
  }

  public static double calculateRationCost(List<HerdNutrition> nutritionList) {
    double rationCostPer = 0;
    double totalCows = 0;
    double rationCost = 0.0;
    for (HerdNutrition nutrition : nutritionList) {

      if (nutrition != null
          && nutrition.getGroupName() != null
          && nutrition.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(nutrition.getAvgTMRasFedCosts())
      //                    && !Strings.isNullOrEmpty(nutrition.getAvgNrCowsFS())
      ) {

        rationCostPer =
            NumberUtils.toDouble(nutrition.getAvgTMRasFedCosts())
                * NumberUtils.toDouble(nutrition.getAvgNrCowsFS());
        totalCows = NumberUtils.toDouble(nutrition.getAvgNrCowsFS());
      }
    }

    if (totalCows > 0.0) {
      rationCost = rationCostPer / totalCows;
    }

    return rationCost;
  }

  public static double calculateDryMaterIntake(List<HerdNutrition> nutritionList) {
    double dryMatterIntakePer = 0;
    double totalCows = 0;

    for (HerdNutrition nutrition : nutritionList) {
      if (nutrition != null
          && nutrition.getGroupName() != null
          && nutrition.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(nutrition.getAvgTMRDMAsFed())
      //                    && !Strings.isNullOrEmpty(nutrition.getAvgNrCowsFS())
      ) {

        dryMatterIntakePer =
            Double.parseDouble(nutrition.getAvgTMRDMAsFed())
                * NumberUtils.toDouble(nutrition.getAvgNrCowsFS());

        totalCows = NumberUtils.toDouble(nutrition.getAvgNrCowsFS());
      }
    }

    double dryMatterIntake = 0.0;
    if (totalCows > 0.0) {
      dryMatterIntake = dryMatterIntakePer / totalCows;
    }

    return dryMatterIntake;
  }

  public static double calculateAsFedIntake(List<HerdNutrition> nutritionList) {
    double asFedIntakePer = 0;
    double totalCows = 0;

    for (HerdNutrition nutrition : nutritionList) {

      if (nutrition != null
          && nutrition.getGroupName() != null
          && nutrition.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(nutrition.getAvgTMRAsFed())
      //                    && !Strings.isNullOrEmpty(nutrition.getAvgNrCowsFS())
      ) {

        asFedIntakePer =
            NumberUtils.toDouble(nutrition.getAvgTMRAsFed())
                * NumberUtils.toDouble(nutrition.getAvgNrCowsFS());

        totalCows = NumberUtils.toDouble(nutrition.getAvgNrCowsFS());
      }
    }

    double asFedIntake = 0;
    if (totalCows > 0) {
      asFedIntake = asFedIntakePer / totalCows;
    }

    return asFedIntake;
  }

  public static double calculateMilkSCC(List<MilkRecording> milkRecordings) {
    double milkFatPer = 0;
    double totalCows = 0;

    for (MilkRecording recording : milkRecordings) {
      if (recording != null
          && recording.getGroupName() != null
          && recording.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(recording.getAverageSCC())
      //                    && !Strings.isNullOrEmpty(recording.getNumberCowsInMilk())
      ) {
        milkFatPer =
            NumberUtils.toDouble(recording.getAverageSCC())
                * NumberUtils.toDouble(recording.getNumberCowsInMilk());
        totalCows = NumberUtils.toDouble(recording.getNumberCowsInMilk());
      }
    }

    double milkSCC = 0.0;
    if (totalCows > 0.0) {
      milkSCC = milkFatPer / totalCows;
    }

    return milkSCC;
  }

  public static double calculateMilkYieldPercent(
      List<MilkRecording> milkRecordings, double milkYldPerct) {
    double milkYld = 0.0;
    double totalCows = 0.0;

    for (MilkRecording recording : milkRecordings) {
      if (recording.getGroupName() != null
          && recording.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(recording.getAverageDailyYieldKg())
      //                    && !Strings.isNullOrEmpty(recording.getNumberCowsInMilk())
      ) {
        milkYld =
            NumberUtils.toDouble(recording.getAverageDailyYieldKg())
                * NumberUtils.toDouble(recording.getNumberCowsInMilk());
        totalCows = NumberUtils.toDouble(recording.getNumberCowsInMilk());
      }
    }

    if (totalCows > 0.0) {
      milkYldPerct = milkYld / totalCows;
    }

    return milkYldPerct;
  }

  public static double calculateMilkFatPercent(List<MilkRecording> milkRecordings) {
    double milkFatPer = 0.0;
    double totalCows = 0.0;
    double milkFatPerct = 0.0;

    for (MilkRecording recording : milkRecordings) {
      if (recording.getGroupName() != null
          && recording.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(recording.getAverageFatPerc())
      //                    && !Strings.isNullOrEmpty(recording.getNumberCowsInMilk())
      ) {

        milkFatPer =
            NumberUtils.toDouble(recording.getAverageFatPerc())
                * NumberUtils.toDouble(recording.getNumberCowsInMilk());
        totalCows = NumberUtils.toDouble(recording.getNumberCowsInMilk());
      }
    }
    if (totalCows > 0.0) {
      milkFatPerct = milkFatPer / totalCows;
    }

    return milkFatPerct;
  }

  public static double calculateMilkProteinPercentage(List<MilkRecording> milkRecordings) {
    double milkProteinPer = 0.0;
    double totalCows = 0.0;
    double milkProteinPerct = 0;

    for (MilkRecording recording : milkRecordings) {
      if (recording.getGroupName() != null
          && recording.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(recording.getAverageProtienPerc())
      //                    && !Strings.isNullOrEmpty(recording.getNumberCowsInMilk())
      ) {

        milkProteinPer =
            NumberUtils.toDouble(recording.getAverageProtienPerc())
                * NumberUtils.toDouble(recording.getNumberCowsInMilk());
        totalCows = NumberUtils.toDouble(recording.getNumberCowsInMilk());
      }
    }
    if (totalCows > 0.0) {
      milkProteinPerct = milkProteinPer / totalCows;
    }

    return milkProteinPerct;
  }

  public static double calculateMilkYieldPercent(List<HerdMilkProduction> productions) {
    double milkYld = 0.0;
    double totalCows = 0.0;
    double milkYldPerct = 0;

    for (HerdMilkProduction production : productions) {
      if (production.getGroupName() != null
          && production.getGroupName().equalsIgnoreCase(HERD_LACTATING_COWS)
      //                    && !Strings.isNullOrEmpty(production.getAverageYieldOverall())
      //                    && !Strings.isNullOrEmpty(production.getNrCowsOverall())
      ) {
        milkYld =
            NumberUtils.toDouble(production.getAverageYieldOverall())
                * NumberUtils.toDouble(production.getNrCowsOverall());
        totalCows = NumberUtils.toDouble(production.getNrCowsOverall());
      }
    }

    if (totalCows > 0.0) {
      milkYldPerct = milkYld / totalCows;
    }

    return milkYldPerct;
  }
}
