/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class RumenHealthTool extends EditableDocumentBase {
  /** */
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("Pens")
  private List<RumenHealthToolItem> pens;

  @JsonProperty("Goals")
  private List<HerdAnalysisGoal> goals;
}
