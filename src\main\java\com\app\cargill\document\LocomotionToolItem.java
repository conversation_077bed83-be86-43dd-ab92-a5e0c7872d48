/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocomotionToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// These values are used in the Locomotion Milk Loss Calculations
  /// for lactating pens only,
  /// and are displayed in the Locomotion Score
  /// % Loss / Cow row. These values are, at the moment, constant
  /// and cannot be changed by the user.
  /// The scores are only used for Categories 3, 4, and 5.
  /// </summary>
  @JsonProperty("MilkScoreThree")
  @Builder.Default
  private Double milkScoreThree = 5.1;

  @Builder.Default
  @JsonProperty("MilkScoreFour")
  private Double milkScoreFour = 16.8;

  @Builder.Default
  @JsonProperty("MilkScoreFive")
  private Double milkScoreFive = 36.0;

  @JsonProperty("PenId")
  private UUID penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("TotalAnimalsInPen")
  private Integer totalAnimalsInPen;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("MilkProductionInKg")
  private Double milkProductionInKg;

  @JsonProperty("Categories")
  private List<LocomotionToolItemCategoryItem> categories;

  @JsonProperty("VisitsSelected")
  private List<UUID> visitsSelected;

  @JsonProperty("IsToolItemNew")
  public Boolean isToolItemNew;

  @JsonProperty("AverageLocomotionScore")
  public Double averageLocomotionScore;

  @JsonProperty("StandardDeviation")
  public Double standardDeviation;

  @JsonProperty("MilkLoss")
  public Double milkLoss;

  @JsonProperty("ToolStatus")
  public ToolStatuses toolStatus;
}
