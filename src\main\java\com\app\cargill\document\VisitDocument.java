/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import jakarta.persistence.Transient;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("java:S6539")
public class VisitDocument extends EditableDocumentBase implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Labyrinth Guid for the account this visit belonds to.
  /// </summary>
  @JsonProperty("CustomerId")
  public UUID customerId;

  @JsonProperty("UnitOfMeasure")
  public UnitOfMeasureKeys unitOfMeasure;
  /// <summary>
  /// Labyrinth Guid for the site of this visit.
  /// </summary>
  @JsonProperty("SiteId")
  public UUID siteId;
  /// <summary>
  /// The date the visit occurred. This date never changes and is
  /// set when the visit is created.
  /// </summary>
  @JsonProperty("VisitDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant visitDate;
  /// <summary>
  /// Indicates if the visit is In Progress or Published.
  /// Once published, a visit and all its contained artifacts cannot
  /// be changed.
  /// </summary>
  ///
  @JsonProperty("FirstName")
  public String firstName;

  /// <summary>
  /// Gets or sets the last name.
  /// </summary>
  /// <value>The last name.</value>
  @JsonProperty("LastName")
  public String lastName;

  @JsonProperty("Status")
  public VisitStatus status; // change

  @JsonProperty("Selected")
  public Boolean selected;

  @JsonProperty("VisitName")
  public String visitName;

  @JsonProperty("FormattedCreationDate")
  public String formattedCreationDate;

  @JsonProperty("SelectedPointScale")
  public BCSPointScale selectedPointScale;
  /// <summary>
  /// Gets or sets a value indicating whether this <see
  /// cref="T:Labyrinth.Common.Entities.Visit"/> is visit auto published.
  /// </summary>
  /// <value><c>true</c> if is visit auto published; otherwise,
  /// <c>false</c>.</value>
  @JsonProperty("IsVisitAutoPublished")
  @Builder.Default
  public Boolean isVisitAutoPublished = false;

  @JsonProperty("NeedsSync")
  public Boolean needsSync;
  /// <summary>
  /// The first key is the report id, the second is the account id.
  /// </summary>

  @JsonProperty("GeneratedPDFReports")
  private Map<UUID, UUID> generatedPDFReports;

  @JsonProperty("CudChewing")
  public CudChewingTool cudChewing; // change

  @JsonProperty("HeatStress")
  public HeatStressTool heatStress;

  @JsonProperty("ForageAuditScorecard")
  public Scorecard forageAuditScorecard;

  @JsonProperty("PileAndBunker")
  public PileAndBunkerTool pileAndBunker;

  @JsonProperty("RumenHealth")
  public RumenHealthTool rumenHealth;

  @JsonProperty("TMRParticleScore")
  public RumenHealthTMRParticleScoreTool tmrParticleScore;

  @JsonProperty("RumenHealthManureScore")
  public RumenHealthManureScoreTool rumenHealthManureScore;

  @JsonProperty("LocomotionScore")
  public LocomotionTool locomotionScore;

  @JsonProperty("BodyCondition")
  public BodyConditionTool bodyCondition;

  @JsonProperty("AnimalAnalysis")
  private AnimalAnalysisTool animalAnalysis;

  @JsonProperty("Revenue")
  public RevenueInputs revenue;

  @JsonProperty("MetabolicIncidence")
  public MetabolicIncidenceTool metabolicIncidence;

  @JsonProperty("PenTimeBudgetTool")
  public PenTimeBudgetTool penTimeBudgetTool;

  @JsonProperty("SelectedCurrency")
  public Currencies selectedCurrency;

  @JsonProperty("WalkThroughReports")
  public VisitWalkThroughReports walkThroughReports;
  // Added for ReadyToMilk ELH Tool
  @JsonProperty("ReadyToMilk")
  public ReadyToMilkTool readyToMilk;
  // Added For Milk Sold Evaluation Tool
  @JsonProperty("MilkSoldEvaluation")
  public MilkSoldEvaluationTool milkSoldEvaluation;

  @JsonProperty("UrinePHTool")
  public UrinePHTool urinePHTool;

  @JsonProperty("CalfHeiferScorecard")
  public CalfHeiferScorecard calfHeiferScorecard;

  @JsonProperty("RoboticMilkEvaluation")
  public RoboticMilkEvaluationTool roboticMilkEvaluation;

  @JsonProperty("ManureScreenerTool")
  public ManureScreenerTool manureScreenerTool;

  @JsonProperty("RumenFillManureScore")
  public RumenFillTool rumenFillManureScore; // c

  @JsonProperty("ForagePennState")
  public ForagePennStateTool foragePennState;

  @JsonProperty("ReportType")
  private List<ReportType> reportType;
  // region Backlog - 272095 - Outlook calendar dates extend to extra days for

  @JsonProperty("ProfitabilityAnalysis")
  private ProfitabilityAnalysisTool profitabilityAnalysis;
  // activities
  @JsonProperty("visitPublishedDateTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant visitPublishedDateTimeUtc;

  @Transient public Double siteMilk;

  @JsonProperty("PensUsed")
  private Map<String, List<UUID>> pensUsed = new HashMap<>();

  @JsonProperty("ReturnOverFeed")
  private ReturnOverFeedTool returnOverFeedTool;
}
