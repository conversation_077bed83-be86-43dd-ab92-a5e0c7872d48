/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.sf.de.SalesforceSyncTrigger;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/lift/sync")
@Tag(
    name = "Salesforce Sync",
    description = "Synchronize data between the app and Salesforce instance(s)")
@RequiredArgsConstructor
@Slf4j
public class SalesforceLiftSyncController {

  private final SalesforceSyncTrigger syncTrigger;

  @PostMapping
  @Operation(
      summary = "Initiate LIFT synchronization task",
      description = "This will start a synchronization of the Salesforce data to the app data")
  public List<LongRunningTask<SyncResult>> startLiftSync(
      @RequestParam(defaultValue = "false") boolean partial) {
    return syncTrigger.triggerSync(partial);
  }

  @PostMapping("/accounts")
  @Operation(
      summary = "Initiate LIFT Accounts synchronization task",
      description =
          "This will start a synchronization of the Salesforce Accounts data to the app data")
  public LongRunningTask<SyncResult> startLiftPullAccountsSync(
      @RequestParam(defaultValue = "false") boolean partial) {
    LongRunningTask<SyncResult> task = syncTrigger.startLiftPullAccountsSync(partial);
    return new LongRunningTask<>(task.getLocalId(), task.getName(), task.getStatus());
  }

  @PostMapping("/accountsMerge")
  @Operation(
      summary = "Initiate LIFT Accounts synchronization task",
      description =
          "This will start a synchronization of the Salesforce Accounts data to the app data")
  public LongRunningTask<SyncResult> startLiftPullAccountsMergeSync(
      @RequestParam(defaultValue = "false") boolean partial) {
    LongRunningTask<SyncResult> task = syncTrigger.startLiftPullAccountsMergeSync(partial);
    return new LongRunningTask<>(task.getLocalId(), task.getName(), task.getStatus());
  }
}
