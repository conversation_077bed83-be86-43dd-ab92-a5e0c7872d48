/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import org.springframework.data.annotation.Id;

@Container(containerName = "State")
@Getter
public class StateCosmos {
  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("StateCode")
  private String stateCode;

  @JsonProperty("StateName")
  private String stateName;

  @JsonProperty("CountryCode")
  private String countryCode;
}
