/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SiteMappingDocument implements Serializable {

  private static final long serialVersionUID = 1L;
  private UUID id;

  @JsonProperty("LabyrinthSiteId")
  private UUID labyrinthSiteId;

  @JsonProperty("DDWHerdId")
  private String ddwHerdId;

  @JsonProperty("MaxSiteId")
  private UUID maxSiteId;

  @JsonProperty("LabyrinthAccountId")
  private UUID labyrinthAccountId;

  @JsonProperty("MilkProcessorId")
  private String milkProcessorId;

  @JsonProperty("DCGOId")
  private String dcgoId;
}
