/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.AccountDto;
import com.app.cargill.dto.AccountFavouriteDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IAccountService;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/account")
@Tag(name = "Account Information Controller", description = "Account Information Controller")
@RequiredArgsConstructor
public class AccountController extends BaseController {

  private final IAccountService accountService;
  private final ResourceBundleMessageSource resourceBundleMessageSource;

  @Value("${app.configurations.default-utc-timestamp}")
  private Instant defaultTime;

  @GetMapping("/getAllAccounts")
  @Operation(
      summary = "This api is for developer testing",
      description = "This method will return all accounts")
  public ResponseEntity<ResponseEntityDto<List<AccountDto>>> getAllAccountsNonPaginated() {
    return handleSuccessResponse(accountService.getAllAccountsNonPaginated());
  }

  @GetMapping
  @Operation(summary = "Get all accounts", description = "This method will return all accounts")
  public ResponseEntity<ResponseEntityDto<List<AccountDto>>> getAllAccounts(
      @RequestParam(required = false, name = "accountType", defaultValue = "") String accountType,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(name = "email") String email) {

    return handleSuccessResponse(accountService.getAllAccounts(accountType, lastSyncTime, email));
  }

  @GetMapping("/paginated")
  @Operation(
      summary = "Get paginated Accounts",
      description = "This method will return paginated Accounts with filter")
  public ResponseEntity<ResponseEntityDto<Page<AccountDto>>> getPaginatedAccounts(
      @RequestParam(defaultValue = "", name = "search", required = false) String search,
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", name = "sortBy") String sortBy,
      @RequestParam(name = "accountType", defaultValue = "", required = false) String accountType,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(name = "email", required = false) String email,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        accountService.getPaginatedAccounts(
            search, page, size, sortBy, accountType, lastSyncTime, email, sorting));
  }

  @GetMapping("/filteredAccounts")
  @Operation(
      summary = "Get Account Ids which are accessible for the user",
      description = "This method will return Account Ids which are accessible for the user")
  public ResponseEntity<ResponseEntityDto<List<String>>> getFilteredAccountIds() {

    return handleSuccessResponse(accountService.getFilteredAccountIds());
  }

  @PostMapping("/favourite")
  @Operation(
      summary = "Pin an account",
      description = "This api will favourite an Account pinning it on the app dashboard")
  public ResponseEntity<ResponseEntityDto<AccountDto>> favourite(
      @RequestBody AccountFavouriteDto accountFavouriteDto) {
    return handleSuccessResponse(accountService.favourite(accountFavouriteDto));
  }

  @PostMapping
  @Operation(summary = "Save an Account", description = "This api will save the Account")
  public ResponseEntity<ResponseEntityDto<Object>> save(
      @RequestBody AccountDto accountDto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {
    try {
      Locale locale = Locale.forLanguageTag(localeString);
      LocaleContextHolder.setLocale(locale);
      return handleSuccessResponse(
          accountService.save(accountDto, locale, resourceBundleMessageSource));
    } catch (CustomDEExceptions e) {
      return handleResponse(
          "Error: Something happened.",
          e.getLocalizedMessage(),
          ResponseStatus.FAILED,
          HttpStatus.NOT_ACCEPTABLE);
    }
  }

  @PutMapping
  @Operation(summary = "update Accounts", description = "This api will update the Accounts")
  public ResponseEntity<ResponseEntityDto<Object>> update(
      @RequestBody AccountDto accountDto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, JsonProcessingException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    try {
      Locale locale = Locale.forLanguageTag(localeString);
      LocaleContextHolder.setLocale(locale);
      return handleSuccessResponse(
          accountService.update(accountDto, locale, resourceBundleMessageSource));
    } catch (CustomDEExceptions e) {
      return handleResponse(
          "Error: Something happened.",
          e.getLocalizedMessage(),
          ResponseStatus.FAILED,
          HttpStatus.NOT_ACCEPTABLE);
    }
  }

  @GetMapping("/{id}")
  @Operation(summary = "get Account by Id", description = "This method will return Account by ID")
  public ResponseEntity<ResponseEntityDto<AccountDto>> getAccountById(@PathVariable String id) {
    return handleSuccessResponse(accountService.getAccountById(id));
  }
}
