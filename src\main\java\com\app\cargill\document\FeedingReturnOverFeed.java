/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeedingReturnOverFeed implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("LactatingCows")
  private Integer lactatingCows;

  @JsonProperty("DaysInMilk")
  private Double daysInMilk;

  @JsonProperty("HomeGrownForages")
  private List<HomeGrownForage> homeGrownForages;

  @JsonProperty("HomeGrownGrains")
  private List<HomeGrownGrain> homeGrownGrains;

  @JsonProperty("PurchaseBulkFeed")
  private List<PurchaseBulkFeed> purchaseBulkFeed;

  @JsonProperty("PurchaseBagsFeed")
  private List<PurchaseBagFeed> purchaseBagsFeed;
}
