/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.sf.cc.api.model.SObjectsResponse;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.service.LiftApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/lift/api")
@Tag(name = "Salesforce API", description = "Outputs data from the generic API endpoints")
@RequiredArgsConstructor
@Slf4j
public class SalesforceLiftApiController {
  private final LiftApiService liftApiService;

  @GetMapping("/versions")
  @Operation(summary = "Get versions", description = "Get all versions supported by the API")
  public List<VersionObject> getVersions() {
    return liftApiService.getApiVersions();
  }

  @GetMapping("/latest_version")
  @Operation(summary = "Get latest version", description = "Get latest API version")
  public VersionObject getLatestVersion() {
    return liftApiService.getLatestApiVersion();
  }

  @GetMapping("/sobject")
  @Operation(
      summary = "SObject(s) definitions",
      description = "Get defined SObjects and their parameters")
  public SObjectsResponse getSObjectDefinitions() {
    return liftApiService.getSObjectsDefinitions();
  }
}
