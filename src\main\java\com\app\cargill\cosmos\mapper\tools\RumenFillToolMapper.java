/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.RumenFillScoreToolItemCosmos;
import com.app.cargill.cosmos.model.tools.RumenFillToolCosmos;
import com.app.cargill.document.RumenFillScoreToolItem;
import com.app.cargill.document.RumenFillTool;
import java.util.UUID;

public class RumenFillToolMapper {

  private RumenFillToolMapper() {}

  public static RumenFillTool map(RumenFillToolCosmos input) {

    CosmosToModelMapper<RumenFillScoreToolItemCosmos, RumenFillScoreToolItem> itemMapper =
        source ->
            RumenFillScoreToolItem.builder()
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
                .penName(source.getPenName())
                .rumenFillScoreVisitsSelected(
                    source.getRumenFillScoreVisitsSelected() != null
                        ? source.getRumenFillScoreVisitsSelected().stream()
                            .map(UUID::fromString)
                            .toList()
                        : null)
                .rumenFillScores(source.getRumenFillScores())
                .isToolItemNew(source.getIsToolItemNew())
                .isFirstTimeWithScore(source.getIsFirstTimeWithScore())
                .daysInMilk(source.getDaysInMilk())
                .build();

    CosmosToModelMapper<RumenFillToolCosmos, RumenFillTool> mapper =
        source ->
            RumenFillTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .goals(source.getGoals())
                .build();

    return mapper.map(input);
  }
}
