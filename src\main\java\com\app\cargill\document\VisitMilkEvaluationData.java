/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.ArrayList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.result.Outputs;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitMilkEvaluationData implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("Outputs")
  public Outputs outputs;

  @JsonProperty("Pickups")
  public ArrayList<Pickup> pickups;

  @JsonProperty("SelectedVisits")
  public ArrayList<String> selectedVisits;

  @JsonProperty("LactatingAnimals")
  public Long lactatingAnimals;

  @JsonProperty("AnimalsinTank")
  public Long animalsinTank;

  @JsonProperty("MilkPickup")
  public Long milkPickup;

  @JsonProperty("DryMatterIntake")
  public Long dryMatterIntake;

  @JsonProperty("DaysInMilk")
  public Long daysInMilk;

  @JsonProperty("MilkUreaMeasure")
  public Long milkUreaMeasure;
}
