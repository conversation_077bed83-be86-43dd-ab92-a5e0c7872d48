/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Enum naming intentional
public enum MarketingClassification {
  NotSet(0),
  SM(1),
  SMT(2),
  SMTC(3),
  // [EnumMember(Value = "Zgoda na przetwarzanie dan.os.")]
  Zgodanaprzetwarzaniedanos(4),
  // [EnumMember(Value = "Zgoda na działania mark.")]
  Zgodanadziałaniamark(5),
  // [EnumMember(Value = "Zgoda na udostępnianie")]
  Zgodanaudostępnianie(6);
  private final Integer classificationCode;

  MarketingClassification(Integer code) {
    this.classificationCode = code;
  }

  public Integer getClassificationCode() {
    return classificationCode;
  }
}
