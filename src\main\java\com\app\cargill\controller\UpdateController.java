/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.ForceUpdateDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.IForceUpdateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/buildInfo")
@Tag(name = "Build Update Controller", description = "Forces Frontend Build Update")
@RequiredArgsConstructor
public class UpdateController extends BaseController {

  private final IForceUpdateService iForceUpdateServiceImpl;

  @GetMapping
  @Operation(
      summary = "Get latest build versions for Android and IOS",
      description = "This method will return the latest build version for Android and IOS")
  public ResponseEntity<ResponseEntityDto<ForceUpdateDto>> getBuildInfo() {

    return handleSuccessResponse(iForceUpdateServiceImpl.getBuildInfo());
  }
}
