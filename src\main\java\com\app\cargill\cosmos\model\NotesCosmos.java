/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.document.DateEpoch;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

@Container(containerName = "Notes")
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
public class NotesCosmos {
  @Id
  @JsonProperty("id")
  @PartitionKey
  private String id;

  @JsonProperty("Note")
  private String note;

  @JsonProperty("Title")
  private String title;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Section")
  private Integer section;

  @JsonProperty("MediaItems")
  private List<NotesCosmosMediaItem> mediaItems = new ArrayList<>();

  @JsonProperty("Category")
  private Integer category;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("IsNew")
  private boolean isNew;

  @Getter
  @ToString
  @JsonIgnoreProperties(ignoreUnknown = true)
  @Setter
  public static class NotesCosmosMediaItem {
    @JsonProperty("MediaId")
    private String mediaId;

    @JsonProperty("NoteId")
    private String noteId;

    @JsonProperty("MediaType")
    private Integer mediaType;

    @JsonProperty("CreateUtc")
    @JsonDeserialize(using = InstantDateTimeDeserializer.class)
    private Instant createUtc;

    @JsonProperty("CreateUserId")
    private String createUserId;

    @JsonProperty("Latitude")
    private Double latitude;

    @JsonProperty("Longitude")
    private Double longitude;

    @JsonProperty("IsNew")
    private Boolean isNew;
  }
}
