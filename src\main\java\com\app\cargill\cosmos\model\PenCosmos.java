/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.FeedingSystem;
import com.app.cargill.constants.HousingSystem;
import com.app.cargill.constants.PenSource;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PenCosmos {
  @JsonProperty("Id")
  private String id;

  @JsonProperty("CustomerAccountId")
  private String customerAccountId;

  @JsonProperty("SiteId")
  private String siteId;

  @JsonProperty("BarnId")
  private String barnId;

  @JsonProperty("Source")
  private PenSource source;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Barn")
  private String barn;

  @JsonProperty("Animals")
  private Integer animals;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("Milk")
  private Double milk;

  @JsonProperty("MilkingFrequency")
  private Double milkingFrequency;

  @JsonProperty("HousingSystemType")
  private HousingSystem housingSystemType;

  @JsonProperty("FeedingSystemType")
  private FeedingSystem feedingSystemType;

  @JsonProperty("NumberOfStalls")
  private Integer numberOfStalls;

  @JsonProperty("DietId")
  private String dietId;

  @JsonProperty("DryMatterIntake")
  private Double dryMatterIntake;

  @JsonProperty("AsFedIntake")
  private Double asFedIntake;

  @JsonProperty("RationCostPerAnimal")
  private Double rationCostPerAnimal;

  @JsonProperty("IsDeleted")
  @Builder.Default
  private Boolean isDeleted = false;

  @JsonProperty("IsMapped")
  @Builder.Default
  private Boolean isMapped = false;

  @JsonProperty("AssociatedPens")
  private List<String> associatedPens;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("NetEnergyOfLactationDairy")
  private String netEnergyOfLactationDairy;

  @JsonProperty("DietSource")
  private DietSource dietSource;

  @JsonProperty("OptimizationType")
  private String optimizationType;

  @JsonProperty("GroupId")
  private String groupId;

  @JsonProperty("Selected")
  private boolean selected;

  @JsonProperty("OptimizationId")
  private Integer optimizationId;
}
