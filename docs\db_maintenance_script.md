1. 1.	Delete all accounts that do not have corresponding users present on the platform
```sql
delete from accounts where account_document->>'id' not in (
    SELECT acc.account_id
    FROM (
             select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
         ) acc
             JOIN users u on u.user_document->>'UserName' = acc.user_name
    GROUP BY acc.account_id);
```

2. Delete all account without GoldenRecordId
```sql
delete from accounts where NOT (account_document ? 'GoldenRecordId') OR account_document->>'GoldenRecordId' IS NULL
```

3. Delete all sites without AccountID
```sql
delete from sites where NOT (site_document ? 'AccountId') OR site_document->>'AccountId' IS NULL
```

4. Delete all sites without ExternalId
```sql
delete from sites where NOT (site_document ? 'ExternalId') OR site_document->>'ExternalId' is null;
```

5. Find sites with duplicate ExternalId (manually delete older records)
```sql
select sites.id, updated_date, sites.site_document->>'ExternalId' from sites join (
    select site_document->>'ExternalId' as ext, COUNT(site_document->>'ExternalId') from sites GROUP BY site_document->>'ExternalId' having COUNT(site_document->>'ExternalId') > 1  ORDER BY COUNT(site_document->>'ExternalId') desc
) s ON sites.site_document->>'ExternalId' = s.ext
order by sites.site_document->>'ExternalId', updated_date desc
```

6. Delete all pens without SiteId
```sql
select pens.id, pens.pen_document->>'Id' as pen_id, s.sid from pens left join (select site_document->>'id' as sid from sites) s ON pen_document->>'SiteId' = s.sid
where s.sid IS NULL
```
```sql
delete FROM pens where id IN (select pens.id from pens left join (select site_document->>'id' as sid from sites) s ON pen_document->>'SiteId' = s.sid
where s.sid IS NULL)
```
7. Delete diets without SiteId
```sql
 delete from diets where NOT (diet_document ? 'SiteId') OR diet_document->>'SiteId' IS NULL
```
8. Delete activities without VisitId or AccountId (this was applied on dev only)
```sql
delete from activities where NOT (activity_document ? 'VisitId') OR activity_document->>'VisitId' IS NULL OR NOT (activity_document ? 'AccountId') OR activity_document->>'AccountId' IS NULL
```
9. Delete notes without VisitId
```sql
delete from notes where NOT (notes_document ? 'VisitId') OR notes_document->>'VisitId' IS NULL
```

Sites with no relative account
```sql
select * from sites where site_document->>'AccountId' not in (select account_document->>'id' from accounts)
```

```sql
select sdata.acc_id, COALESCE(sdata.snum, 0) from (select site_document->>'AccountId' as acc_id, COUNT(site_document->>'AccountId') as snum from sites group by site_document->>'AccountId') sdata
LEFT JOIN accounts ON sdata.acc_id = accounts.account_document->>'id'
```