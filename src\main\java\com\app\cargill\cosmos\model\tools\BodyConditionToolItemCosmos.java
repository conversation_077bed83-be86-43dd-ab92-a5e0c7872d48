/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.BodyConditionToolItemScoreItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class BodyConditionToolItemCosmos {
  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("BodyConditionScoreVisitsSelected")
  private List<String> bodyConditionScoreVisitsSelected;

  @JsonProperty("BodyConditionScores")
  private List<BodyConditionToolItemScoreItem> bodyConditionScores;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("DaysInMilk")
  private int daysInMilk;

  @JsonProperty("Milk")
  private Double milk;

  @JsonProperty("IsToolItemNew")
  private boolean isToolItemNew;

  @JsonProperty("IsFirstTimeWithScore")
  private boolean isFirstTimeWithScore;
}
