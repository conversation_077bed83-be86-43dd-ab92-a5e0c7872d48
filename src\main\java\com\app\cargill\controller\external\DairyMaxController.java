/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller.external;

import com.app.cargill.dairymax.model.DairyMax;
import com.app.cargill.dairymax.response.MaxResponseDto;
import com.app.cargill.dairymax.service.IDairyMaxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dairymax")
@Tag(
    name = "Dairy Max Controller",
    description = "Endpoint for communication with Dairy Max service")
@RequiredArgsConstructor
@Slf4j
public class DairyMaxController {

  private final IDairyMaxService dairyMaxService;

  @PostMapping
  @Operation(
      summary = "Update site and diet data from DairyMax ",
      description =
          "This api will update the site and diets data " + "as received from Dairy Max service.")
  public ResponseEntity<MaxResponseDto> save(@Valid @NotNull @RequestBody List<DairyMax> maxData) {
    try {
      dairyMaxService.updateSiteFromMax(maxData);
      return ResponseEntity.ok()
          .body(new MaxResponseDto("Success", "Request successfully processed."));
    } catch (Exception e) {
      return ResponseEntity.internalServerError().body(new MaxResponseDto("Error", e.getMessage()));
    }
  }
}
