/* Cargill Inc.(C) 2022 */
package com.app.cargill.dairymax.model;

import com.app.cargill.constants.DietSource;
import com.app.cargill.document.AnimalClass;
import com.app.cargill.document.DietDocument;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MaxDiet {

  @JsonProperty("Id")
  private UUID maxId;

  @JsonProperty("LabyrinthAccountId")
  private UUID maxLabyrinthAccountId;

  @JsonProperty("SiteId")
  private UUID maxSiteId;

  @JsonProperty("Name")
  private String maxName;

  @JsonProperty("BreedName")
  private String maxBreedName;

  @JsonProperty("BreedId")
  private UUID maxBreedId;

  @JsonProperty("StartDate")
  private Instant maxStartDate;

  @JsonProperty("EndDate")
  private Instant maxEndDate;

  @JsonProperty("AnimalType")
  private AnimalClass maxAnimalType;

  @JsonProperty("BarnId")
  private UUID maxBarnId;

  @JsonProperty("EnvironmentId")
  private UUID maxEnvironmentId;

  @JsonProperty("EnvironmentName")
  private String maxEnvironmentName;

  @JsonProperty("ReportMilkWeight")
  private Double maxReportMilkWeight;

  @JsonProperty("NumberOfAnimals")
  private Integer maxNumberOfAnimals;

  @JsonProperty("AnalyzeOptimization")
  private AnalyzeDietOptimizationMax maxAnalyzeOptimization;

  @JsonProperty("FormulateOptimization")
  private FormulateDietOptimizationMax maxFormulateOptimization;

  @JsonProperty("Source")
  private DietSource maxSource;

  @JsonProperty("Selected")
  @Builder.Default
  private Boolean maxSelected = false;

  @JsonProperty("SelectedPenGuids")
  private List<UUID> maxSelectedPenGuids;

  /** Added this field for checking it for Animalclass and Subclasss and diet */
  @JsonProperty("IsSystemGenerated")
  @Builder.Default
  private Boolean maxIsSystemGenerated = false;

  @JsonProperty("IsDeleted")
  @Builder.Default
  private Boolean maxIsDeleted = false;

  @JsonProperty("IsActive")
  private Boolean maxIsActive;

  @JsonProperty("OptimizationId")
  private Integer maxOptimizationId;

  @JsonProperty("OptimizationType")
  private String maxOptimizationType;

  @JsonProperty("OptimizationStatus")
  private String maxOptimizationStatus;

  @JsonProperty("CreateUser")
  private String maxCreateUser;

  public MaxDiet(DietDocument diet) {
    this.maxAnimalType = diet.getAnimalType();
    this.maxBarnId = diet.getBarnId();
    this.maxBreedId = diet.getBreedId();
    this.maxBreedName = diet.getBreedName();
    this.maxCreateUser = diet.getCreateUser();
    this.maxEndDate = diet.getEndDate();
    this.maxEnvironmentId = diet.getEnvironmentId();
    this.maxEnvironmentName = diet.getEnvironmentName();
    this.maxId = diet.getId();
    this.maxIsActive = diet.getIsActive();
    this.maxIsDeleted = diet.getIsDeleted();
    this.maxIsSystemGenerated = diet.getIsSystemGenerated();
    this.maxLabyrinthAccountId = diet.getLabyrinthAccountId();
    this.maxName = diet.getName();
    this.maxNumberOfAnimals = diet.getNumberOfAnimals();
    this.maxOptimizationId = diet.getOptimizationId();
    this.maxOptimizationStatus = diet.getOptimizationStatus();
    this.maxOptimizationType = diet.getOptimizationType();
    this.maxReportMilkWeight = diet.getReportMilkWeight();
    this.maxSelected = diet.getSelected();
    this.maxSelectedPenGuids = diet.getSelectedPenGuids();
    this.maxSiteId = diet.getSiteId();
    this.maxSource = diet.getSource();
    this.maxStartDate = diet.getStartDate();
  }
}
