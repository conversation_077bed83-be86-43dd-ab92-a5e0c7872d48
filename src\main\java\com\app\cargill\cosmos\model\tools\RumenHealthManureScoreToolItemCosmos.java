/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.RumenHealthManureScores;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class RumenHealthManureScoreToolItemCosmos extends EditableToolPenEntityBaseCosmos {
  @JsonProperty("ManureScoreVisitsSelected")
  private List<String> manureScoreVisitsSelected;

  @JsonProperty("ManureScores")
  private RumenHealthManureScores manureScores;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;
}
