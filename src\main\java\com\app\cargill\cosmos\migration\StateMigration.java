/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.StateCosmos;
import com.app.cargill.cosmos.repo.StateCosmosRepository;
import com.app.cargill.document.StateDocument;
import com.app.cargill.model.States;
import com.app.cargill.repository.StatesRepository;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class StateMigration implements CosmosDataMigration {

  private final StateCosmosRepository cosmosRepository;
  private final StatesRepository statesRepository;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("States");
    try {
      List<States> statesList = fetchAll(migrationResult);
      statesRepository.saveAll(statesList);
      log.info("States migration completed. {} records copied to PostgreSQL", statesList.size());
    } catch (Exception e) {
      log.error("Error occurred during States migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  private List<States> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    Iterator<StateCosmos> cosmosIterator = cosmosRepository.findAll().iterator();
    List<States> statesList = new ArrayList<>();
    int failures = 0;
    while (cosmosIterator.hasNext()) {
      try {
        States state = new States(statesMapper.map(cosmosIterator.next()));
        state.setLocalId(state.getStateDocument().getId().toString());
        statesList.add(state);
      } catch (Exception e) {
        log.error("There was an error fetching a State from CosmosDB", e);
        failures++;
      }
    }
    log.info("{} states fetched from CosmosDB", statesList.size());
    if (failures > 0) {
      log.warn("{} states failed to map during the fetching process", failures);
    }
    migrationResult.setSucceeded(statesList.size());
    migrationResult.setFailed(failures);
    return statesList;
  }

  private final CosmosToModelMapper<StateCosmos, StateDocument> statesMapper =
      source ->
          StateDocument.builder()
              .id(UUID.fromString(source.getId()))
              .stateName(source.getStateName())
              .stateCode(source.getStateCode())
              .countryCode(source.getCountryCode())
              .build();
}
