/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.mapper.DietMapper;
import com.app.cargill.cosmos.model.DietCosmos;
import com.app.cargill.cosmos.repo.DietsCosmosRepository;
import com.app.cargill.model.Diets;
import com.app.cargill.repository.DietRepository;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class DietsMigration implements CosmosDataMigration {
  private final DietsCosmosRepository cosmosRepository;
  private final DietRepository dietRepository;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("Diets");
    try {
      log.info("Diets migration started ...");
      List<Diets> dietsList = fetchAll(migrationResult);
      dietRepository.saveAll(dietsList);
      log.info("Diets migration completed. {}", migrationResult);
    } catch (Exception e) {
      log.error("Error occurred during Diets migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  public Flux<Diets> moveRecords(String siteId) {
    Flux<Diets> cosmosRecords =
        processCosmosFlux(cosmosRepository.findAllBySiteId(siteId), new AtomicInteger(0));
    return processRecords(cosmosRecords);
  }

  private Flux<Diets> processCosmosFlux(Flux<DietCosmos> records, AtomicInteger failedItems) {
    return records
        .map(
            dietCosmos -> {
              Diets dietMapping = new Diets(DietMapper.map(dietCosmos));
              dietMapping.setLocalId(dietMapping.getDietDocument().getId().toString());
              return dietMapping;
            })
        .onErrorContinue(
            (throwable, object) -> {
              failedItems.incrementAndGet();
              log.error("Error during Diets migration: {}", object);
              log.error("Error", throwable);
            });
  }

  private Flux<Diets> processRecords(Flux<Diets> records) {
    return records
        .filter(d -> !Boolean.TRUE.equals(d.getDietDocument().getIsDeleted()) && !d.isDeleted())
        .flatMap(
            diet ->
                Mono.fromCallable(
                    () -> {
                      try {
                        Diets existingRecord =
                            dietRepository.findById(diet.getDietDocument().getId().toString());
                        return Objects.requireNonNullElseGet(
                            existingRecord, () -> dietRepository.save(diet));
                      } catch (Exception e) {
                        throw new MigrationException("Error during Diets process", e);
                      }
                    }))
        .subscribeOn(Schedulers.boundedElastic())
        .onErrorContinue(
            (t, o) -> {
              log.error("Error with object: {}", o);
              log.error("Error during Diet save in DB", t);
            });
  }

  private List<Diets> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    Iterator<DietCosmos> allDietsIterator = cosmosRepository.findAll().toIterable().iterator();
    List<Diets> dietsList = new ArrayList<>();
    int failures = 0;
    while (allDietsIterator.hasNext()) {
      try {
        Diets diet = new Diets(DietMapper.map(allDietsIterator.next()));
        diet.setLocalId(diet.getDietDocument().getId().toString());
        dietsList.add(diet);
      } catch (Exception e) {
        log.error("There was an error fetching a diet from CosmosDB", e);
        failures++;
      }
    }
    log.info("{} diets fetched from CosmosDB", dietsList.size());
    if (failures > 0) {
      log.warn("{} diets failed to map during the fetching process", failures);
    }
    migrationResult.setSucceeded(dietsList.size());
    migrationResult.setFailed(failures);
    return dietsList;
  }

  @Async
  public CompletableFuture<MigrationResult> postMigration() {
    return CompletableFuture.completedFuture(new MigrationResult("Diets Post Migration"));
  }

  @Override
  @Async
  public CompletableFuture<MigrationResult> migrationFix(String fixesToRun) {

    var lambdaContext =
        new Object() {
          Integer count = 0;
        };
    if (fixesToRun != null
        && (fixesToRun.equals(MigrationFix.DIETS.toString())
            || fixesToRun.equals(MigrationFix.ALL.toString()))) {
      log.info("Starting Diets migration fix");
      List<DietCosmos> cosmosDiets = cosmosRepository.findAllDiets().collectList().block();
      assert cosmosDiets != null;
      log.info("Diets Fetched from cosmos where fields are missing: " + cosmosDiets.size());
      List<Diets> postgresDiets = dietRepository.findAll();
      log.info("Diets Fetched from Postgres :" + postgresDiets.size());
      List<Diets> dietsToUpdate = new ArrayList<>();
      cosmosDiets.stream()
          .forEach(
              cd ->
                  postgresDiets.parallelStream()
                      .forEach(
                          pd -> {
                            if (pd.getDietDocument().getId().toString().equals(cd.getId())) {
                              pd.getDietDocument().setSiteId(UUID.fromString(cd.getSiteId()));
                              pd.getDietDocument().setOptimizationId(cd.getOptimizationId());
                              pd.getDietDocument().setOptimizationType(cd.getOptimizationType());
                              pd.getDietDocument()
                                  .setOptimizationStatus(cd.getOptimizationStatus());
                              pd.getDietDocument().setCreateUser(cd.getCreateUser());
                              dietsToUpdate.add(pd);
                              lambdaContext.count++;
                            }
                          }));
      dietRepository.saveAllAndFlush(postgresDiets);
      log.info("RECORDS UPDATED FOR DIETS MISSING FIELDS : " + lambdaContext.count);
      log.info("MIGRATION FIX COMPLETED");
    }
    return CompletableFuture.completedFuture(
        new MigrationResult("Data Fixed", lambdaContext.count, 0));
  }
}
