/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.PricingBasis;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkProcessorListItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Type")
  private PricingBasis type;

  @JsonProperty("Id")
  private UUID id;

  @JsonProperty("Selected")
  private Boolean selected;
}
