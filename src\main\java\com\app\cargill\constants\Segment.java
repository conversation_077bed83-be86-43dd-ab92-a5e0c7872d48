/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import java.util.stream.Stream;

@SuppressWarnings("java:S115")
public enum Segment {
  NotSet(0),
  <PERSON>(1),
  <PERSON>(9),
  <PERSON>(8),
  <PERSON><PERSON><PERSON>(10),
  <PERSON>(4),
  <PERSON><PERSON>(2),
  <PERSON>(6),
  <PERSON><PERSON>(7),
  <PERSON>(3),
  <PERSON>(5);
  private final Integer segmentCode;

  Segment(Integer code) {
    this.segmentCode = code;
  }

  public Integer getSegmentValue() {
    return this.segmentCode;
  }

  public static Integer getValueFromName(String name) {
    return Stream.of(Segment.values())
        .filter(s -> s.name().equals(name))
        .findAny()
        .orElse(Segment.Noah)
        .getSegmentValue();
  }
}
