/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.LocomotionHerdToolItemCategoryItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class LocomotionHerdToolItemCosmos {
  @JsonProperty("Categories")
  private List<LocomotionHerdToolItemCategoryItem> categories;

  @JsonProperty("PensForVisit")
  private List<LocomotionToolItemCosmos> pensForVisit;

  @JsonProperty("DaysInMilk")
  private Double daysInMilk;

  @JsonProperty("TotalAnimalsInHerd")
  private Integer totalAnimalsInHerd;

  @JsonProperty("MilkProductionInKg")
  private Double milkProductionInKg;

  @JsonProperty("MilkPriceAtSiteLevel")
  private Double milkPriceAtSiteLevel;
}
