/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.MilkProcessorListItemCosmos;
import com.app.cargill.cosmos.model.tools.RevenueInputItemCosmos;
import com.app.cargill.cosmos.model.tools.RevenueInputsCosmos;
import com.app.cargill.document.MilkProcessorListItem;
import com.app.cargill.document.RevenueInputItem;
import com.app.cargill.document.RevenueInputs;
import java.util.UUID;

public class RevenueInputsMapper {

  private RevenueInputsMapper() {}

  public static RevenueInputs map(RevenueInputsCosmos input) {

    CosmosToModelMapper<RevenueInputItemCosmos, RevenueInputItem> itemMapper =
        source ->
            RevenueInputItem.builder()
                .scenarioTitle(source.getScenarioTitle())
                .scenarioTitleText(source.getScenarioTitleText())
                .scenarioOneValue(source.getScenarioOneValue())
                .scenarioTwoValue(source.getScenarioTwoValue())
                .visitId(UUID.fromString(source.getVisitId()))
                .toolStatus(source.getToolStatus())
                .build();

    CosmosToModelMapper<MilkProcessorListItemCosmos, MilkProcessorListItem> processorMapper =
        source ->
            MilkProcessorListItem.builder()
                .id(UUID.fromString(source.getId()))
                .name(source.getName())
                .type(source.getType())
                .selected(source.getSelected())
                .build();

    CosmosToModelMapper<RevenueInputsCosmos, RevenueInputs> mapper =
        source ->
            RevenueInputs.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .selectedProcessor(
                    source.getSelectedProcessor() != null
                        ? processorMapper.map(source.getSelectedProcessor())
                        : null)
                .items(
                    source.getItems() != null
                        ? source.getItems().stream().map(itemMapper::map).toList()
                        : null)
                .scenarioItems(source.getScenarioItems())
                .build();

    return mapper.map(input);
  }
}
