app.debug.web-client=false
app.scheduling.enable=false
cloud.aws.region=${AWS_DEFAULT_REGION:us-east-1}
cloud.aws.s3.bucket-name=${BUCKET_DEDISCOVER_NAME:cargill-customer-portraits}
cloud.aws.s3.access-key=${AWS_ACCESS_KEY_ID:********************}
cloud.aws.s3.secret-key=${AWS_SECRET_ACCESS_KEY:3Tceu8t88E59S6M5Kffnskg7FoSndFxFaI6llbIe}
cosmos.uri=https://crescendo-preprod.documents.azure.com:443/
cosmos.key=****************************************************************************************
cosmos.queryMetricsEnabled=false
cosmos.database=preprod-prodclone
logging.config=classpath:logback.xml
logging.level.root=info
login.okta.oauth2.issuer=https://cargillcustomer-qa.oktapreview.com/oauth2/aushb5mlqe4IiZu3k0h7
login.okta.oauth2.client-id=0oajmlp89rXYEEGxG0h7
login.azure.oauth2.tenant-id=57368c21-b8cf-42cf-bd0b-43ecd4bc62ae
login.azure.oauth2.issuer=https://sts.windows.net/${login.azure.oauth2.tenant-id}/
login.azure.oauth2.client-id=ea8bccbf-17d4-447c-8e61-53e08ae27625
salesforce.lift.token-host=cargillanh--anhqa.sandbox.my.salesforce.com
salesforce.lift.token-path=/services/oauth2/token
salesforce.lift.client-secret=****************************************************************
salesforce.lift.client-id=3MVG98bly0AKZIIXbXfaAkxa19JkGSBR2DZgQAELUdEMGilx.7ZZYfbGihxWIdiafGWdFg79NX8Mwqv6qs.nS
salesforce.lift.default-owner-id=<EMAIL>
spring.datasource.url=${DB_CONNECTION:****************************************}
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.show_sql=false
spring.sql.init.mode=never
spring.data.rest.default-media-type=application/json
springdoc.swagger-ui.base-url-private-postfix=${SWAGGER_BASE_URL_POSTFIX:/}
springdoc.swagger-ui.base-url-public-postfix=${SWAGGER_BASE_URL_POSTFIX:/}
springdoc.swagger-ui.oauth.client-id=clientId
springdoc.swagger-ui.oauth.client-secret=secret
springdoc.swagger-ui.oauth.use-basic-authentication-with-access-code-grant=true
springdoc.swagger-ui.oauth.scope-separator= 
springdoc.swagger-ui.oauth.access-token-url=${SWAGGER_ACCESS_TOKEN_URL:http://localhost:8080/auth/okta-token}

#sharepoint properties
sharepoint.post-file-url=/sites/labyrinthTest/_api/web/GetFolderByServerRelativeUrl({0}/sites/labyrinthTest/Shared%20Documents/{1}{2})/Files/add(url={3}{4}{5},overwrite=true)
sharepoint.create-folder-url=/sites/labyrinthTest/_api/Web/Folders/add({0}/sites/labyrinthTest/Shared%20Documents/{1}{2})
sharepoint.check-folder-exists-url=/sites/labyrinthTest/_api/web/GetFolderByServerRelativeUrl({0}/sites/labyrinthTest/Shared%20Documents/{1}{2})/Exists
sharepoint.visit-report-file-url=https://cargillonline.sharepoint.com/sites/labyrinthTest/Shared%20Documents/{0}/{1}

#azure blob
azure.blob.storage.connection=${AZURE_BLOB_STORAGE_CRED:************************************************************************************************************************************************************************************************************************************************************}

#DairyForecast Connection
spring.security.oauth2.client.registration.dairyforecast.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.dairyforecast.client-id=4135ff52-a9fa-4c56-a171-023b0fa51f97
spring.security.oauth2.client.registration.dairyforecast.client-secret=****************************************
spring.security.oauth2.client.registration.dairyforecast.scope=fcce74cd-f098-4844-8fe7-4efffd39041c/.default
spring.security.oauth2.client.provider.dairyforecast.token-uri=https://login.microsoftonline.com/57368c21-b8cf-42cf-bd0b-43ecd4bc62ae/oauth2/v2.0/token
app.dairyforecast.url=https://dairyforecastapisync.stage.cglcloud.in/api/DairyForecast

api.admin.id=ccf6baea-ce19-4bc8-b8cd-8c990ea358d5


#salesforce Crescendo properties
salesforce.crescendo.token-host=cargill18--preprod.sandbox.my.salesforce.com
salesforce.crescendo.token-path=/services/oauth2/token
salesforce.crescendo.client-secret=${SALESFORCE_CRESCENDO_SECRET:****************************************************************}
salesforce.crescendo.client-id=${SALESFORCE_CRESCENDO_CLIENT_ID:3MVG9iBaHQRfreBmcng3LzB7txKf.Cgl3feXThDZP8oETXSnatzJOUHovst75ApsQDnRRxQvsJ5i4ee0h4J3t}
salesforce.crescendo.default-owner-id=Crescendo_DE_Integration


#sharepoint urls
sharepoint.grant_type=client_credentials
sharepoint.client_id=${CAN_SHAREPOINT_CLIENT_ID:d3777710-8753-4f7a-b1d9-26e3da4c52a7}
sharepoint.client_secret=${CAN_SHAREPOINT_CLIENT_SECRET:****************************************}
sharepoint.resource=${CAN_SHAREPOINT_RESOURCE:********-0000-0ff1-ce00-************/cargillonline.sharepoint.com@57368c21-b8cf-42cf-bd0b-43ecd4bc62ae}
sharepoint.tenant_id=57368c21-b8cf-42cf-bd0b-43ecd4bc62ae
sharepoint.authority=https://accounts.accesscontrol.windows.net/${sharepoint.tenant_id}/tokens/OAuth/2
sharepoint.web_url=https://cargillonline.sharepoint.com/sites/dairyenteligen
sharepoint.instance-url=https://cargillonline.sharepoint.com
sharepoint.file_url=/sites/dairyenteligen/Shared Documents/DE_Reports
sharepoint.summary_file_name_with_suffix=true
sharepoint.detailed_file_name_with_suffix=true
