/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import java.util.concurrent.CompletableFuture;
import org.springframework.scheduling.annotation.Async;

public interface CosmosDataMigration {
  CompletableFuture<MigrationResult> moveAll();

  @Async
  default CompletableFuture<MigrationResult> postMigration(String migrationType) {
    return CompletableFuture.completedFuture(new MigrationResult("Post Migration"));
  }

  @Async
  default CompletableFuture<MigrationResult> migrationFix(String fixesToRun) {
    return CompletableFuture.completedFuture(new MigrationResult("Migration FIX"));
  }

  default MigrationType migrationType() {
    return MigrationType.UNKNOWN;
  }

  public enum MigrationType {
    ACCOUNTS,
    CONTENT_DETAILS,
    NOTES,
    MILK_PROCESSORS,
    ACTIVITIES,

    SITES,
    COUNTRY_TOOLS,

    COUNTRY,
    DIETS,

    STATE,
    USER_PREFERENCES,
    USER,

    VISIT,
    UNKNOWN,
    ALL;
  }

  enum MigrationFix {
    ACCOUNTS,
    COUNTRIES,
    COUNTRY_TOOLS,
    DIETS,
    SITE_MAPPINGS,
    SITES,
    STATES,
    USER_PREFERENCES,
    VISITS,

    PENS,
    ALL;
  }
}
