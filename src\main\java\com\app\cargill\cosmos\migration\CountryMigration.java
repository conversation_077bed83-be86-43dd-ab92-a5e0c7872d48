/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.CountryCosmos;
import com.app.cargill.cosmos.repo.CountryCosmosRepository;
import com.app.cargill.document.CountryDocument;
import com.app.cargill.model.Countries;
import com.app.cargill.repository.CountriesRepository;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CountryMigration implements CosmosDataMigration {

  private final CountryCosmosRepository cosmosRepository;
  private final CountriesRepository countriesRepository;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("Country");
    try {
      List<Countries> countriesList = fetchAll(migrationResult);
      countriesRepository.saveAll(countriesList);
      log.info(
          "Countries migration completed. {} records copied to PostgreSQL", countriesList.size());
    } catch (Exception e) {
      log.error("Error occurred during Countries migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  private List<Countries> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    Iterator<CountryCosmos> cosmosIterator = cosmosRepository.findAll().iterator();
    List<Countries> countriesList = new ArrayList<>();
    int failures = 0;
    while (cosmosIterator.hasNext()) {
      try {
        Countries country = new Countries(countriesMapper.map(cosmosIterator.next()));
        country.setLocalId(country.getCountryDocument().getId().toString());
        countriesList.add(country);
      } catch (Exception e) {
        log.error("There was an error fetching a Country from CosmosDB", e);
        failures++;
      }
    }
    log.info("{} countries fetched from CosmosDB", countriesList.size());
    if (failures > 0) {
      log.warn("{} countries failed to map during the fetching process", failures);
    }
    migrationResult.setSucceeded(countriesList.size());
    migrationResult.setFailed(failures);
    return countriesList;
  }

  private final CosmosToModelMapper<CountryCosmos, CountryDocument> countriesMapper =
      source ->
          CountryDocument.builder()
              .id(UUID.fromString(source.getId()))
              .countryName(source.getCountryName())
              .countryCode(source.getCountryCode())
              .countryBusinessIdMapping(source.getCountryBusinessIDMapping())
              .build();
}
