/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import java.util.Objects;
import lombok.Getter;

@SuppressWarnings("java:S115") // Constants Naming
public enum Business {
  Global(0),
  Brazil(1),
  Canada(2),
  France(3),
  Netherlands(4),
  Philippines(5),
  Poland(6),
  US(7),
  Vietnam(8),
  Spain(9),
  Italy(10),
  Korea(11),
  India(12),
  Mexico(13),
  Russia(14),
  SouthAfrica(15),
  CPNBrazil(16),
  CPNFrance(17),
  NorthAmerica(18),
  China(19),
  Portugal(20),
  Ukraine(21),
  CFNChina(22),
  CFNIndia(23),
  CPNPoland(24),
  CPNUS(25),
  Hungary(26),
  UK(27),
  Pakistan(28),
  UNITED_STATES(29),
  Argentina(30),
  Neolait_FRANCE(31),
  Provimi_FRANCE(32);

  @Getter private final Integer businessId;

  Business(Integer businessId) {
    this.businessId = businessId;
  }

  public static Business fromId(Integer id) {
    for (Business type : values()) {
      if (Objects.equals(type.getBusinessId(), id)) {
        return type;
      }
    }
    return null;
  }

  public static Business handleCountryId(Business countryId) {

    String CPN = "CPN";
    String CFN = "CFN";
    Business ret = null;
    for (Business type : Business.values()) {
      if (Objects.equals(type.getBusinessId(), countryId.getBusinessId())) {
        String countryName = type.name();
        if (null != countryName && (countryName.contains(CPN) || countryName.contains(CFN))) {
          countryName = countryName.substring(CPN.length());
          // log.info("countryName After-- "+countryName);
          ret = Business.valueOf(countryName);
        } else {
          ret = type;
        }
      }
    }

    return ret;
  }
}
