/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.AccountType;
import com.app.cargill.converter.AccountTypeToEnumDeserializer;
import com.app.cargill.converter.AccountTypeToIntegerDeserializer;
import com.app.cargill.converter.AvailabilityOnMarketDeserializer;
import com.app.cargill.converter.BusinessIdDeserializer;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.converter.SubTypeIdDeserializer;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.document.UserRole;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Instant;
import java.util.List;
import lombok.Getter;
import org.springframework.data.annotation.Id;

@Container(containerName = "Accounts")
@Getter
public class AccountCosmos {
  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("GoldenRecordId")
  private String goldenRecordId;

  @JsonProperty("AccountName")
  private String accountName;

  @JsonProperty("LegalName")
  private String legalName;

  @JsonProperty("Type")
  private String type;

  @JsonProperty("AccountType")
  @JsonDeserialize(using = AccountTypeToIntegerDeserializer.class)
  private Integer accountType;

  @JsonProperty("Contacts")
  private List<ContactCosmos> contacts;

  @JsonProperty("PrimaryContactId")
  private String primaryContactId;

  @JsonProperty("PrimaryContactTitle")
  private String primaryContactTitle;

  @JsonProperty("PrimaryContactPhoneNumber")
  private String primaryContactPhoneNumber;

  @JsonProperty("LastUpdateUserId")
  private String lastUpdateUserId;

  @JsonProperty("AccountValidated")
  private Boolean accountValidated;

  @JsonProperty("PhotoId")
  private String photoId;

  @JsonProperty("NeedsSync")
  private Boolean needsSync;

  @JsonProperty("Users")
  private List<String> users;

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("ProspectStatus")
  private com.app.cargill.constants.ProspectStatus prospectStatus;

  @JsonProperty("IsDuplicate")
  private Boolean isDuplicate;

  @JsonProperty("AutoValidate")
  private Boolean autoValidate;

  @JsonProperty("SourceSystem")
  private String sourceSystem;

  @JsonProperty("ParentAccountID")
  private String parentAccountID;

  @JsonProperty("SubTypeID")
  @JsonDeserialize(using = SubTypeIdDeserializer.class)
  private Integer subTypeID;

  @JsonProperty("ExternalLeadSourceID")
  private Integer externalLeadSourceID;

  @JsonProperty("BuyingGroupID")
  private String buyingGroupID;

  @JsonProperty("ExternalBuyingGroupID")
  private String externalBuyingGroupID;

  @JsonProperty("AccountStatus")
  private String accountStatus;

  @JsonProperty("DateOfLastVisit")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant dateOfLastVisit;

  @JsonProperty("DateOfLastCall")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant dateOfLastCall;

  @JsonProperty("WonLostId")
  private Integer wonLostId;

  @JsonProperty("WonLostComments")
  private String wonLostComments;

  @JsonProperty("CreditFlag")
  private Boolean creditFlag;

  @JsonProperty("PriceFlag")
  private Boolean priceFlag;

  @JsonProperty("ServiceFlag")
  private Boolean serviceFlag;

  @JsonProperty("PerformanceFlag")
  private Boolean performanceFlag;

  @JsonProperty("PortfolioFlag")
  private Boolean portfolioFlag;

  @JsonProperty("BusinessSolutionFlag")
  private Boolean businessSolutionFlag;

  @JsonProperty("QualityFlag")
  private Boolean qualityFlag;

  @JsonProperty("OtherFlag")
  private Boolean otherFlag;

  @JsonProperty("BusinessID")
  @JsonDeserialize(using = BusinessIdDeserializer.class)
  private Integer businessID;

  @JsonProperty("DefaultCargillPlantID")
  private Integer defaultCargillPlantID;

  @JsonProperty("DefaultCustServiceID")
  private Integer defaultCustServiceID;

  @JsonProperty("LastModificationDate")
  private DateEpoch lastModificationDate;

  @JsonProperty("Active") // Epoch datetime is commented in base model
  private Boolean active;

  @JsonProperty("BrandId")
  private Integer brandId;

  @JsonProperty("NineBoxStepTwoID")
  private String nineBoxStepTwoID;

  @JsonProperty("SegmentStepOneId")
  private String segmentStepOneId;

  @JsonProperty("CompanyEmail")
  private String companyEmail;

  @JsonProperty("LastInvoiceDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastInvoiceDate;

  @JsonProperty("LastOrderDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastOrderDate;

  @JsonProperty("DeliveryInstructions")
  private String deliveryInstructions;

  @JsonProperty("ERPPayerId")
  private String erpPayerId;

  @JsonProperty("ERPShipToId")
  private String erpShipToId;

  @JsonProperty("IsServicedbyCSPro")
  private Boolean isServicedbyCSPro;

  @JsonProperty("LastAdminUpdate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastAdminUpdate;

  @JsonProperty("LastInvoicesInfo")
  private String lastInvoicesInfo;

  @JsonProperty("LastOrdersInfo")
  private String lastOrdersInfo;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("ReqProcessingLog")
  private String reqProcessingLog;

  @JsonProperty("Liabilities")
  private String liabilities;

  @JsonProperty("LimitChangeReasonId")
  private Integer limitChangeReasonId;

  @JsonProperty("MarketInfluencer")
  private Boolean marketInfluencer;

  @JsonProperty("OtherActivityProduction")
  private String otherActivityProduction;

  @JsonProperty("PersonalID")
  private String personalID;

  @JsonProperty("PreviousStatus")
  private String previousStatus;

  @JsonProperty("ReasonDescription")
  private String reasonDescription;

  @JsonProperty("Securities")
  private String securities;

  @JsonProperty("Assets")
  private String assets;

  @JsonProperty("VolumeEstimate")
  private String volumeEstimate;

  @JsonProperty("MarginEstimate")
  private String marginEstimate;

  @JsonProperty("PhysicalAddress")
  private AddressCosmos physicalAddress;

  @JsonProperty("CorrespondenceAddress")
  private AddressCosmos correspondenceAddress;

  @JsonProperty("SocialMediaAddress")
  private String socialMediaAddress;

  @JsonProperty("WebSiteAddress")
  private String webSiteAddress;

  @JsonProperty("lstOtherBU")
  private List<Integer> lstOtherBU;

  @JsonProperty("ExternalParentAccountID")
  private String externalParentAccountId;

  @JsonProperty("AccountCurrency")
  private Integer accountCurrency;

  @JsonProperty("ApprovalStatus")
  private String approvalStatus;

  @JsonProperty("AccountNumber")
  private String accountNumber;

  @JsonProperty("AvailabilityOnMarket")
  @JsonDeserialize(using = AvailabilityOnMarketDeserializer.class)
  private Integer availabilityOnMarket;

  @JsonProperty("ChangeAccountType")
  private Boolean changeAccountType;

  @JsonProperty("NewAccountType")
  @JsonDeserialize(using = AccountTypeToEnumDeserializer.class)
  private AccountType newAccountType;

  @JsonProperty("ConsumerStatus")
  private Integer consumerStatus;

  @JsonProperty("CustomerStatus")
  private Integer customerStatus;

  @JsonProperty("CourtId")
  private String courtId;

  @JsonProperty("ERPIdLength")
  private String erpIdLength;

  @JsonProperty("ERPPayerIdLength")
  private String erpPayerIdLength;

  @JsonProperty("ERPShiptoIdLength")
  private String erpShiptoIdLength;

  @JsonProperty("IsMobileFirst")
  private Boolean isMobileFirst = true;

  @JsonProperty("VeterinaryId")
  private String veterinaryId;

  @JsonProperty("WonLost")
  private String wonLost;

  @JsonProperty("WonLostReasonCode")
  private String wonLostReasonCode;

  @JsonProperty("CurrentUserProfileNameandId")
  private String currentUserProfileNameandId;

  @JsonProperty("ERPIdLengthvalidatoriserror")
  private String erpIdLengthValidatorIsError;

  @JsonProperty("ExternalId")
  private String externalId;

  @JsonProperty("LastModifiedBy")
  private String lastModifiedBy;

  @JsonProperty("OwnerProfileNameandId")
  private String ownerProfileNameandId;

  @JsonProperty("CustomerCode")
  private String customerCode;

  @JsonProperty("isFavourite")
  private boolean isFavourite;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("AdditionalInfo")
  private AdditionalInformationCosmos additionalInfo;

  @JsonProperty("SalesTerritory")
  private String salesTerritory;

  @JsonProperty("SubBrandId")
  private Integer subBrandId;

  @JsonProperty("SiteCount")
  private Integer siteCount = 0;

  @JsonProperty("UserRoles")
  private List<UserRole> userRoles;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private boolean isNew;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;
}
