/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder(toBuilder = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class EditableDocumentBase implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("id")
  public UUID id;

  @JsonProperty("CreateUser")
  public String createUser;

  @JsonProperty("IsDeleted")
  public boolean isDeleted;

  @JsonProperty("MobileLastUpdatedTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant mobileLastUpdatedTime;

  @JsonProperty("LastModifyUser")
  public String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant lastModifiedTimeUtc = Instant.MIN;

  @JsonProperty("LastSyncTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  public boolean isNew;
}
