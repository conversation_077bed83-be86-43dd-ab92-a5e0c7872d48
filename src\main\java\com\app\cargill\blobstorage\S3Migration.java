/* Cargill Inc.(C) 2022 */
package com.app.cargill.blobstorage;

import com.app.cargill.cosmos.migration.MigrationResult;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.UserMigrationRepository;
import com.app.cargill.service.impl.AwsCredentialsFactory;
import com.app.cargill.service.impl.BlobServiceClientFactory;
import com.azure.core.http.rest.PagedIterable;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.models.BlobContainerItem;
import com.azure.storage.blob.models.BlobItem;
import com.azure.storage.blob.models.ListBlobContainersOptions;
import java.io.ByteArrayOutputStream;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.transfer.s3.S3TransferManager;
import software.amazon.awssdk.transfer.s3.model.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class S3Migration {
  @Value("${cloud.aws.s3.bucket-name}")
  @Setter
  private String bucketName;

  @Value("${cloud.aws.region}")
  @Setter
  private String awsRegion;

  private final BlobServiceClientFactory blobServiceClientFactory;
  private final AwsCredentialsFactory credentialsFactory;
  private S3AsyncClient s3Client;
  private S3TransferManager tm;
  private final UserMigrationRepository userMigrationRepository;
  private final AccountsRepository accountsRepository;

  public void init() {

    s3Client =
        S3AsyncClient.builder()
            .region(Region.of(awsRegion))
            .credentialsProvider(credentialsFactory.getCredentialsProvider())
            .build();
    log.debug("[S3Migration][migrate] connected to S3 Client...");
    tm = S3TransferManager.builder().s3Client(s3Client).build();
    log.debug("[S3Migration][migrate] Created S3TransferManager ...");
  }

  @Async
  public CompletableFuture<MigrationResult> migrate(String email) {
    try {
      List<String> accountIds =
          accountsRepository.findAccountIdsByEmailIn(Collections.singletonList(email));
      return migrate(accountIds, 1000, false);
    } catch (Exception e) {
      log.error("S3_MIGRATION_ERROR", e);
      return CompletableFuture.failedFuture(e);
    }
  }

  @Async
  public CompletableFuture<MigrationResult> migrate(
      Integer numberOfContainers, boolean migrateAllAccounts) throws CustomDEExceptions {
    List<String> accountIds = new ArrayList<>();
    if (!migrateAllAccounts) {
      List<String> userMigrationEmails = userMigrationRepository.findAllEmailByDeletedFalse();
      accountIds = accountsRepository.findAccountIdsByEmailIn(userMigrationEmails);
      log.debug(
          "[S3Migration][migrate] Migrating Selective accounts only size : " + accountIds.size());
    }
    return migrate(accountIds, numberOfContainers, migrateAllAccounts);
  }

  public CompletableFuture<MigrationResult> migrate(
      List<String> accountIds, Integer numberOfContainers, boolean migrateAllAccounts)
      throws CustomDEExceptions {

    log.debug("[S3Migration][migrate] Entering...");
    init();
    log.debug("[S3Migration][migrate] Initializing S3 Client Done...");
    MigrationResult migrationResult = new MigrationResult("Azure Blob to S3 Migration.");
    ExecutorService executorService =
        Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    try {
      BlobServiceClient blobServiceClient = blobServiceClientFactory.getBlobClient();
      log.debug("[S3Migration][migrate] connected to blob Service Client...");

      ListBlobContainersOptions containersOptions = new ListBlobContainersOptions();
      containersOptions.setMaxResultsPerPage(numberOfContainers);

      PagedIterable<BlobContainerItem> containersIterator =
          blobServiceClient.listBlobContainers(containersOptions, Duration.ofMinutes(2));

      log.debug(
          "[S3Migration][migrate] total number of Containers: "
              + containersIterator.stream().count());

      List<Future<BlobContainerItem>> futures =
          containersIterator.stream()
              .parallel()
              .filter(con -> migrateAllAccounts || accountIds.contains(con.getName().trim()))
              .map(
                  container ->
                      executorService.submit(
                          () -> {
                            BlobContainerClient containerClient =
                                blobServiceClient.getBlobContainerClient(container.getName());
                            List<String> blobs =
                                containerClient.listBlobs().stream()
                                    .map(BlobItem::getName)
                                    .toList();
                            log.debug(
                                "[S3Migration][migrate] Container Name: "
                                    + container.getName()
                                    + ", BlobItems: "
                                    + blobs.size());
                            downloadAndUpload(tm, containerClient, blobs);
                            return container;
                          }))
              .toList();

      processFutures(executorService, futures);
      log.debug("[S3Migration][migrate] Uploaded Successfully..");

    } catch (Exception ex) {
      log.error(
          "[S3Migration][migrate] error in initializing client and fetching containers: "
              + ex.getMessage(),
          ex);
      throw new CustomDEExceptions(ex.getMessage(), HttpStatus.BAD_REQUEST.value());
    } finally {
      executorService.shutdown(); // Initiate an orderly shutdown
      try {
        if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
          executorService.shutdownNow(); // Force shutdown
          if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
            log.error("[S3Migration][migrate] ExecutorService did not terminate.");
          }
        }
      } catch (InterruptedException ex) {
        executorService.shutdownNow();
        Thread.currentThread().interrupt(); // Preserve interrupt status
      }
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  private void processFutures(
      ExecutorService executorService, List<Future<BlobContainerItem>> futures)
      throws CustomDEExceptions {

    for (Future<BlobContainerItem> future : futures) {
      try {
        future.get();
      } catch (InterruptedException | ExecutionException e) {
        log.error("[S3Migration][processFutures] " + e.getMessage());
        executorService.shutdown();
        Thread.currentThread().interrupt();
        throw new CustomDEExceptions(e.getMessage(), HttpStatus.BAD_REQUEST.value());
      }
    }
  }

  private void downloadAndUpload(
      S3TransferManager tm, BlobContainerClient client, List<String> blobsList) {
    for (String blob : blobsList) {
      try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
        log.debug("[S3Migration][downloadAndUpload] Downloading blobItem: " + blob);
        client.getBlobClient(blob).downloadStream(out);
        uploadBlobItemToS3(blob, out, tm, client);
      } catch (Exception ex) {
        log.error(
            "[S3Migration][downloadAndUpload] download error for blob "
                + blob
                + " "
                + ex.getMessage());
      }
    }
  }

  public void uploadBlobItemToS3(
      String blob, ByteArrayOutputStream out, S3TransferManager tm, BlobContainerClient client) {

    String key = client.getBlobContainerName().concat("/").concat(getModifiedFileName(blob));
    log.debug("[S3Migration][downloadAndUpload] Uploading blobItem: " + key);

    UploadRequest uploadRequest =
        UploadRequest.builder()
            .requestBody(AsyncRequestBody.fromBytes(out.toByteArray()))
            .putObjectRequest(req -> req.bucket(bucketName).key(key))
            .build();

    Upload upload = tm.upload(uploadRequest);
    // Wait for the transfer to complete
    if (upload.completionFuture() != null) {
      CompletedUpload join = upload.completionFuture().join();
      log.debug("upload response :  " + join.toString());
    } else {
      log.debug("upload response :  Failed, key = " + key);
    }
  }

  public String getModifiedFileName(String originalName) {
    if (!originalName.contains("-")) {
      return originalName;
    }
    int startIndex = originalName.indexOf("/") + 1;
    String firstPart = originalName.substring(startIndex, originalName.indexOf("-"));
    if (originalName.indexOf(firstPart) != originalName.lastIndexOf(firstPart)) {
      return originalName.substring(originalName.lastIndexOf(firstPart));
    } else {
      return originalName.substring(startIndex);
    }
  }
}
