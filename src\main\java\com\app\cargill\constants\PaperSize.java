/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

public enum PaperSize {
  A4("210mm", "297mm", "10px", "10px", "10px", "10px");

  private String width;
  private String height;
  private String marginLeft;
  private String marginRight;
  private String marginTop;
  private String marginBottom;

  PaperSize(
      String width,
      String height,
      String marginLeft,
      String marginRight,
      String marginTop,
      String marginBottom) {
    this.width = width;
    this.height = height;
    this.marginLeft = marginLeft;
    this.marginRight = marginRight;
    this.marginTop = marginTop;
    this.marginBottom = marginBottom;
  }

  public String getWidth() {
    return width;
  }

  public String getHeight() {
    return height;
  }

  public String getMarginLeft() {
    return marginLeft;
  }

  public String getMarginRight() {
    return marginRight;
  }

  public String getMarginTop() {
    return marginTop;
  }

  public String getMarginBottom() {
    return marginBottom;
  }
}
