/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RoboticMilkEvaluationToolOutputToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("CowsperRobot")
  private Double cowsPerRobot;

  @JsonProperty("MilkingsPerRobot")
  private Double milkingsPerRobot;

  @JsonProperty("RobotFreeTime")
  private Double robotFreeTime;

  @JsonProperty("MilkPerRobot")
  private Double milkPerRobot;

  @JsonProperty("Milkings")
  private Double milkings;

  @JsonProperty("MilkingRefusals")
  private Double milkingRefusals;

  @JsonProperty("MilkingFailures")
  private Double milkingFailures;

  @JsonProperty("MilkingSpeed")
  private Double milkingSpeed;

  @JsonProperty("AverageBoxTime")
  private Double averageBoxTime;

  @JsonProperty("MaximumConcentrate")
  private Double maximumConcentrate;

  @JsonProperty("AverageConcentrate")
  private Double averageConcentrate;

  @JsonProperty("MinimumConcentrate")
  private Double minimumConcentrate;

  @JsonProperty("ConcentratePer100KGMilk")
  private Double concentratePer100KGMilk;

  @JsonProperty("RestFeed")
  private Double restFeed;
}
