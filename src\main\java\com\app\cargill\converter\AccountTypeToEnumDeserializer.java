/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.app.cargill.constants.AccountType;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings("java:S108") // Empty catch blocks intentional
public class AccountTypeToEnumDeserializer extends JsonDeserializer<AccountType> {

  @Override
  public AccountType deserialize(
      JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    try {
      return AccountType.valueOf(node.asText());
    } catch (IllegalArgumentException e) {
    }

    try {
      return AccountType.fromId(Integer.parseInt(node.asText()));
    } catch (IllegalArgumentException e) {
    }

    if ("Commercial Customer".equals(node.asText()) || "Commercial".equals(node.asText())) {
      return AccountType.Customer;
    } else {
      if (!"Standard".equals(node.asText())) {
        log.warn(
            "Unknown AccountType value: {}. Falling back to default: \"Prospect\"", node.asText());
      }
      return AccountType.Prospect;
    }
  }
}
