/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller.admin;

import com.app.cargill.dto.admin.User;
import com.app.cargill.service.admin.UserAdminService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/user")
@Tag(name = "User management controller")
@RequiredArgsConstructor
public class UserAdminController {

  private final UserAdminService userAdminService;

  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @PreAuthorize(("authentication.name == @securityConfiguration.getApiAdminId()"))
  public List<User> getAllUsers() {
    return userAdminService.getAllUsers();
  }

  @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @PreAuthorize(("authentication.name == @securityConfiguration.getApiAdminId()"))
  public User addUser(@RequestBody Map<String, String> userData) {
    return userAdminService.addUser(
        userData.get("email"), userData.get("country"), userData.get("fullName"));
  }

  @PutMapping(value = "/de/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
  @PreAuthorize(("authentication.name == @securityConfiguration.getApiAdminId()"))
  public User updateUser(@PathVariable UUID id, @RequestBody Map<String, String> userData) {
    return userAdminService.updateUser(id, !Boolean.parseBoolean(userData.get("active")));
  }
}
