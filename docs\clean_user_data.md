Find all accounts for a specific user
```sql
select id, account_document->>'AccountName', account_document->>'Users' from accounts where account_document->>'id' in (
    select account_id from (
        select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
    ) r where LOWER(r.user_name) = '<EMAIL>'
)
```

Find all sites for a specific user
```sql
select * from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
(select account_id from (
	select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
) r where LOWER(r.user_name) = '<EMAIL>'))
```

Find all user_preferences for a specific user
```sql
select * from user_preferences where LOWER(user_preference_document->>'UserId') = '<EMAIL>'
```

Milk Processors
```sql
select * from milk_processors where LOWER(milk_processors_document->>'UserId') = '<EMAIL>'
```

Diets
```sql
select * from diets where diet_document->>'SiteId' in (
	select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
	(select account_id from (
		select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
	) r where LOWER(r.user_name) = '<EMAIL>'))
)
```

Pens
```sql
select * from pens where pen_document->>'SiteId' in (
	select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
	(select account_id from (
		select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
	) r where LOWER(r.user_name) = '<EMAIL>'))
)
```

Site Mappings
```sql
select * from site_mappings where site_mapping_document->>'LabyrinthSiteId' in (
	select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
	(select account_id from (
		select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
	) r where LOWER(r.user_name) = '<EMAIL>'))
)
```

Visits
```sql
select * from visits where visit_document->>'SiteId' in (
	select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
	(select account_id from (
		select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
	) r where LOWER(r.user_name) = '<EMAIL>'))
)
```

Activities
```sql
select * from activities where activity_document->>'VisitId' in (
	select visit_document->>'id' from visits where visit_document->>'SiteId' in (
		select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
		(select account_id from (
			select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
		) r where LOWER(r.user_name) = '<EMAIL>'))
	)
) 
or
activity_document->>'AccountId' in (
	select account_id from (
			select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
		) r where LOWER(r.user_name) = '<EMAIL>')
```

Content Details
```sql
select * from content_details where content_details_document->>'LabyrinthVisitId' in (
    select visit_document->>'id' from visits where visit_document->>'SiteId' in (
        select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in
        (select account_id from (
            select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
        ) r where LOWER(r.user_name) = '<EMAIL>'))
    )
)
or
content_details_document->>'AccountId' in
(select account_id from (
    select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
) r where LOWER(r.user_name) = '<EMAIL>')
```

Notes
```sql
select * from notes where notes_document->>'VisitId' in (
select visit_document->>'id' from visits where visit_document->>'SiteId' in (
	select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
	(select account_id from (
		select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
	) r where LOWER(r.user_name) = '<EMAIL>'))
))
```

Notifications
```sql
select * from notifications where notification_document->>'SiteId' in (
select visit_document->>'id' from visits where visit_document->>'SiteId' in (
	select site_document->>'id' from sites where site_document->>'AccountId' in (select account_document->>'id' from accounts where account_document->>'id' in 
	(select account_id from (
		select account_document->>'id' as account_id, trim('"' from jsonb_array_elements((account_document->>'Users')::jsonb)::text) as user_name from accounts
	) r where LOWER(r.user_name) = '<EMAIL>'))
))
```
