/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.app.cargill.constants.Currencies;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;

public class CurrenciesStringToEnumDeserializer extends JsonDeserializer<Currencies> {

  @Override
  public Currencies deserialize(
      JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    try {
      return Currencies.fromId(Integer.parseInt(node.asText()));
    } catch (IllegalArgumentException e) {
      return Currencies.valueOf(node.asText());
    }
  }
}
