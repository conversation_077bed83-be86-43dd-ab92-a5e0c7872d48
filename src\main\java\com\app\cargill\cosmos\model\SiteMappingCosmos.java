/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Container(containerName = "SiteMappings")
@Getter
@Setter
@ToString
public class SiteMappingCosmos {

  @JsonProperty("id")
  @PartitionKey
  private String id;

  @JsonProperty("LabyrinthSiteId")
  private String labyrinthSiteId;

  @JsonProperty("DDWHerdId")
  private String ddwHerdId;

  @JsonProperty("MaxSiteId")
  private String maxSiteId;

  @JsonProperty("LabyrinthAccountId")
  private String labyrinthAccountId;

  @JsonProperty("MilkProcessorId")
  private String milkProcessorId;

  @JsonProperty("DCGOId")
  private String dcgoId;
}
