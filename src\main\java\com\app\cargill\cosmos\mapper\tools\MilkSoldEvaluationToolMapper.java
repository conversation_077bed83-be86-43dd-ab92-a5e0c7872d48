/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.MilkSoldEvaluationToolCosmos;
import com.app.cargill.cosmos.model.tools.MilkSoldEvaluationToolItemCosmos;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.MilkSoldEvaluationToolItem;
import java.util.UUID;

public class MilkSoldEvaluationToolMapper {

  private MilkSoldEvaluationToolMapper() {}

  public static MilkSoldEvaluationTool map(MilkSoldEvaluationToolCosmos input) {

    CosmosToModelMapper<MilkSoldEvaluationToolItemCosmos, MilkSoldEvaluationToolItem> itemMapper =
        source ->
            MilkSoldEvaluationToolItem.builder()
                .outputs(source.getOutputs())
                .pickups(source.getPickups())
                .selectedVisits(
                    source.getSelectedVisits() != null
                        ? source.getSelectedVisits().stream().map(UUID::fromString).toList()
                        : null)
                .lactatingAnimals(source.getLactatingAnimals())
                .animalsinTank(source.getAnimalsinTank())
                .animalsinTank(source.getAnimalsinTank())
                .milkPickup(source.getMilkPickup())
                .dryMatterIntake(source.getDryMatterIntake())
                .daysInMilk(source.getDaysInMilk())
                .milkUreaMeasure(source.getMilkUreaMeasure())
                .build();

    CosmosToModelMapper<MilkSoldEvaluationToolCosmos, MilkSoldEvaluationTool> mapper =
        source ->
            MilkSoldEvaluationTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .visitMilkEvaluationData(
                    source.getVisitMilkEvaluationData() != null
                        ? itemMapper.map(source.getVisitMilkEvaluationData())
                        : null)
                .build();

    return mapper.map(input);
  }
}
