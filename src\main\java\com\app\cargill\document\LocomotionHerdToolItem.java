/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocomotionHerdToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Categories")
  private List<LocomotionHerdToolItemCategoryItem> categories;

  @JsonProperty("PensForVisit")
  private List<LocomotionToolItem> pensForVisit;

  @JsonProperty("DaysInMilk")
  private Double daysInMilk;

  @JsonProperty("TotalAnimalsInHerd")
  private Integer totalAnimalsInHerd;

  @JsonProperty("MilkProductionInKg")
  private Double milkProductionInKg;

  @JsonProperty("MilkPriceAtSiteLevel")
  public Double milkPriceAtSiteLevel;

  @JsonProperty("AverageLocomotionScore")
  public Double averageLocomotionScore;

  @JsonProperty("ToolStatus")
  public ToolStatuses toolStatus;

  @JsonProperty("MilkLoss")
  public Double milkLoss;

  @JsonProperty("MilkLossInKgPerDay")
  public Double milkLossInKgPerDay;

  @JsonProperty("MilkLossInKgPerYear")
  public Double milkLossInKgPerYear;

  @JsonProperty("RevenueLossInDollarPerDay")
  public Double revenueLossInDollarPerDay;

  @JsonProperty("RevenueLossInDollarPerYear")
  public Double revenueLossInDollarPerYear;

  @JsonProperty("StandardDeviationScore")
  public Double standardDeviationScore;
}
