/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.config;

import com.app.cargill.sf.cc.config.SalesforceConfig;
import java.util.Base64;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "salesforce.crescendo")
@Getter
@Setter
@RequiredArgsConstructor
public class CrescendoConfig implements SalesforceConfig {

  @Override
  public String getTokenAuthHeader() {
    String clientIdAndSecret = String.format("%s:%s", getClientId(), getClientSecret());
    return String.format(
        "Basic %s", Base64.getEncoder().encodeToString(clientIdAndSecret.getBytes()));
  }

  private String clientSecret;
  private String grantType = "client_credentials";
  private String tokenHost;
  private String tokenPath;
  private String clientId;
  private String scheme = "https";
  private String port;
  private String defaultOwnerId;
}
