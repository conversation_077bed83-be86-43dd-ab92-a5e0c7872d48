/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class SalesForceUserDocument {

  private UUID id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("UserName")
  private String userName;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("lastName")
  private String lastName;

  @JsonProperty("LastLoginDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastLoginDate;

  public SalesForceUserDocument() {}

  /*public SalesForceUserDocument() {}*/
}
