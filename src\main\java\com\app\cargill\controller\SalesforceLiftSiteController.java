/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.model.Sites;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/salesforce/lift/site")
@Tag(
    name = "Salesforce Lift Account",
    description = "Controller related to actions over Account objects")
@RequiredArgsConstructor
@Slf4j
public class SalesforceLiftSiteController {

  private final SitesRepository sitesRepository;
  private final LiftSitesService liftSitesService;
  private final LiftSiteMappingsService liftSiteMappingsService;
  private final ResourceBundleMessageSource resourceBundleMessageSource;

  @PostMapping("/trigger/{siteId}")
  @Operation(summary = "Get a LIFT account", description = "Get a lift account by owner's email")
  public ResponseEntity<String> triggerCreate(@PathVariable UUID siteId) {
    Sites site = sitesRepository.findBySiteId(siteId.toString());
    if (site == null) {
      return ResponseEntity.badRequest().body(String.format("UUID does not exist: %s", siteId));
    }
    String result;
    try {
      result =
          liftSitesService.createSite(
              site.getSiteDocument(), Locale.ENGLISH, resourceBundleMessageSource);
      site.getSiteDocument().setExternalId(result);
      sitesRepository.save(site);
    } catch (Exception e) {
      log.error("TRIGGER_SITE_CREATION_FAILED", e);
      return ResponseEntity.internalServerError()
          .body(String.format("TRIGGER_SITE_CREATION_FAILED %s", siteId));
    }

    try {
      liftSiteMappingsService.createSiteMapping(
          site.getSiteDocument(), Locale.ENGLISH, resourceBundleMessageSource);
    } catch (Exception e) {
      log.error("TRIGGER_SITE_MAPPING_CREATION_FAILED", e);
      return ResponseEntity.internalServerError()
          .body(String.format("TRIGGER_SITE_MAPPING_CREATION_FAILED %s", siteId));
    }
    return ResponseEntity.accepted().body(result);
  }

  @GetMapping("/autoDeleteSites")
  @Operation(
      summary = "Get all the Lift Deleted Sites and update delete as true in DB",
      description = "Deleted Sites check service")
  public void autoDeleteLiftdeletedSites() throws Exception {
    liftSitesService.autoDeleteLiftdeletedSites();
  }
}
