/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.LabyrinthContentType;
import com.app.cargill.constants.MediaTypes;
import com.app.cargill.constants.VisitReportType;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = false)
public class NoteMediaItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("MediaId")
  private UUID mediaId;

  @JsonProperty("NoteId")
  private UUID noteId;

  @JsonProperty("MediaType")
  private MediaTypes mediaType;

  @JsonProperty("CreateUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant createUtc;

  @JsonProperty("CreateUserId")
  private String createUserId;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("Latitude")
  private Double latitude;

  @JsonProperty("Longitude")
  private Double longitude;

  @JsonProperty("AttachmentSequence")
  private Integer attachmentSequence;

  @JsonProperty("LabyrinthContentType")
  private LabyrinthContentType labyrinthContentType;

  @JsonProperty("MediaName")
  private String mediaName;

  @JsonProperty("ReportType")
  private VisitReportType reportType;

  @JsonProperty("IsNew")
  private Boolean isNew;
}
