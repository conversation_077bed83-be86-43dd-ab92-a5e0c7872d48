/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum UserSettingsBrands {
  None(0),
  // [Description("Cargill")]
  Cargill(1),
  // [Description("<PERSON><PERSON>na")]
  <PERSON><PERSON>na(8),
  // [Description("Provimi")]
  Provimi(3),
  // [Description("Provimi US")]
  ProvimiUS(10),
  RaggioDiSole(11),
  <PERSON>g<PERSON><PERSON>(12);
  ;

  private final Integer value;

  UserSettingsBrands(Integer value) {
    this.value = value;
  }

  public Integer getValue() {
    return value;
  }

  public static UserSettingsBrands fromId(int userSetting) {
    for (UserSettingsBrands item : values()) {
      if (item.getValue().intValue() == userSetting) {
        return item;
      }
    }
    return null;
  }
}
