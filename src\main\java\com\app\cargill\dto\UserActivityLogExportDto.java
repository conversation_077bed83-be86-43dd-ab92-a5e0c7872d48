/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserActivityLogExportDto {

    private List<UserActivityLogResponseDto> activityLogs;
    private String fileName;
    private Instant exportDate;
    private String exportedBy;
    private Long totalRecords;
    private Instant dateFrom;
    private Instant dateTo;
    private String filterCriteria;
}
