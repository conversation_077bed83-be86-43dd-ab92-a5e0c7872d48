/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.constants.DietSource;
import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.BarnCosmos;
import com.app.cargill.cosmos.model.SiteCosmos;
import com.app.cargill.cosmos.model.SiteVisitCosmos;
import com.app.cargill.document.Barn;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteVisit;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.IAnimalClassService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SitesMigration implements CosmosDataMigration {

  private final SitesRepository sitesRepository;
  private final PensRepository pensRepository;
  private final VisitsRepository visitsRepository;
  private final DietRepository dietRepository;
  private final IAnimalClassService animalClassService;

  private Integer failed;
  private Integer success;

  private final CosmosToModelMapper<List<BarnCosmos>, List<Barn>> barnMapper =
      list ->
          list.stream()
              .map(
                  source ->
                      Barn.builder()
                          .id(UUID.fromString(source.getId()))
                          .barnName(source.getBarnName())
                          .createUser(source.getCreateUser())
                          .build())
              .toList();

  private final CosmosToModelMapper<List<SiteVisitCosmos>, List<SiteVisit>> siteVisitMapper =
      list ->
          list.stream()
              .map(
                  source ->
                      SiteVisit.builder()
                          .labyrinthVisitId(
                              source.getLabyrinthVisitId() != null
                                  ? UUID.fromString(source.getLabyrinthVisitId())
                                  : null)
                          .visitDate(source.getVisitDate())
                          .status(source.getStatus())
                          .visitName(source.getVisitName())
                          .build())
              .toList();

  private final CosmosToModelMapper<SiteCosmos, SiteDocument> siteMapper =
      source ->
          SiteDocument.builder()
              .id(UUID.fromString(source.getId()))
              .accountId(
                  source.getAccountId() != null ? UUID.fromString(source.getAccountId()) : null)
              .externalAccountId(source.getExternalAccountId())
              .siteName(source.getSiteName())
              .currentMilkPrice(source.getCurrentMilkPrice())
              .daysInMilk(source.getDaysInMilk())
              .milkingSystemType(source.getMilkingSystemType())
              .dryMatterIntake(source.getDryMatterIntake())
              .lactatingAnimal(source.getLactatingAnimal())
              .milk(source.getMilk())
              .milkFatPercent(source.getMilkFatPercent())
              .milkProteinPercent(source.getMilkProteinPercent())
              .milkOtherSolidsPercent(source.getMilkOtherSolidsPercent())
              .milkSomaticCellCount(source.getMilkSomaticCellCount())
              .bacteriaCellCount(source.getBacteriaCellCount())
              .netEnergyOfLactationDairy(source.getNetEnergyOfLactationDairy())
              .rationCost(source.getRationCost())
              .needsSync(false)
              .barns(
                  source.getBarns() != null ? barnMapper.map(source.getBarns()) : new ArrayList<>())
              .visits(
                  source.getVisits() != null
                      ? siteVisitMapper.map(source.getVisits())
                      : new ArrayList<>())
              .asFedIntake(source.getAsFedIntake())
              .dataSourceMappings(
                  source.getDataSourceMappings() != null
                      ? source.getDataSourceMappings()
                      : new ArrayList<>())
              .origination(source.getOrigination())
              .dDWLastUpdatedDate(source.getDDWLastUpdatedDate())
              .numberOfParlorStalls(source.getNumberOfParlorStalls())
              .createUser(source.getCreateUser())
              .isDeleted(source.isDeleted())
              .lastModifyUser(source.getLastModifyUser())
              .createTimeUtc(source.getCreateTimeUtc())
              .lastModifiedTimeUtc(
                  Instant.ofEpochSecond(source.getLastModifiedTimeUtc().getEpoch()))
              .lastSyncTimeUtc(source.getLastSyncTimeUtc())
              .isNew(source.isNew())
              .build();

  @Async
  @Override
  public CompletableFuture<MigrationResult> postMigration(String migrationType) {
    MigrationResult migrationResult = new MigrationResult("Accounts Post Migration");
    if (migrationType != null
        && (migrationType.equalsIgnoreCase(MigrationType.SITES.name())
            || migrationType.equalsIgnoreCase(MigrationType.ALL.name()))) {
      log.info("STARTING POST MIGRATION TASKS FOR SITES : " + DateTime.now());
      try {
        updateLastVisitDateForSites();
      } catch (Exception e) {
        log.error("Could not perform tasks due to: " + e.getLocalizedMessage());
        return CompletableFuture.completedFuture(migrationResult);
      }
      migrationResult.setFailed(failed);
      migrationResult.setSucceeded(success);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  private void updateLastVisitDateForSites() {

    log.info("UPDATING LAST VISIT DATE IN SITES. STARTED AT: " + DateTime.now());
    success = 0;
    failed = 0;

    List<Sites> sites = sitesRepository.findAll();
    if (sites.isEmpty()) {
      throw new NotFoundDEException("No sites Found.");
    }
    List<Visits> visits = visitsRepository.findAll();

    if (visits.isEmpty()) {
      throw new NotFoundDEException("No visits Found");
    }

    sites.stream()
        .forEach(
            site -> {
              try {
                site.getSiteDocument().setDateOfLastVisit(getLatestVisitDate(visits, site));
                success++;
              } catch (Exception ex) {
                log.error(
                    "Error updating Site for ID: "
                        + site.getSiteDocument().getId()
                        + " ISSUE: "
                        + ex.getLocalizedMessage());
                failed++;
              }
            });
    sitesRepository.saveAll(sites);

    log.info("SUCCESSFULLY UPDATED SITES: " + success + "\n FAILED SITES: " + failed);

    log.info("UPDATING LAST VISIT DATE IN SITES. COMPLETED AT : " + DateTime.now());
  }

  private Instant getLatestVisitDate(List<Visits> visits, Sites site) {

    Optional<VisitDocument> latestDateVisit =
        visits.parallelStream()
            .filter(
                visit ->
                    visit.getVisitDocument().getSiteId().equals(site.getSiteDocument().getId()))
            .map(Visits::getVisitDocument)
            .max(Comparator.comparing(VisitDocument::getVisitDate));

    if (latestDateVisit.isEmpty()) {
      return null;
    }
    return latestDateVisit.get().getVisitDate();
  }

  @SuppressWarnings("java:S3776")
  private void setAnimalClassInPens() {
    List<Pens> pens = pensRepository.findAll();
    // List<Pens> pensToUpdate = new ArrayList<>();
    log.debug("Number of pens fetched: " + pens.size());
    try {
      for (Pens pen : pens) {
        log.debug("Processing pen record " + pen.getId());
        if (pen.getPenDocument() != null
            && pen.getPenDocument().getDietId() != null
            && (pen.getPenDocument().getAnimalClassId() == null
                || !pen.getPenDocument().getAnimalClassId().toString().startsWith("00000000"))) {

          log.debug("Processing pen id " + pen.getPenDocument().getId());

          boolean isPenUpdated = false;
          Diets diet = dietRepository.findById(pen.getPenDocument().getDietId().toString());

          if (diet != null && diet.getDietDocument().getAnimalType() != null) {
            if (diet.getDietDocument().getAnimalType().getId() != null
                && diet.getDietDocument()
                    .getAnimalType()
                    .getId()
                    .toString()
                    .startsWith("00000000")) {
              pen.getPenDocument().setAnimalClassId(diet.getDietDocument().getAnimalType().getId());
              isPenUpdated = true;
            } else {
              UUID animalClass =
                  animalClassService.getAnimalTypeId(
                      diet.getDietDocument().getAnimalType().getSubClass());
              if (!animalClass.toString().equals("00000000-0000-0000-0000-000000000000")) {
                pen.getPenDocument().setAnimalClassId(animalClass);
                isPenUpdated = true;
              }
            }
            log.debug("is pen " + pen.getPenDocument().getId() + "modified ? " + isPenUpdated);
            if (isPenUpdated
                && pen.getPenDocument().getDietSource() != null
                && pen.getPenDocument().getDietSource() != DietSource.MAX) {
              log.debug("Removing diet from pen " + pen.getPenDocument().getId());
              pen.getPenDocument().setDietId(null);
            }
            if (isPenUpdated) {
              pensRepository.save(pen);
              log.debug("Pen " + pen.getPenDocument().getId() + " updated");
            }
          }
        }
      }
    } catch (Exception ex) {
      log.error("Error while updating pen in migration fix " + ex.getMessage(), ex);
    }
  }

  @Override
  public CompletableFuture<MigrationResult> moveAll() {
    // TODO Auto-generated method stub
    return null;
  }
}
