/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.SegmentsDto;
import com.app.cargill.service.ISegmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/segment")
@Tag(name = "Segment Information Controller", description = "Segment Information Controller")
@RequiredArgsConstructor
public class SegmentController extends BaseController {

  private final ISegmentService segmentService;

  @GetMapping
  @Operation(summary = "Get all segments", description = "This method will return all segments")
  public ResponseEntity<ResponseEntityDto<List<SegmentsDto<Integer, String, Boolean>>>>
      getAllSegments() {
    return handleSuccessResponse(segmentService.getAllSegments());
  }
}
