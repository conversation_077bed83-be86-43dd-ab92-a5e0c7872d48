/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.converter.CurrenciesStringToEnumDeserializer;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.cosmos.model.tools.BodyConditionToolCosmos;
import com.app.cargill.cosmos.model.tools.CalfHeiferScorecardCosmos;
import com.app.cargill.cosmos.model.tools.CudChewingToolCosmos;
import com.app.cargill.cosmos.model.tools.HeatStressToolCosmos;
import com.app.cargill.cosmos.model.tools.LocomotionToolCosmos;
import com.app.cargill.cosmos.model.tools.ManureScreenerToolCosmos;
import com.app.cargill.cosmos.model.tools.MetabolicIncidenceToolCosmos;
import com.app.cargill.cosmos.model.tools.MilkSoldEvaluationToolCosmos;
import com.app.cargill.cosmos.model.tools.PenTimeBudgetToolCosmos;
import com.app.cargill.cosmos.model.tools.PileAndBunkerToolCosmos;
import com.app.cargill.cosmos.model.tools.ReadyToMilkToolCosmos;
import com.app.cargill.cosmos.model.tools.ReportTypeCosmos;
import com.app.cargill.cosmos.model.tools.RevenueInputsCosmos;
import com.app.cargill.cosmos.model.tools.RoboticMilkEvaluationToolCosmos;
import com.app.cargill.cosmos.model.tools.RumenFillToolCosmos;
import com.app.cargill.cosmos.model.tools.RumenHealthManureScoreToolCosmos;
import com.app.cargill.cosmos.model.tools.RumenHealthTMRParticleScoreToolCosmos;
import com.app.cargill.cosmos.model.tools.RumenHealthToolCosmos;
import com.app.cargill.cosmos.model.tools.ScorecardCosmos;
import com.app.cargill.cosmos.model.tools.UrinePHToolCosmos;
import com.app.cargill.cosmos.model.tools.VisitWalkThroughReportsCosmos;
import com.app.cargill.document.DateEpoch;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

@Container(containerName = "Visits")
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitCosmos {

  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  //  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private boolean isNew;

  @JsonProperty("CustomerId")
  private String customerId;

  @JsonProperty("SiteId")
  private String siteId;

  @JsonProperty("VisitDate")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant visitDate;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("Status")
  private VisitStatus status;

  @JsonProperty("Selected")
  private boolean selected;

  @JsonProperty("VisitName")
  private String visitName;

  @JsonProperty("FormattedCreationDate")
  private String formattedCreationDate;

  @JsonProperty("IsVisitAutoPublished")
  private boolean isVisitAutoPublished;

  @JsonProperty("NeedsSync")
  private boolean needsSync;

  @JsonProperty("GeneratedPDFReports")
  private Map<String, String> generatedPDFReports;

  @JsonProperty("CudChewing")
  private CudChewingToolCosmos cudChewing;

  @JsonProperty("HeatStress")
  private HeatStressToolCosmos heatStress;

  @JsonProperty("ForageAuditScorecard")
  private ScorecardCosmos forageAuditScorecard;

  @JsonProperty("PileAndBunker")
  private PileAndBunkerToolCosmos pileAndBunker;

  @JsonProperty("RumenHealth")
  private RumenHealthToolCosmos rumenHealth;

  @JsonProperty("TMRParticleScore")
  private RumenHealthTMRParticleScoreToolCosmos tmrParticleScore;

  @JsonProperty("RumenHealthManureScore")
  private RumenHealthManureScoreToolCosmos rumenHealthManureScore;

  @JsonProperty("LocomotionScore")
  private LocomotionToolCosmos locomotionScore;

  @JsonProperty("BodyCondition")
  private BodyConditionToolCosmos bodyCondition;

  @JsonProperty("Revenue")
  private RevenueInputsCosmos revenue;

  @JsonProperty("MetabolicIncidence")
  private MetabolicIncidenceToolCosmos metabolicIncidence;

  @JsonProperty("PenTimeBudgetTool")
  private PenTimeBudgetToolCosmos penTimeBudgetTool;

  @JsonProperty("SelectedCurrency")
  @JsonDeserialize(using = CurrenciesStringToEnumDeserializer.class)
  private Currencies selectedCurrency;

  @JsonProperty("WalkThroughReports")
  private VisitWalkThroughReportsCosmos walkThroughReports;

  @JsonProperty("ReadyToMilk")
  private ReadyToMilkToolCosmos readyToMilk;

  @JsonProperty("MilkSoldEvaluation")
  private MilkSoldEvaluationToolCosmos milkSoldEvaluation;

  @JsonProperty("UrinePHTool")
  private UrinePHToolCosmos urinePHTool;

  @JsonProperty("CalfHeiferScorecard")
  private CalfHeiferScorecardCosmos calfHeiferScorecard;

  @JsonProperty("RoboticMilkEvaluation")
  private RoboticMilkEvaluationToolCosmos roboticMilkEvaluation;

  @JsonProperty("ManureScreenerTool")
  private ManureScreenerToolCosmos manureScreenerTool;

  @JsonProperty("RumenFillManureScore")
  private RumenFillToolCosmos rumenFillManureScore;

  @JsonProperty("ReportType")
  private List<ReportTypeCosmos> reportType;

  @JsonProperty("VisitPublishedDateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant visitPublishedDateTimeUtc;
}
