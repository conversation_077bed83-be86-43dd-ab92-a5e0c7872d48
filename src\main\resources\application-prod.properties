app.debug.web-client=true

#aws properties
cloud.aws.region=${AWS_DEFAULT_REGION:us-east-1}
cloud.aws.s3.bucket-name=${BUCKET_DEDISCOVER_NAME:cargill-customer-portraits}
cloud.aws.s3.access-key=${AWS_ACCESS_KEY_ID:}
cloud.aws.s3.secret-key=${AWS_SECRET_ACCESS_KEY:}

#cosmos properties
cosmos.uri=https://mobilefirstprd.documents.azure.com:443/
cosmos.key=${COSMOS_KEY}
cosmos.queryMetricsEnabled=true
cosmos.database=MobileFirstRepository

#azure_okta properties
logging.config=classpath:logback-colorless.xml
login.okta.oauth2.issuer=https://cargillcustomer.okta-emea.com/oauth2/aus27mqdnpScrChMJ0i7
login.okta.oauth2.client-id=${LOGIN_OKTA_CLIENT_ID}
login.azure.oauth2.tenant-id=${LOGIN_OKTA_TENANT_ID}
login.azure.oauth2.issuer=https://sts.windows.net/${login.azure.oauth2.tenant-id}/
login.azure.oauth2.client-id=${LOGIN_AZURE_CLIENT_ID}

#salesforce Lift properties
salesforce.lift.token-host=cargillanh.my.salesforce.com
salesforce.lift.token-path=/services/oauth2/token
salesforce.lift.client-secret=${SALESFORCE_LIFT_CLIENT_SECRET}
salesforce.lift.client-id=${SALESFORCE_LIFT_CLIENT_ID}
salesforce.lift.default-owner-id=<EMAIL>

#salesforce Crescendo properties
salesforce.crescendo.token-host=cargill18--preprod.sandbox.my.salesforce.com
salesforce.crescendo.token-path=/services/oauth2/token
salesforce.crescendo.client-secret=${SALESFORCE_CRESCENDO_SECRET}
salesforce.crescendo.client-id=${SALESFORCE_CRESCENDO_CLIENT_ID}
salesforce.crescendo.default-owner-id=Crescendo_DE_Integration


#spring Properties
spring.datasource.url=${DB_CONNECTION:*******************************************}
spring.datasource.username=${DB_USERNAME:admin}
spring.datasource.password=${DB_PASSWORD:Passw0rd}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.show_sql=false
spring.sql.init.mode=never
spring.data.rest.default-media-type=application/json
springdoc.swagger-ui.base-url-private-postfix=${SWAGGER_BASE_URL_POSTFIX:/api/dairyenteligen/dediscover/v1/}
springdoc.swagger-ui.base-url-public-postfix=${SWAGGER_BASE_URL_POSTFIX:/api/dairyenteligen/dediscover/public/}
springdoc.swagger-ui.oauth.client-id=clientId
springdoc.swagger-ui.oauth.client-secret=secret
springdoc.swagger-ui.oauth.use-basic-authentication-with-access-code-grant=true
springdoc.swagger-ui.oauth.scope-separator= 
springdoc.swagger-ui.oauth.access-token-url=${SWAGGER_ACCESS_TOKEN_URL:https://api.cglcloud.com/api/dairyenteligen/dediscover/public/auth/okta-token}

#sharepoint properties
sharepoint.post-file-url=/sites/dairyenteligen/_api/web/GetFolderByServerRelativeUrl({0}/sites/dairyenteligen/Shared%20Documents/{1}{2})/Files/add(url={3}{4}{5},overwrite=true)
sharepoint.create-folder-url=/sites/dairyenteligen/_api/Web/Folders/add({0}/sites/dairyenteligen/Shared%20Documents/{1}{2})
sharepoint.check-folder-exists-url=/sites/dairyenteligen/_api/web/GetFolderByServerRelativeUrl({0}/sites/dairyenteligen/Shared%20Documents/{1}{2})/Exists
sharepoint.visit-report-file-url=https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%20Documents/{0}/{1}

#azure blob
azure.blob.storage.connection=${AZURE_BLOB_STORAGE_CRED}

#DairyForecast Connection
spring.security.oauth2.client.registration.dairyforecast.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.dairyforecast.client-id=${DF_CLIENT_ID}
spring.security.oauth2.client.registration.dairyforecast.client-secret=${DF_CLIENT_SECRET}
spring.security.oauth2.client.registration.dairyforecast.scope=d76e7baf-4f0e-41c1-a2ed-09450a2825cd/.default
spring.security.oauth2.client.provider.dairyforecast.token-uri=https://login.microsoftonline.com/57368c21-b8cf-42cf-bd0b-43ecd4bc62ae/oauth2/v2.0/token
app.dairyforecast.url=https://dairyforecastapisync.cglcloud.com/api/DairyForecast

api.admin.id=a7d051d7-5577-4985-87d3-e86fd5cb58de

#sharepoint urls
sharepoint.grant_type=client_credentials
sharepoint.client_id=${CAN_SHAREPOINT_CLIENT_ID}
sharepoint.client_secret=${CAN_SHAREPOINT_CLIENT_SECRET}
sharepoint.resource=${CAN_SHAREPOINT_RESOURCE}
sharepoint.tenant_id=57368c21-b8cf-42cf-bd0b-43ecd4bc62ae
sharepoint.authority=https://accounts.accesscontrol.windows.net/${sharepoint.tenant_id}/tokens/OAuth/2
sharepoint.web_url=https://cargillonline.sharepoint.com/sites/dairyenteligen
sharepoint.instance-url=https://cargillonline.sharepoint.com
sharepoint.file_url=/sites/dairyenteligen/Shared Documents/DE_Reports
sharepoint.summary_file_name_with_suffix=true
sharepoint.detailed_file_name_with_suffix=true
