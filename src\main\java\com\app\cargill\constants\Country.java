/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum Country {
  UNDEFINED("UNDEFINED"),
  BRAZIL("Brazil"),
  CANADA("Canada"),
  FRANCE("France"),
  NETHERLANDS("Netherlands"),
  PHILIPPINES("Philippines"),
  POLAND("Poland"),
  UNITED_STATES("United States"),
  VIETNAM("Vietnam"),
  SPAIN("Spain"),
  ITALY("Italy"),
  KOREA("Korea"),
  INDIA("India"),
  MEXICO("Mexico"),
  RUSSIA("Russia"),
  SOUTH_AFRICA("South Africa"),
  CHINA("China"),
  PORTUGAL("Portugal"),
  UKRAINE("Ukraine"),
  HUNGARY("Hungary"),
  UNITED_KINGDOM("United Kingdom"),
  PAKISTAN("Pakistan"),
  ARGENTINA("Argentina");

  private final String value;

  Country(String countryName) {
    this.value = countryName;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
