app.configurations.default-page-size=20
app.configurations.default-utc-timestamp=1970-01-01T00:00:00Z
app.configurations.auto-publish-visit-days=5
app.configurations.default-page-sorting=asc
logging.file.name=logback.xml
logging.level.reactor.netty.http.client=error
logging.level.root=warn
logging.level.com.amazonaws.util.EC2MetadataUtils=error
logging.level.org.hibernate=error
logging.level.org.springframework.transaction=warn
logging.level.org.springframework.web=warn
logging.level.org.springframework.orm.jpa=warn
logging.level.org.springframework.orm=WARN
logging.level.org.springframework.security=warn
logging.level.org.springframework.cloud.aws=error
logging.level.org.apache.http=warn
server.port=${SERVER_PORT:8080}
server.connection-timeout=120000
server.forward-headers-strategy=framework
server.tomcat.max-http-form-post-size=25MB
server.tomcat.max-swallow-size=25MB
server.error.include-message=always
server.error.include-binding-errors=always
spring.application.name=cargill-application
spring.datasource.url=${DB_CONNECTION}:**************************************
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:postgres}
spring.profiles.active=${SPRING_PROFILES_ACTIVE:local}
spring.thymeleaf.cache=false
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.codec.max-in-memory-size=60MB
spring.jpa.show-sql=false
spring.jpa.hibernate.use-new-id-generator-mappings=false
spring.jpa.hibernate.ddl-auto=update
spring.jpa.hibernate.order_insert=true
spring.jpa.hibernate.jdbc.batch_size=500
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.jdbc.batch_size=500
spring.jpa.properties.hibernate.jdbc.order_inserts=true
spring.jpa.properties.hibernate.jdbc.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.open-in-view=false
spring.liquibase.change-log=classpath:db/liquibase-changelog.xml
spring.liquibase.enabled=false
spring.batch.job.enabled=true
spring.sql.init.platform=postgresql
spring.servlet.encoding.charset=UTF-8
spring.servlet.encoding.force=true
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=${server.tomcat.max-http-form-post-size}
spring.servlet.multipart.max-request-size=${server.tomcat.max-http-form-post-size}
spring.http.multipart.max-file-size=${server.tomcat.max-http-form-post-size}
spring.http.multipart.max-request-size=${server.tomcat.max-http-form-post-size}


i18n.directory=src/main/resources/internationalization
