/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RevenueInputToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("CurrentSCCInCellsPerMilliliterScenarioOne")
  private Double currentSCCInCellsPerMilliliterScenarioOne;

  @JsonProperty("PotentialSCCInCellsPerMilliliterScenarioOne")
  private Double potentialSCCInCellsPerMilliliterScenarioOne;

  @JsonProperty("CurrentMastitisPercentScenarioOne")
  private Double currentMastitisPercentScenarioOne;

  @JsonProperty("PotentialMastitisPercentScenarioOne")
  private Double potentialMastitisPercentScenarioOne;

  @JsonProperty("CurrentSCCDeductionScenarioOne")
  private Double currentSCCDeductionScenarioOne;

  @JsonProperty("PotentialSCCDeductionScenarioOne")
  private Double potentialSCCDeductionScenarioOne;

  @JsonProperty("MilkPriceInDollarPerKgScenarioOne")
  private Double milkPriceInDollarPerKgScenarioOne;

  @JsonProperty("NumberOfCowsScenarioOne")
  private Double numberOfCowsScenarioOne;

  @JsonProperty("MilkProductionInKgScenarioOne")
  private Double milkProductionInKgScenarioOne;

  @JsonProperty("LetDownResponseInKgPerCowPerDayScenarioOne")
  private Double letDownResponseInKgPerCowPerDayScenarioOne;

  @JsonProperty("CurrentSCCInCellsPerMilliliterScenarioTwo")
  private Double currentSCCInCellsPerMilliliterScenarioTwo;

  @JsonProperty("PotentialSCCInCellsPerMilliliterScenarioTwo")
  private Double potentialSCCInCellsPerMilliliterScenarioTwo;

  @JsonProperty("CurrentMastitisPercentScenarioTwo")
  private Double currentMastitisPercentScenarioTwo;

  @JsonProperty("PotentialMastitisPercentScenarioTwo")
  private Double potentialMastitisPercentScenarioTwo;

  @JsonProperty("CurrentSCCDeductionScenarioTwo")
  private Double currentSCCDeductionScenarioTwo;

  @JsonProperty("PotentialSCCDeductionScenarioTwo")
  private Double potentialSCCDeductionScenarioTwo;

  @JsonProperty("MilkPriceInDollarPerKgScenarioTwo")
  private Double milkPriceInDollarPerKgScenarioTwo;

  @JsonProperty("NumberOfCowsScenarioTwo")
  private Double numberOfCowsScenarioTwo;

  @JsonProperty("MilkProductionInKgScenarioTwo")
  private Double milkProductionInKgScenarioTwo;

  @JsonProperty("LetDownResponseInKgPerCowPerDayScenarioTwo")
  private Double letDownResponseInKgPerCowPerDayScenarioTwo;

  @JsonProperty("CurrencySymbol")
  private String currencySymbol;

  @JsonProperty("CurrentLetDownResponseInKgPerCowPerDayScenarioOne")
  private Double currentLetDownResponseInKgPerCowPerDayScenarioOne;

  @JsonProperty("PotentialLetDownResponseInKgPerCowPerDayScenarioOne")
  private Double potentialLetDownResponseInKgPerCowPerDayScenarioOne;

  @JsonProperty("CurrentLetDownResponseInKgPerCowPerDayScenarioTwo")
  private Double currentLetDownResponseInKgPerCowPerDayScenarioTwo;

  @JsonProperty("PotentialLetDownResponseInKgPerCowPerDayScenarioTwo")
  private Double potentialLetDownResponseInKgPerCowPerDayScenarioTwo;
}
