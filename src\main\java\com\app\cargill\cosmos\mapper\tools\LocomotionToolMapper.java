/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.LocomotionHerdToolItemCosmos;
import com.app.cargill.cosmos.model.tools.LocomotionToolCosmos;
import com.app.cargill.cosmos.model.tools.LocomotionToolItemCosmos;
import com.app.cargill.document.LocomotionHerdToolItem;
import com.app.cargill.document.LocomotionTool;
import com.app.cargill.document.LocomotionToolItem;
import java.util.UUID;

public class LocomotionToolMapper {

  private LocomotionToolMapper() {}

  public static LocomotionTool map(LocomotionToolCosmos input) {

    CosmosToModelMapper<LocomotionToolItemCosmos, LocomotionToolItem> itemMapper =
        source ->
            LocomotionToolItem.builder()
                .visitsSelected(
                    source.getVisitsSelected() != null
                        ? source.getVisitsSelected().stream().map(UUID::fromString).toList()
                        : null)
                .milkScoreThree(source.getMilkScoreThree())
                .milkScoreFour(source.getMilkScoreFour())
                .milkScoreFive(source.getMilkScoreFive())
                .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
                .penName(source.getPenName())
                .totalAnimalsInPen(source.getTotalAnimalsInPen())
                .daysInMilk(source.getDaysInMilk())
                .milkProductionInKg(source.getMilkProductionInKg())
                .categories(source.getCategories())
                .isToolItemNew(source.getIsToolItemNew())
                .build();

    CosmosToModelMapper<LocomotionHerdToolItemCosmos, LocomotionHerdToolItem> herdMapper =
        source ->
            LocomotionHerdToolItem.builder()
                .categories(source.getCategories())
                .pensForVisit(
                    source.getPensForVisit() != null
                        ? source.getPensForVisit().stream().map(itemMapper::map).toList()
                        : null)
                .daysInMilk(source.getDaysInMilk())
                .totalAnimalsInHerd(source.getTotalAnimalsInHerd())
                .milkProductionInKg(source.getMilkProductionInKg())
                .milkPriceAtSiteLevel(source.getMilkPriceAtSiteLevel())
                .build();

    CosmosToModelMapper<LocomotionToolCosmos, LocomotionTool> mapper =
        source ->
            LocomotionTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .herd(source.getHerd() != null ? herdMapper.map(source.getHerd()) : null)
                .build();

    return mapper.map(input);
  }
}
