/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.CowUrinePHItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class UrinePHToolItemCosmos extends EditableToolPenEntityBaseCosmos {
  @JsonProperty("DietDCAD")
  private Double dietDCAD;

  @JsonProperty("CowUrinePH")
  private List<CowUrinePHItem> cowUrinePH;

  @JsonProperty("AverageUrinePHVisitsSelected")
  private List<String> averageUrinePHVisitsSelected;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("UrinePHGoal")
  private UrinePHGoalCosmos urinePHGoal;
}
