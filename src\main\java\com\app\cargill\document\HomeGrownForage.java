/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.HomeGrownForageTypes;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomeGrownForage implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("HomeGrownForageType")
  private HomeGrownForageTypes homeGrownForageType;

  @JsonProperty("ForageName")
  private String forageName;

  @JsonProperty("TotalHerdPerDay")
  private Double totalHerdPerDay;

  @JsonProperty("DryMatter")
  private Double dryMatter;

  @JsonProperty("TotalDryMatter")
  private Double totalDryMatter;

  @JsonProperty("PricePerTon")
  private Double pricePerTon; // From API call
}
