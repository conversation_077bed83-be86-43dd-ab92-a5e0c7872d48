/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.EarTagDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.IEarTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/earTags")
@Tag(name = "Ear Tags Information Controller", description = "Ear Tags Information Controller")
@RequiredArgsConstructor
public class EarTagController extends BaseController {

  private final IEarTagService earTagServiceImpl;

  @GetMapping("/paginated")
  @Operation(
      summary = "Get all Ear Tags based on current logged in User",
      description = "This method will return all Ear Tags based on current Logged in user")
  public ResponseEntity<ResponseEntityDto<Page<EarTagDto>>> getAllEarTagsPaginated(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", name = "sortBy") String sortBy,
      @RequestParam(
              name = "lastSyncTime",
              defaultValue = "${app.configurations.default-utc-timestamp}")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        earTagServiceImpl.getAllEarTagsPaginated(page, size, sortBy, sorting, lastSyncTime));
  }

  @PostMapping
  @Operation(summary = "Save EarTags", description = "This api will save eartags WRT to SiteId")
  public ResponseEntity<ResponseEntityDto<List<EarTagDto>>> save(@RequestBody EarTagDto earTagDto) {
    return handleSuccessResponse(earTagServiceImpl.save(earTagDto));
  }

  @PutMapping
  @Operation(
      summary = "Update EarTags",
      description = "This api will Update eartags WRT to EarTag ID")
  public ResponseEntity<ResponseEntityDto<List<EarTagDto>>> update(
      @RequestBody EarTagDto earTagDto) {
    return handleSuccessResponse(earTagServiceImpl.update(earTagDto));
  }
}
