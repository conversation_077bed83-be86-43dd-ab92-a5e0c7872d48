/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.VisitStatus;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SiteVisit {
  @JsonProperty("LabyrinthVisitId")
  public UUID labyrinthVisitId;

  @JsonProperty("VisitDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant visitDate;

  @JsonProperty("Status")
  public VisitStatus status;

  @JsonProperty("VisitName")
  public String visitName;
}
