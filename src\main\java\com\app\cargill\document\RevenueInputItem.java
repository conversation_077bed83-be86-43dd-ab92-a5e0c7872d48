/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RevenueInputItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("ScenarioTitle")
  private Integer scenarioTitle;

  @JsonProperty("ScenarioTitleText")
  private String scenarioTitleText;

  @JsonProperty("ScenarioOneValue")
  private Double scenarioOneValue;

  @JsonProperty("ScenarioTwoValue")
  private Double scenarioTwoValue;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;
}
