/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.NoteCategoryType;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class NotesDocument extends EditableDocumentBase implements Serializable {
  @JsonProperty("Title")
  private String title;

  @JsonProperty("Note")
  private String note;

  @JsonProperty("AccountId")
  private UUID accountId;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("SiteId")
  private UUID siteId;

  /* mapping to string from NoteVisitSection enum to make it dynamic, it contains unique name of
  each screen*/
  @JsonProperty("Section")
  private String section;
  // it contains UUID of selected section entity
  @JsonProperty("SectionId")
  private UUID sectionId;

  @JsonProperty("SectionTitle")
  private String sectionTitle;

  @JsonProperty("MediaItems")
  private List<NoteMediaItem> mediaItems;

  @JsonProperty("Category")
  private NoteCategoryType category;

  @JsonProperty("ActionNotificationDateTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant actionNotificationDateTimeUtc;

  @JsonProperty("IsComment")
  private Boolean isComment;
}
