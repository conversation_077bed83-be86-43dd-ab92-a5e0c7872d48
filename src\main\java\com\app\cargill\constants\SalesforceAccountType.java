/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

public enum SalesforceAccountType {

  // [EnumMember(Value = "012j00000010CWeAAM")]
  PROSPECT("012j00000010CWeAAM"),
  // [EnumMember(Value = "012j00000010CWbAAM")]
  CUSTOMER("012j00000010CWbAAM"),
  // [EnumMember(Value = "012j00000010CWdAAM")]
  THIRD_PARTY("012j00000010CWdAAM"),
  // [EnumMember(Value = "012f10000000KKaAAM")]
  CONSUMER("012f10000000KKaAAM"),
  // [EnumMember(Value = "012j00000010CWcAAM")]
  COMPETITOR("012j00000010CWcAAM");

  private final String value;

  SalesforceAccountType(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }
}
