/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkProduction implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("AverageMilkProductionKg")
  private Double averageMilkProductionKg;

  @JsonProperty("MilkProductionKg")
  private Double milkProductionKg;

  @JsonProperty("KgOfQuotaPerDay")
  private Double kgOfQuotaPerDay;

  @JsonProperty("IncentiveDaysKgPerDay")
  private Double incentiveDaysKgPerDay;

  @JsonProperty("TotalQuotaKgPerDay")
  private Double totalQuotaKgPerDay;

  @JsonProperty("CurrentQuotaUtilizationKgPerDay")
  private Double currentQuotaUtilizationKgPerDay;

  @JsonProperty("Butterfat")
  private MilkComponent butterfat;

  @JsonProperty("Protein")
  private MilkComponent protein;

  @JsonProperty("LactoseAndOtherSolids")
  private MilkComponent lactoseAndOtherSolids;

  @JsonProperty("Class2Protein")
  private SimpleComponent class2Protein;

  @JsonProperty("Class2LactoseAndOtherSolids")
  private SimpleComponent class2LactoseAndOtherSolids;

  @JsonProperty("Deductions")
  private SimpleComponent deductions;
}
