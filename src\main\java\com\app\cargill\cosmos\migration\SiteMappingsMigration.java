/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.mapper.SiteMappingsMapper;
import com.app.cargill.cosmos.model.SiteMappingCosmos;
import com.app.cargill.cosmos.repo.SiteMappingsCosmosRepository;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.repository.SiteMappingsRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class SiteMappingsMigration implements CosmosDataMigration {
  private final SiteMappingsCosmosRepository cosmosRepository;
  private final SiteMappingsRepository siteMappingsRepository;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("SiteMappings");
    try {
      log.info("SiteMappings migration started ...");
      List<SiteMappings> smList = fetchAll(migrationResult);
      smList =
          smList.stream()
              .filter(sm -> sm.getSiteMappingDocument().getLabyrinthSiteId() != null)
              .filter(
                  sm ->
                      siteMappingsRepository.findBySiteIdWithDeleted(
                              sm.getSiteMappingDocument().getLabyrinthSiteId().toString())
                          == null)
              .toList();
      siteMappingsRepository.saveAll(smList);
      log.info("SiteMappings migration completed. {}", migrationResult);
      log.info("{} SiteMappings added.", smList.size());
    } catch (Exception e) {
      log.error("Error occurred during SiteMappings migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  public Flux<SiteMappings> moveRecords(String siteId) {
    Flux<SiteMappings> cosmosRecords =
        processCosmosFlux(cosmosRepository.findAllBySiteId(siteId), new AtomicInteger(0));
    return processRecords(cosmosRecords);
  }

  private List<SiteMappings> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    AtomicInteger failures = new AtomicInteger(0);
    List<SiteMappings> smList =
        processCosmosFlux(cosmosRepository.findAll(), failures).collectList().block();

    assert smList != null;
    log.info("{} SiteMappings fetched from CosmosDB", smList.size());
    if (failures.get() > 0) {
      log.warn("{} SiteMappings failed to map during the fetching process", failures.get());
    }
    migrationResult.setSucceeded(smList.size());
    migrationResult.setFailed(failures.get());
    return smList;
  }

  private Flux<SiteMappings> processCosmosFlux(
      Flux<SiteMappingCosmos> records, AtomicInteger failedItems) {
    return records
        .map(
            siteMappingCosmos -> {
              SiteMappings siteMapping =
                  new SiteMappings(SiteMappingsMapper.map(siteMappingCosmos));
              siteMapping.setLocalId(siteMapping.getSiteMappingDocument().getId().toString());
              return siteMapping;
            })
        .onErrorContinue(
            (throwable, object) -> {
              failedItems.incrementAndGet();
              log.error("Error during SiteMappings migration: {}", object, throwable);
            });
  }

  private Flux<SiteMappings> processRecords(Flux<SiteMappings> records) {
    return records
        .filter(sm -> sm.getSiteMappingDocument().getLabyrinthSiteId() != null)
        .flatMap(this::processSingleRecord)
        .onErrorContinue((t, o) -> log.error("Error during SiteMappings save in DB {}", o, t));
  }

  private Mono<SiteMappings> processSingleRecord(SiteMappings sm) {
    return Mono.fromCallable(
            () -> {
              try {
                SiteMappings existingRecord =
                    siteMappingsRepository.findBySiteIdWithDeleted(
                        sm.getSiteMappingDocument().getLabyrinthSiteId().toString());
                return Objects.requireNonNullElseGet(
                    existingRecord, () -> siteMappingsRepository.save(sm));
              } catch (Exception e) {
                throw new MigrationException("Error during SiteMappings process", e);
              }
            })
        .subscribeOn(Schedulers.boundedElastic());
  }

  @Override
  @Async
  public CompletableFuture<MigrationResult> migrationFix(String fixesToRun) {

    var lambdaContext =
        new Object() {
          Integer count = 0;
        };
    if (fixesToRun != null
        && (fixesToRun.equals(MigrationFix.SITE_MAPPINGS.toString())
            || fixesToRun.equals(MigrationFix.ALL.toString()))) {
      List<SiteMappingCosmos> cosmosMappings =
          cosmosRepository.findAllSiteMappings().collectList().block();
      assert cosmosMappings != null;
      log.info("Mappings Fetched from cosmos where fields are missing: " + cosmosMappings.size());
      List<SiteMappings> postgresMappings = siteMappingsRepository.findAll();
      log.info("Mappings Fetched from Postgres :" + postgresMappings.size());
      List<SiteMappings> mappingsToUpdate = new ArrayList<>();
      cosmosMappings.stream()
          .forEach(
              csm ->
                  postgresMappings.parallelStream()
                      .forEach(
                          psm -> {
                            if (psm.getSiteMappingDocument()
                                .getId()
                                .toString()
                                .equals(csm.getId())) {
                              psm.getSiteMappingDocument()
                                  .setMilkProcessorId(csm.getMilkProcessorId());
                              psm.getSiteMappingDocument().setDcgoId(csm.getDcgoId());
                              mappingsToUpdate.add(psm);
                              lambdaContext.count++;
                            }
                          }));
      siteMappingsRepository.saveAllAndFlush(mappingsToUpdate);
      log.info("RECORDS UPDATED FOR SITE MAPPINGS MISSING FIELDS : " + lambdaContext.count);
      log.info("MIGRATION FIX COMPLETED");
      return CompletableFuture.completedFuture(
          new MigrationResult("Data Fixed", lambdaContext.count, 0));
    }
    return CompletableFuture.completedFuture(
        new MigrationResult("Data Fixed", lambdaContext.count, 0));
  }
}
