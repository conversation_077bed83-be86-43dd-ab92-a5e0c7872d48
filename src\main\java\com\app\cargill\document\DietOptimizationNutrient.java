/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DietOptimizationNutrient implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("NutrientId")
  public String nutrientSpeciesId;

  @JsonProperty("Result")
  public Double result;
  // Max
  @JsonProperty("Name")
  public String name;
}
