/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.service.ICdpService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cdp")
@Tag(name = "CDP Data Transfer Controller", description = "CDP Data Transfer Controller")
@Slf4j
@RequiredArgsConstructor
public class CDPController extends BaseController {

  private final ICdpService cdpService;

  @GetMapping(path = "/getAllAccountsByFromAndToDate")
  public ResponseEntity<ResponseEntityDto<List<AccountDocumentDTO>>> getAllAccountsByFromAndToDate(
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateFrom")
          Instant dateFrom,
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateTo")
          Instant dateTo,
      @ParameterObject @PageableDefault(size = 10000, sort = "id", direction = Sort.Direction.ASC)
          Pageable pageable) {

    log.info("getAllAccountsByFromAndToDate invoked");
    Page<AccountDocumentDTO> page =
        cdpService.getAllAccountsByFromAndToDate(dateFrom, dateTo, pageable);
    return handleSuccessResponse(page.getContent());
  }

  @GetMapping(path = "/getAllSitesByFromAndToDate")
  public ResponseEntity<ResponseEntityDto<List<SiteDocumentDTO>>> getAllSitesByFromAndToDate(
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateFrom")
          Instant dateFrom,
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateTo")
          Instant dateTo,
      @ParameterObject @PageableDefault(size = 10000, sort = "id", direction = Sort.Direction.ASC)
          Pageable pageable) {

    log.info("getAllSitesByFromAndToDate invoked");
    Page<SiteDocumentDTO> page = cdpService.getAllSitesByFromAndToDate(dateFrom, dateTo, pageable);
    log.info("Page size: {}", page.getSize());
    return handleSuccessResponse(page.getContent());
  }

  @GetMapping(path = "/getAllVisitsByFromAndToDate")
  public ResponseEntity<ResponseEntityDto<List<VisitDocumentDTO>>> getAllVisitsByFromAndToDate(
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateFrom")
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          Instant dateFrom,
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateTo")
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          Instant dateTo,
      @ParameterObject @PageableDefault(size = 10000, sort = "id", direction = Sort.Direction.ASC)
          Pageable pageable) {

    log.info("getAllVisitsByFromAndToDate invoked");
    Page<VisitDocumentDTO> page =
        cdpService.getAllVisitsByFromAndToDate(dateFrom, dateTo, pageable);
    log.info(
        "getAllVisitsByFromAndToDate {} {} {}",
        page.getContent().size(),
        StringEscapeUtils.escapeJava(dateFrom.toString()),
        StringEscapeUtils.escapeJava(dateTo.toString()));
    return handleSuccessResponse(page.getContent());
  }
}
