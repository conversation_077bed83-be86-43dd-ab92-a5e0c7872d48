/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.UserPreferenceDto;
import com.app.cargill.service.IUserPreferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/userPreferences")
@Tag(
    name = "User Preference Information Controller",
    description = "User Preference Information Controller")
@RequiredArgsConstructor
public class UserPreferenceController extends BaseController {

  private final IUserPreferenceService userPreferenceServiceImpl;

  @PostMapping
  @Operation(
      summary = "Saves UserPreferences",
      description = "This method will Save User Preferences")
  public ResponseEntity<ResponseEntityDto<UserPreferenceDto>> save(
      @RequestBody UserPreferenceDto userPreferenceDto) {

    return handleSuccessResponse(userPreferenceServiceImpl.save(userPreferenceDto));
  }

  @PutMapping
  @Operation(
      summary = "updates UserPreferences",
      description = "This method will update User Preferences")
  public ResponseEntity<ResponseEntityDto<UserPreferenceDto>> update(
      @RequestBody UserPreferenceDto userPreferenceDto) {

    return handleSuccessResponse(userPreferenceServiceImpl.update(userPreferenceDto));
  }
}
