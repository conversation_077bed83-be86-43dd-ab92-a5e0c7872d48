/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PricingMatrixValuesCosmos implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("id")
  private String id;

  @JsonProperty("Amount")
  private Double amount;

  @JsonProperty("Weight")
  private Double weight;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("CreateUser")
  private String createUser;
}
