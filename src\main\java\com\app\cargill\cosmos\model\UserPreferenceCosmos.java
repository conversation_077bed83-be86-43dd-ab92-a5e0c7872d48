/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.Currencies;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.document.UserFavourites;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Instant;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Container(containerName = "UserPreferences")
@Getter
@Setter
public class UserPreferenceCosmos {

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("UserId")
  private String userId;

  @JsonProperty("UnitOfMeasure")
  private Integer unitOfMeasure;

  @JsonProperty("LastSyncTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("LastSyncOperationDateTime")
  private Instant lastSyncOperationDateTime;

  @JsonProperty("LastEulaVersionAccepted")
  private Instant lastEulaVersionAccepted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("BrandList")
  private List<Integer> brandList;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("CreateTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant createTimeUtc;

  @JsonProperty("EulaContent")
  private String eulaContent;

  @JsonProperty("id")
  private String id;

  @JsonProperty("SelectedCurrency")
  private Currencies selectedCurrency;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("LastPrivacyVersionAccepted")
  private Instant lastPrivacyVersionAccepted;

  @JsonProperty("Favourites")
  private List<UserFavourites> favourites;

  @JsonProperty("IsNew")
  private boolean isNew;
}
