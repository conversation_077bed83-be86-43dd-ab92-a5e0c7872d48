/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.ReadyToMilkToolCosmos;
import com.app.cargill.document.ReadyToMilkTool;
import java.util.UUID;

public class ReadyToMilkToolMapper {

  private ReadyToMilkToolMapper() {}

  public static ReadyToMilkTool map(ReadyToMilkToolCosmos input) {

    CosmosToModelMapper<ReadyToMilkToolCosmos, ReadyToMilkTool> mapper =
        source ->
            ReadyToMilkTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .visitReadyToMilkData(source.getVisitReadyToMilkData())
                .build();

    return mapper.map(input);
  }
}
