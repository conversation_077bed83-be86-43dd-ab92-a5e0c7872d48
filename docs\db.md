#### Insert integration users for local/dev/stage

```sql
INSERT INTO users (created_date, deleted, updated_date, account_type, authentication_platform, email, full_name, mobile_number, password, principal_name, local_id, user_document) VALUES ('2023-04-19 15:05:19.000000', false, '2023-04-19 15:05:24.000000', 'INTEGRATION', 'AZURE', 'ccf6baea-ce19-4bc8-b8cd-8c990ea358d5@integration', '', null, null, null, 'ccf6baea-ce19-4bc8-b8cd-8c990ea358d5', '{"id": "ccf6baea-ce19-4bc8-b8cd-8c990ea358d5", "UserName": "ccf6baea-ce19-4bc8-b8cd-8c990ea358d5@integration", "CountryId": "Global", "SalesforceCountryId": 0}');
```
##### Check all sites without external id
```sql
select site_document->>'SiteName', COUNT(site_document->>'SiteName') from sites where NOT (site_document ? 'ExternalId') OR site_document->>'ExternalId' is null GROUP BY site_document->>'SiteName' ORDER BY COUNT(site_document->>'SiteName') desc
```

##### Get all Accounts with same name
```sql
SELECT *  FROM accounts where account_document->>'AccountName' IN (select account_document->>'AccountName' from accounts group by account_document->>'AccountName' HAVING COUNT(*) > 1)
```

### Add user for migration
```sql
insert into user_migration (email, status, local_id, created_date, updated_date) 
values ('<EMAIL>', 'SCHEDULED', '49469f47-edfb-44f6-9ebb-e0ee0d4f37e3', current_timestamp, current_timestamp)
```

### Get user accounts
```sql
select * from 
(select account_document->>'id' as account_id, account_document->>'GoldenRecordId', account_document->>'AccountName', jsonb_array_elements((account_document->>'UserRoles')::jsonb)::jsonb as user_roles from accounts) ur
where user_roles->>'UserName' like '<EMAIL>'
```

### Get user accounts by role
```sql
select * from (select * from 
(select account_document->>'id' as account_id, account_document->>'GoldenRecordId', account_document->>'AccountName', jsonb_array_elements((account_document->>'UserRoles')::jsonb)::jsonb as user_roles from accounts) ur
where user_roles->>'UserName' like '<EMAIL>') user_r
where user_roles->>'UserRole' not like 'Account Representative'
```