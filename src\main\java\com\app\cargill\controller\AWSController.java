/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.S3PresignedUrlsDto;
import com.app.cargill.service.IS3Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/aws")
@Tag(name = "AWS", description = "AWS Services Information Controller")
@RequiredArgsConstructor
public class AWSController extends BaseController {

  private final IS3Service s3ServiceImpl;

  @PostMapping(value = "/s3/generatePresignedUrls", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Generate put/get urls for s3 bucket",
      description = "This api will generate put/get urls for s3 bucket")
  public ResponseEntity<ResponseEntityDto<List<S3PresignedUrlsDto>>> generatePresignedUrls(
      @RequestBody List<S3PresignedUrlsDto> dto) {
    return handleSuccessResponse(s3ServiceImpl.generatePresignedUrls(dto));
  }

  @GetMapping(value = "/s3/search", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Search s3 objects by key names",
      description =
          "This api will return the list of s3 objects along with their presigned URLs "
              + " that starts with the provided key name.")
  public ResponseEntity<ResponseEntityDto<List<S3PresignedUrlsDto>>> searchFiles(
      @RequestParam(name = "key") String key) {
    if (key == null || key.trim().isEmpty()) {
      return handleResponse(
          "Search key missing", null, ResponseStatus.FAILED, HttpStatus.BAD_REQUEST);
    }
    return handleSuccessResponse(s3ServiceImpl.findObjects(key));
  }
}
