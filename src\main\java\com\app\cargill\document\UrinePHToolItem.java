/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class UrinePHToolItem extends EditableToolPenEntityBase {
  @JsonProperty("DietDCAD")
  public Double dietDCAD;

  @JsonProperty("CowUrinePH")
  public List<CowUrinePHItem> cowUrinePH;

  @JsonProperty("AverageUrinePHVisitsSelected")
  public List<UUID> averageUrinePHVisitsSelected;

  @JsonProperty("IsToolItemNew")
  public Boolean isToolItemNew;

  @JsonProperty("UrinePHGoal")
  public UrinePHGoal urinePHGoal;
}
