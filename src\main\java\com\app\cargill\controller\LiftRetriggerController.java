/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.ILiftRetriggerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/lift/retrigger")
@Tag(
    name = "Lift Retrigger Information Controller",
    description = "Lift Retrigger Information Controller")
@RequiredArgsConstructor
public class LiftRetriggerController extends BaseController {

  private final ILiftRetriggerService liftRetriggerService;
  private final ResourceBundleMessageSource resourceBundleMessageSource;

  @PostMapping("/accounts")
  @Operation(
      summary = "Retrigger Accounts to LIFT",
      description = "This api will retrigger accounts to LIFT")
  public ResponseEntity<ResponseEntityDto<List<String>>> retriggerAccounts(
      @RequestBody List<String> accountIds,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws Exception {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    List<String> result =
        liftRetriggerService.retriggerAccounts(accountIds, locale, resourceBundleMessageSource);

    return handleSuccessResponse(result);
  }

  @PostMapping("/sites")
  @Operation(
      summary = "Retrigger sites + siteMappings to LIFT",
      description = "This api will retrigger site + siteMappings to LIFT")
  public ResponseEntity<ResponseEntityDto<List<String>>> retriggerSites(
      @RequestBody List<String> siteIds,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString) {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    List<String> result =
        liftRetriggerService.retriggerSites(siteIds, locale, resourceBundleMessageSource);

    return handleSuccessResponse(result);
  }

  @PostMapping("/events")
  @Operation(
      summary = "Retrigger sites + siteMappings to LIFT",
      description = "This api will retrigger site + siteMappings to LIFT")
  public ResponseEntity<ResponseEntityDto<List<String>>> retriggerEvents(
      @RequestBody List<String> visitIds,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString) {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    List<String> result =
        liftRetriggerService.retriggerEvents(visitIds, locale, resourceBundleMessageSource);

    return handleSuccessResponse(result);
  }
}
