/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.mapper.userprererences.UserSettingsBrandListMapper;
import com.app.cargill.cosmos.model.UserPreferenceCosmos;
import com.app.cargill.cosmos.repo.UserPreferencesCosmosRepository;
import com.app.cargill.document.UserFavourites;
import com.app.cargill.document.UserPreferenceDocument;
import com.app.cargill.model.UserPreferences;
import com.app.cargill.repository.UserPreferencesRepository;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserPreferencesMigration implements CosmosDataMigration {

  private final UserPreferencesCosmosRepository cosmosRepository;
  private final UserPreferencesRepository userPreferencesRepository;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("UserPreferences");
    try {
      List<UserPreferences> usersPreferencesList = fetchAll(migrationResult);
      userPreferencesRepository.saveAll(usersPreferencesList);
      log.info(
          "UserPreference migration completed. {} records copied to PostgresSQL",
          usersPreferencesList.size());
    } catch (Exception e) {
      log.error("Error occurred during UserPreferences migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  public Mono<UserPreferences> moveRecord(String userName) {
    return Mono.fromCallable(() -> cosmosRepository.getUserPreferenceCosmosByUserEmail(userName))
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(
            upList -> {
              if (!upList.isEmpty()) {
                if (upList.size() > 1) {
                  log.warn(
                      "Multiple user preferences records found for {}. Returning the first record.",
                      StringEscapeUtils.escapeJava(userName));
                }
                return Mono.just(upList.get(0));
              } else {
                log.info(
                    "User preferences for {} not found", StringEscapeUtils.escapeJava(userName));
                return Mono.empty();
              }
            })
        .map(userPreferencesMapper::map)
        .flatMap(this::processUserPreferences);
  }

  private Mono<UserPreferences> processUserPreferences(UserPreferenceDocument input) {
    return Mono.fromCallable(
            () -> {
              UserPreferences userPreferences = new UserPreferences(input);
              userPreferences.setLocalId(
                  userPreferences.getUserPreferenceDocument().getId().toString());
              try {
                UserPreferences existingRecord =
                    userPreferencesRepository.findByUserId(
                        userPreferences.getUserPreferenceDocument().getUserId());
                return Objects.requireNonNullElseGet(
                    existingRecord, () -> userPreferencesRepository.save(userPreferences));
              } catch (Exception e) {
                throw new MigrationException("Error during user preferences process", e);
              }
            })
        .subscribeOn(Schedulers.boundedElastic())
        .onErrorResume(
            t -> {
              log.error("Error during account process", t);
              return Mono.error(t);
            });
  }

  private List<UserPreferences> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    Iterator<UserPreferenceCosmos> cosmosIterator = cosmosRepository.findAll().iterator();
    List<UserPreferences> userPreferencesList = new ArrayList<>();
    int failures = 0;
    int recordsFetched = 0;
    while (cosmosIterator.hasNext()) {
      try {
        recordsFetched++;
        UserPreferences userPreferences =
            new UserPreferences(userPreferencesMapper.map(cosmosIterator.next()));
        userPreferences.setLocalId(userPreferences.getUserPreferenceDocument().getId().toString());
        userPreferencesList.add(userPreferences);
      } catch (Exception e) {
        log.error("There was an error fetching a User Preference from CosmosDB", e);
        failures++;
      }
    }
    log.info("{} UserPreferences fetched from CosmosDB", recordsFetched);
    if (failures > 0) {
      log.warn("{} userPreferences failed to map during the fetching process", failures);
    }
    migrationResult.setSucceeded(userPreferencesList.size());
    migrationResult.setFailed(failures);
    return userPreferencesList;
  }

  private final CosmosToModelMapper<UserPreferenceCosmos, UserPreferenceDocument>
      userPreferencesMapper =
          source ->
              UserPreferenceDocument.builder()
                  .brandList(UserSettingsBrandListMapper.map(source.getBrandList()))
                  .createTimeUtc(source.getCreateTimeUtc())
                  .createUser(source.getCreateUser())
                  .eulaContent(source.getEulaContent())
                  .favourites(
                      UserFavourites.builder()
                          .accounts(new ArrayList<>())
                          .tools(new ArrayList<>())
                          .notes(new ArrayList<>())
                          .build())
                  .id(UUID.fromString(source.getId()))
                  .isDeleted(source.isDeleted())
                  .isNew(source.isNew())
                  .lastEulaVersionAccepted(source.getLastEulaVersionAccepted())
                  .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                  .lastModifyUser(source.getLastModifyUser())
                  .lastPrivacyVersionAccepted(source.getLastPrivacyVersionAccepted())
                  .lastSyncOperationDateTime(source.getLastSyncOperationDateTime())
                  .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                  .selectedCurrency(source.getSelectedCurrency())
                  .unitOfMeasure(UnitOfMeasureKeys.fromId(source.getUnitOfMeasure()))
                  .userId(source.getUserId())
                  .build();
}
