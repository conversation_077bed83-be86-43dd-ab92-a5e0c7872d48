/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.CityDto;
import com.app.cargill.dto.CountryDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.StateDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IGeoLocationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/geoLocation")
@Tag(
    name = "Geo Location Information Controller",
    description = "Geo Location Information Controller")
@RequiredArgsConstructor
public class GeoLocationController extends BaseController {

  private final IGeoLocationService geoLocationService;
  private final ResourceBundleMessageSource resourceBundleMessageSource;

  @GetMapping("/countries")
  @Operation(summary = "Get all countries", description = "This api will return the countries list")
  public ResponseEntity<ResponseEntityDto<List<CountryDto>>> fetchAllCountries(
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString) {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);

    return handleSuccessResponse(
        geoLocationService.fetchAllCountries(locale, resourceBundleMessageSource));
  }

  @GetMapping("/countries/paginated")
  @Operation(
      summary = "Get all countries paginated",
      description = "This api will return the countries list paginated")
  public ResponseEntity<ResponseEntityDto<Page<CountryDto>>> fetchAllCountriesPaginated(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);

    return handleSuccessResponse(
        geoLocationService.fetchAllCountriesPaginated(
            page, size, sortBy, lastSyncTime, sorting, locale, resourceBundleMessageSource));
  }

  @GetMapping("/states")
  @Operation(
      summary = "Get states by country Id or get all states",
      description = "This api will return the states list")
  public ResponseEntity<ResponseEntityDto<List<StateDto>>> fetchStates(
      @RequestParam(required = false) String countryCode,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    return handleSuccessResponse(
        geoLocationService.fetchStateByCountryCode(
            countryCode, locale, resourceBundleMessageSource));
  }

  @GetMapping("/states/paginated")
  @Operation(
      summary = "Get states by country Id or get all states paginated",
      description = "This api will return the states list paginated")
  public ResponseEntity<ResponseEntityDto<Page<StateDto>>> fetchStatesPaginated(
      @RequestParam(required = false) String countryCode,
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);

    return handleSuccessResponse(
        geoLocationService.fetchStateByCountryCodePaginated(
            countryCode,
            page,
            size,
            sortBy,
            lastSyncTime,
            sorting,
            locale,
            resourceBundleMessageSource));
  }

  @GetMapping("/statesByCountryCodes/paginated")
  @Operation(
      summary = "Get states by country code or get all states paginated",
      description = "This api will return the states list using list of country codes paginated")
  public ResponseEntity<ResponseEntityDto<Page<StateDto>>> fetchStatesByCountryCodesPaginated(
      @RequestParam(required = false) List<String> countryCodes,
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);

    return handleSuccessResponse(
        geoLocationService.fetchStatesByCountryCodesPaginated(
            countryCodes,
            page,
            size,
            sortBy,
            lastSyncTime,
            sorting,
            locale,
            resourceBundleMessageSource));
  }

  @GetMapping("/cities")
  @Operation(
      summary = "Get cities by state Id or get all cities",
      description = "This api will return the cities list")
  public ResponseEntity<ResponseEntityDto<List<CityDto>>> fetchCities(
      @RequestParam(required = false) String stateCode,
      @RequestParam(required = false) String countryCode) {

    return handleSuccessResponse(
        geoLocationService.fetchCitiesByStateCodeAndCountryCode(stateCode, countryCode));
  }

  @GetMapping("/cities/paginated")
  @Operation(
      summary = "Get cities by state and country id or get all cities paginated",
      description = "This method will return the cities list")
  public ResponseEntity<ResponseEntityDto<Page<CityDto>>> fetchCitiesPaginated(
      @RequestParam(required = false) String stateCode,
      @RequestParam(required = false) String countryCode,
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        geoLocationService.fetchCitiesByStateCodeAndCountryCodePaginated(
            stateCode, countryCode, page, size, sortBy, lastSyncTime, sorting));
  }
}
