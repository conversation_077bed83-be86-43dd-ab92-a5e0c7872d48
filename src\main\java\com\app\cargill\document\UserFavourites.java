/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.Tool;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFavourites implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private List<Tool> tools;
  private List<String> accounts;
  private List<String> notes;
}
