/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.service.I18nUpdateService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/i18n")
@RequiredArgsConstructor
public class I18nUpdateController {

  private final I18nUpdateService i18nUpdateService;

  @PostMapping("/update")
  public ResponseEntity<String> updateI18n(@RequestParam("file") MultipartFile file) {
    String response =
        i18nUpdateService.updateI18nFile(file, "French CA", "messages_frca.properties");
    return ResponseEntity.ok(response);
  }

  @PostMapping("/missingKeys")
  public ResponseEntity<List<String>> getMissingKeys(@RequestParam("file") MultipartFile file) {
    List<String> missingKeys = i18nUpdateService.findMissingKeys(file, "messages_pt.properties");
    return ResponseEntity.ok(missingKeys);
  }
}
