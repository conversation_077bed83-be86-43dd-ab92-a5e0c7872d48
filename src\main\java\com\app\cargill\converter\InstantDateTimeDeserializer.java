/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class InstantDateTimeDeserializer extends JsonDeserializer<Instant> {

  @Override
  public Instant deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    String nodeText = node.asText();
    try {
      return Instant.parse(nodeText);
    } catch (Exception e) {
      if (!"0001-01-01T00:00:00".equals(nodeText)) { // prevent log bloat, this value is known issue
        log.warn("Error deserializing Instant: {} Falling back to beginning of time", nodeText);
      }
      return Instant.MIN;
    }
  }
}
