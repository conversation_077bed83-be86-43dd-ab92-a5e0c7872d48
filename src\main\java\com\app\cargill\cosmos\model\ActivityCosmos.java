/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.document.DateEpoch;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

@Container(containerName = "Activities")
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
public class ActivityCosmos {

  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private Boolean isDeleted = false;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("ActivityDateTime")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant activityDateTime;

  @JsonProperty("ActivityType")
  private Integer activityType;

  @JsonProperty("Subject")
  private String subject;

  @JsonProperty("IsPrivate")
  private Boolean isPrivate;

  @JsonProperty("IsGroupEvent")
  private Boolean isGroupEvent;

  @JsonProperty("EventLocation")
  private String eventLocation;

  @JsonProperty("EventStartDate")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant eventStartDate;

  @JsonProperty("AllDayEvent")
  private Boolean allDayEvent;

  @JsonProperty("SFDCAccountId")
  private String sfdcAccountId;

  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("NeedsSync")
  private Boolean needsSync;

  @JsonProperty("SFDCId")
  private String sfdcId;

  @JsonProperty("SFDCVisitId")
  private String sfdcVisitId;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("AssignedToID")
  private String assignedToID;

  @JsonProperty("EndDateTime")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant endDateTime;

  @JsonProperty("OwnerId")
  private String ownerId;
}
