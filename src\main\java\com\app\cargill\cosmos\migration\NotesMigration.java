/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.constants.LabyrinthContentType;
import com.app.cargill.constants.MediaTypes;
import com.app.cargill.constants.NoteCategoryType;
import com.app.cargill.constants.NoteVisitSection;
import com.app.cargill.constants.VisitReportType;
import com.app.cargill.cosmos.model.NotesCosmos;
import com.app.cargill.cosmos.model.NotesCosmos.NotesCosmosMediaItem;
import com.app.cargill.cosmos.repo.ContentDetailsCosmosRepository;
import com.app.cargill.cosmos.repo.NotesCosmosRepository;
import com.app.cargill.cosmos.repo.VisitsCosmosRepository;
import com.app.cargill.document.NoteMediaItem;
import com.app.cargill.document.NotesDocument;
import com.app.cargill.model.Notes;
import com.app.cargill.repository.NotesRepository;
import com.azure.cosmos.implementation.Strings;
import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.PeriodType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class NotesMigration implements CosmosDataMigration {

  private final NotesCosmosRepository notesCosmosRepository;
  private final VisitsCosmosRepository visitsCosmosRepository;
  private final NotesRepository dbRepository;
  private final ContentDetailsCosmosRepository contentDetailsCosmosRepository;

  @Override
  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult(MigrationType.NOTES.name());

    DateTime start = DateTime.now();
    AtomicInteger failedNotes = new AtomicInteger(0);
    Flux<Notes> notesFlux = fetchAll(failedNotes);
    return processNotes(notesFlux, failedNotes)
        .reduce(0, (accumulator, a) -> accumulator + 1)
        .map(
            v -> {
              DateTime end = DateTime.now();
              Duration duration = new Duration(start, end);
              migrationResult.setFailed(failedNotes.get());
              migrationResult.setSucceeded(v);
              log.info(
                  "Time taken for Notes migration: {} start: {} end: {}",
                  duration.toPeriod().normalizedStandard(PeriodType.standard()),
                  start,
                  end);

              log.info("Notes migration completed. {}", migrationResult);
              return migrationResult;
            })
        .toFuture();
  }

  @Override
  public MigrationType migrationType() {
    return MigrationType.NOTES;
  }

  public Flux<Notes> moveRecords(String visitId) {
    AtomicInteger failedRecords = new AtomicInteger(0);
    Flux<Notes> notesFlux =
        processCosmosNotesFlux(notesCosmosRepository.findAllByVisitId(visitId), failedRecords);
    return processNotes(notesFlux, failedRecords);
  }

  private Flux<Notes> processNotes(Flux<Notes> notesFlux, AtomicInteger failedRecords) {
    return notesFlux
        .filterWhen(
            note ->
                Mono.fromCallable(() -> checkIfExists(failedRecords, note))
                    .subscribeOn(Schedulers.boundedElastic()))
        .flatMap(
            note ->
                Mono.fromCallable(() -> dbRepository.save(note))
                    .subscribeOn(Schedulers.boundedElastic()))
        .onErrorContinue(
            (t, o) -> {
              failedRecords.incrementAndGet();
              log.error("Error with object: {}", o);
              log.error("Error during Notes save in DB", t);
            });
  }

  private Flux<Notes> processCosmosNotesFlux(
      Flux<NotesCosmos> cosmosNotesFlux, AtomicInteger failedRecords) {
    return cosmosNotesFlux
        .filter(this::filterCosmosData)
        .map(this::cosmosToDocumentMapper)
        .delayElements(java.time.Duration.ofMillis(100))
        .publishOn(Schedulers.boundedElastic())
        .map(
            notesDocument -> {
              visitsCosmosRepository
                  .findById(
                      notesDocument.getVisitId() != null
                          ? notesDocument.getVisitId().toString()
                          : "")
                  .doOnNext(
                      visitCosmos ->
                          notesDocument.setSiteId(
                              visitCosmos.getSiteId() != null
                                  ? UUID.fromString(visitCosmos.getSiteId())
                                  : null))
                  .block();
              notesDocument
                  .getMediaItems()
                  .forEach(
                      m ->
                          contentDetailsCosmosRepository
                              .findById(m.getMediaId() != null ? m.getMediaId().toString() : "")
                              .doOnNext(
                                  cdc -> {
                                    m.setLabyrinthContentType(
                                        LabyrinthContentType.fromId(cdc.getLabyrinthContentType()));
                                    m.setMediaId(
                                        !Strings.isNullOrEmpty(cdc.getId())
                                            ? UUID.fromString(cdc.getId())
                                            : null);
                                    m.setMediaName(cdc.getName());
                                    m.setReportType(VisitReportType.fromId(cdc.getReportType()));
                                    m.setIsNew(cdc.isNew());
                                  })
                              .block());
              Notes note = new Notes(notesDocument);
              note.setLocalId(notesDocument.getId().toString());
              return note;
            })
        .onErrorContinue(
            (throwable, object) -> {
              failedRecords.incrementAndGet();
              log.error("Error during Notes migration: {}", object);
              log.error("Error", throwable);
            });
  }

  private Flux<Notes> fetchAll(AtomicInteger failedRecords) {
    return processCosmosNotesFlux(notesCosmosRepository.findAll(), failedRecords);
  }

  private boolean filterCosmosData(NotesCosmos noteCosmos) {
    boolean result = true;

    if (noteCosmos.getAccountId() == null) {
      log.warn("Missing accountId for NotesCosmos");
      log.warn("{}", noteCosmos);
      return false;
    }

    try {
      UUID.fromString(noteCosmos.getAccountId());
    } catch (IllegalArgumentException exception) {
      log.warn("Invalid accountId for NotesCosmos");
      log.warn("{}", noteCosmos);
      return false;
    }

    return result;
  }

  private NotesDocument cosmosToDocumentMapper(NotesCosmos noteCosmos) {
    NotesDocument notesDocument = new NotesDocument();
    notesDocument.setId(UUID.fromString(noteCosmos.getId()));
    notesDocument.setAccountId(UUID.fromString(noteCosmos.getAccountId()));
    notesDocument.setNote(noteCosmos.getNote());
    notesDocument.setTitle(noteCosmos.getTitle());
    notesDocument.setVisitId(
        noteCosmos.getVisitId() != null ? UUID.fromString(noteCosmos.getVisitId()) : null);
    notesDocument.setSection(NoteVisitSection.findNameByIndex(noteCosmos.getSection()));
    notesDocument.setMediaItems(
        noteCosmos.getMediaItems().stream().map(this::cosmosToDocumentMediaMapper).toList());
    NoteCategoryType noteCategoryType = NoteCategoryType.findByIndex(noteCosmos.getCategory());
    if (noteCategoryType != NoteCategoryType.Action) {
      noteCategoryType = NoteCategoryType.General;
    }
    notesDocument.setCategory(noteCategoryType);
    notesDocument.setCreateUser(noteCosmos.getCreateUser());
    notesDocument.setDeleted(noteCosmos.isDeleted());
    notesDocument.setLastModifyUser(noteCosmos.getLastModifyUser());
    notesDocument.setCreateTimeUtc(noteCosmos.getCreateTimeUtc());
    if (noteCosmos.getLastModifiedTimeUtc() != null) {
      notesDocument.setLastModifiedTimeUtc(
          Instant.ofEpochSecond(noteCosmos.getLastModifiedTimeUtc().getEpoch()));
    }
    notesDocument.setLastSyncTimeUtc(noteCosmos.getLastSyncTimeUtc());
    notesDocument.setNew(noteCosmos.isNew());
    return notesDocument;
  }

  private NoteMediaItem cosmosToDocumentMediaMapper(NotesCosmosMediaItem cosmosMediaItem) {
    NoteMediaItem mediaItem = new NoteMediaItem();
    if (cosmosMediaItem.getMediaId() != null) {
      mediaItem.setMediaId(UUID.fromString(cosmosMediaItem.getMediaId()));
    }
    if (cosmosMediaItem.getNoteId() != null) {
      mediaItem.setNoteId(UUID.fromString(cosmosMediaItem.getNoteId()));
    }
    mediaItem.setMediaType(MediaTypes.fromId(cosmosMediaItem.getMediaType()));
    mediaItem.setCreateUtc(cosmosMediaItem.getCreateUtc());
    mediaItem.setLatitude(cosmosMediaItem.getLatitude());
    mediaItem.setLongitude(cosmosMediaItem.getLongitude());
    mediaItem.setCreateUserId(cosmosMediaItem.getCreateUserId());

    return mediaItem;
  }

  private Boolean checkIfExists(AtomicInteger failedAccounts, Notes note) {
    try {
      return dbRepository.findByDocumentId(note.getNotesDocument().getId().toString()) == null;
    } catch (Exception e) {
      failedAccounts.incrementAndGet();
      log.error("Error with object: {}", note);
      log.error("Error during Notes find in DB", e);
      return false;
    }
  }
}
