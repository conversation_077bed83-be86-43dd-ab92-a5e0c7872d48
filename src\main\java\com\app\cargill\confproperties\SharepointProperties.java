/* Cargill Inc.(C) 2022 */
package com.app.cargill.confproperties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("sharepoint")
@Data
public class SharepointProperties {
  private String grantType;
  private String clientId;
  private String clientSecret;
  private String resource;
  private String tenantId;
  private String authority;
  private String webUrl;
  private String fileUrl;
  private boolean summaryFileNameWithSuffix;
  private boolean detailedFileNameWithSuffix;
  private String postFileUrl;
  private String createFolderUrl;
  private String checkFolderExistsUrl;
  private String visitReportFileUrl;
  private String instanceUrl;
}
