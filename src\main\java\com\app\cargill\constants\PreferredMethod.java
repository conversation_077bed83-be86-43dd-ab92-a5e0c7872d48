/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Enum names intentional
public enum PreferredMethod {
  // This 0 index is needed because Cosmos Library deserialization converts through indices
  Unused(0),
  Email(1),
  Phone(2),
  Mobile(3),
  Text(4),
  Call(5);
  private final Integer value;

  PreferredMethod(Integer code) {
    this.value = code;
  }

  public Integer getValue() {
    return value;
  }
}
