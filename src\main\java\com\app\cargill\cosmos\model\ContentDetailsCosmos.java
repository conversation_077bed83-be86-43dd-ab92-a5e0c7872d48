/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

@Container(containerName = "ContentDetails")
@Getter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
public class ContentDetailsCosmos {

  @Id
  @JsonProperty("id")
  @PartitionKey
  private String id;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("LabyrinthVisitId")
  private String labyrinthVisitId;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("isDeleted")
  private boolean isDeleted = false;

  @JsonProperty("LabyrinthContentType")
  private Integer labyrinthContentType;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("ReportType")
  private Integer reportType;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("IsNew")
  private boolean isNew;
}
