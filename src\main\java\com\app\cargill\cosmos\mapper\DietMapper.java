/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper;

import com.app.cargill.cosmos.model.AnimalCosmos;
import com.app.cargill.cosmos.model.DietCosmos;
import com.app.cargill.document.AnimalClass;
import com.app.cargill.document.DietDocument;
import java.util.UUID;

public class DietMapper {
  private DietMapper() {}

  public static DietDocument map(DietCosmos input) {

    CosmosToModelMapper<DietCosmos, DietDocument> penMapper =
        source ->
            DietDocument.builder()
                .id(UUID.fromString(source.getId()))
                .labyrinthAccountId(UuidMapper.getNullableUuid(source.getLabyrinthAccountId()))
                .name(source.getName())
                .breedId(UuidMapper.getNullableUuid(source.getBreedId()))
                .breedName(source.getBreedName())
                .startDate(source.getStartDate())
                .endDate(source.getEndDate())
                .animalType(
                    source.getAnimalType() != null ? mapAnimal(source.getAnimalType()) : null)
                .siteId(UuidMapper.getNullableUuid(source.getSiteId()))
                .barnId(UuidMapper.getNullableUuid(source.getBarnId()))
                .environmentName(source.getEnvironmentName())
                .reportMilkWeight(source.getReportMilkWeight())
                .numberOfAnimals(source.getNumberOfAnimals())
                .analyzeOptimization(source.getAnalyzeOptimization())
                .formulateOptimization(source.getFormulateOptimization())
                .source(source.getSource())
                .selected(source.getSelected())
                .selectedPenGuids(
                    source.getSelectedPenGuids() != null
                        ? source.getSelectedPenGuids().stream().map(UUID::fromString).toList()
                        : null)
                .isSystemGenerated(source.getIsSystemGenerated())
                .isDeleted(source.getIsDeleted())
                .isActive(source.getIsActive())
                .optimizationId(source.getOptimizationId())
                .optimizationType(source.getOptimizationType())
                .optimizationStatus(source.getOptimizationStatus())
                .createUser(source.getCreateUser())
                .build();

    return penMapper.map(input);
  }

  private static AnimalClass mapAnimal(AnimalCosmos input) {
    CosmosToModelMapper<AnimalCosmos, AnimalClass> penMapper =
        source ->
            AnimalClass.builder()
                .id(UuidMapper.getNullableUuid(source.getId()))
                .classs(source.getClas())
                .subClass(source.getSubClass())
                .selected(source.getSelected())
                .build();

    return penMapper.map(input);
  }
}
