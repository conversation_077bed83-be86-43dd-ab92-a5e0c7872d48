/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.CudChewingByPenCosmos;
import com.app.cargill.cosmos.model.tools.CudChewingToolCosmos;
import com.app.cargill.document.CudChewingByPen;
import com.app.cargill.document.CudChewingTool;
import java.util.UUID;

public class CudChewingToolMapper {

  private CudChewingToolMapper() {}

  public static CudChewingTool map(CudChewingToolCosmos input) {
    CosmosToModelMapper<CudChewingByPenCosmos, CudChewingByPen> penMapper =
        source ->
            CudChewingByPen.builder()
                .penId(UUID.fromString(source.getPenId()))
                .penName(source.getPenName())
                .countYes(source.getCountYes())
                .countNo(source.getCountNo())
                .yesPercent(source.getYesPercent())
                .noPercent(source.getNoPercent())
                .build();

    CosmosToModelMapper<CudChewingToolCosmos, CudChewingTool> mapper =
        source ->
            CudChewingTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .cudChewingReports(
                    source.getCudChewingReports() != null
                        ? source.getCudChewingReports().stream().map(penMapper::map).toList()
                        : null)
                .build();

    return mapper.map(input);
  }
}
