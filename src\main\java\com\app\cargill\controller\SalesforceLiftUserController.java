/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.model.SalesForceUser;
import com.app.cargill.service.admin.UserAdminService;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.azure.core.annotation.QueryParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/salesforce/lift/user")
@Tag(name = "Salesforce Lift User", description = "Controller related to actions over User objects")
@RequiredArgsConstructor
@Slf4j
public class SalesforceLiftUserController {

  private final LiftUserService liftUserService;
  private final UserAdminService userAdminService;

  @GetMapping("/find")
  @Operation(summary = "Get a LIFT User", description = "Get a lift user by email")
  public User getAccount(@QueryParam("email") String email) {
    return liftUserService.findUserByEmail(email);
  }

  @PutMapping("/update-data")
  @Operation(
      summary = "Update users LIFT data",
      description = "Update Salesforce data for users with country Global,US,Canada and Brazil")
  public Flux<SalesForceUser> getUsersDetailFromLift() {
    return userAdminService.updateLiftUsersData();
  }
}
