/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RumenHealthManureScoreItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("AnimalNumbers")
  public Integer animalNumbers;

  @JsonProperty("ScoreCategory")
  public Double scoreCategory;

  @JsonProperty("PercentOfPen")
  public Double percentOfPen;
}
