/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.app.cargill.constants.MilkingSystem;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MilkingSystemDeserializer extends JsonDeserializer<MilkingSystem> {

  @Override
  public MilkingSystem deserialize(
      JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    try {
      return MilkingSystem.values()[Integer.parseInt(node.asText())];
    } catch (Exception e) {
      log.warn(
          "Error deserializing MilkingSystem: {} Falling back to MilkingSystem.Other",
          node.asText());
      return MilkingSystem.Other;
    }
  }
}
