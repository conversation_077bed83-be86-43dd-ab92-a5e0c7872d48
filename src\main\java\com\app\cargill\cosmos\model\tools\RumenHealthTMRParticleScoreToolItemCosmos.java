/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import java.util.List;
import lombok.Getter;

@Getter
public class RumenHealthTMRParticleScoreToolItemCosmos extends EditableToolPenEntityBaseCosmos {
  @JsonProperty("VisitsSelected")
  private List<String> visitsSelected;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("TopScaleAmountInGrams")
  private Double topScaleAmountInGrams;

  @JsonProperty("TopGoalMinimumPercent")
  private Double topGoalMinimumPercent;

  @JsonProperty("TopGoalMaximumPercent")
  private Double topGoalMaximumPercent;

  @JsonProperty("TopScreenTareAmountInGrams")
  private Double topScreenTareAmountInGrams;

  @JsonProperty("Mid1ScaleAmountInGrams")
  private Double mid1ScaleAmountInGrams;

  @JsonProperty("Mid1GoalMinimumPercent")
  private Double mid1GoalMinimumPercent;

  @JsonProperty("Mid1GoalMaximumPercent")
  private Double mid1GoalMaximumPercent;

  @JsonProperty("Mid1ScreenTareAmountInGrams")
  private Double mid1ScreenTareAmountInGrams;

  @JsonProperty("Mid2ScaleAmountInGrams")
  private Double mid2ScaleAmountInGrams;

  @JsonProperty("Mid2GoalMinimumPercent")
  private Double mid2GoalMinimumPercent;

  @JsonProperty("Mid2GoalMaximumPercent")
  private Double mid2GoalMaximumPercent;

  @JsonProperty("Mid2ScreenTareAmountInGrams")
  private Double mid2ScreenTareAmountInGrams;

  @JsonProperty("TrayScaleAmountInGrams")
  private Double trayScaleAmountInGrams;

  @JsonProperty("TrayGoalMinimumPercent")
  private Double trayGoalMinimumPercent;

  @JsonProperty("TrayGoalMaximumPercent")
  private Double trayGoalMaximumPercent;

  @JsonProperty("TrayScreenTareAmountInGrams")
  private Double trayScreenTareAmountInGrams;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;

  @JsonProperty("TMRScoreName")
  private String tmrScoreName;

  @JsonProperty("TMRScoreId")
  private String tmrScoreId;

  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;
}
