/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ReturnOverFeedType;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.ReturnOverFeedPricePerTonDto;
import com.app.cargill.service.IReturnOverFeedPricingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pricing")
@Tag(
    name = "Return Over Feed Pricing Information Controller",
    description = "Return Over Feed Pricing Information Controller")
@RequiredArgsConstructor
public class ReturnOverFeedPricingController extends BaseController {

  private final IReturnOverFeedPricingService returnOverFeedPricingService;

  @GetMapping
  @Operation(
      summary =
          "Get price per ton by Return Over Feed Type or return all of them if there is no type",
      description =
          "This api will return price per ton by Return Over Feed Type or return all of them if"
              + " there is no type")
  public ResponseEntity<ResponseEntityDto<List<ReturnOverFeedPricePerTonDto>>> getPricePerTon(
      @RequestParam ReturnOverFeedType returnOverFeedType) {
    return handleSuccessResponse(returnOverFeedPricingService.getPricePerTon(returnOverFeedType));
  }

  @PutMapping
  @Operation(
      summary = "Update price per ton of objects from admin panel",
      description =
          "This api will allow the admin panel to update the prices of the return over feed"
              + " pricing")
  ResponseEntity<ResponseEntityDto<ReturnOverFeedPricePerTonDto>> getPricePerTon(
      @RequestBody ReturnOverFeedPricePerTonDto returnOverFeedPricePerTon) {
    return handleSuccessResponse(returnOverFeedPricingService.update(returnOverFeedPricePerTon));
  }
}
