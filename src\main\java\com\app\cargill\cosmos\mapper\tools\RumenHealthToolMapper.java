/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.RumenHealthToolCosmos;
import com.app.cargill.cosmos.model.tools.RumenHealthToolItemCosmos;
import com.app.cargill.document.RumenHealthTool;
import com.app.cargill.document.RumenHealthToolItem;
import java.util.UUID;

public class RumenHealthToolMapper {

  private RumenHealthToolMapper() {}

  public static RumenHealthTool map(RumenHealthToolCosmos input) {

    CosmosToModelMapper<RumenHealthToolItemCosmos, RumenHealthToolItem> itemMapper =
        source ->
            RumenHealthToolItem.builder()
                .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
                .penName(source.getPenName())
                .cudChewsCount(source.getCudChewsCount())
                .cudChewingCowsCount(source.getCudChewingCowsCount())
                .build();

    CosmosToModelMapper<RumenHealthToolCosmos, RumenHealthTool> mapper =
        source ->
            RumenHealthTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .goals(source.getGoals())
                .build();

    return mapper.map(input);
  }
}
