/* Cargill Inc.(C) 2022 */
package com.app.cargill.components;

import com.app.cargill.constants.TemplateExportType;
import freemarker.ext.beans.BeansWrapperBuilder;
import freemarker.template.Configuration;
import freemarker.template.TemplateException;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.output.FileWriterWithEncoding;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

@Component
@RequiredArgsConstructor
@Slf4j
@SuppressWarnings("java:S4042")
public class FreeMarkerComponent {
  private final Configuration fmConfiguration;
  private final PlayWrightComponent playWrightComponent;

  public String renderTemplate(Map<String, Object> model, String templateName, Locale locale) {
    try {
      fmConfiguration.setEncoding(locale, StandardCharsets.UTF_8.name());
      fmConfiguration.setDefaultEncoding("utf-8");
      fmConfiguration.setNumberFormat("computer");
      fmConfiguration.setClassForTemplateLoading(this.getClass(), "/templates/");
      return FreeMarkerTemplateUtils.processTemplateIntoString(
          fmConfiguration.getTemplate(templateName), model);
    } catch (IOException e) {
      log.error("IOException While rendering template " + e.getLocalizedMessage());
    } catch (TemplateException e) {
      log.error("TemplateException While rendering template " + e.getLocalizedMessage());
    }
    return "Export Failed!!!";
  }

  public byte[] render(
      Object dto,
      String reportName,
      ResourceBundleMessageSource source,
      Locale locale,
      TemplateExportType exportType) {
    Map<String, Object> model = new HashMap<>();
    model.put("model", dto);
    model.put("localization", source);
    model.put("locale", locale);
    model.put("region", locale.toLanguageTag());
    model.put(
        "statics", new BeansWrapperBuilder(Configuration.VERSION_2_3_31).build().getStaticModels());
    String generatedFilePath =
        System.getProperty("user.dir") + PlayWrightComponent.GENERATED_TEMPLATE_FOLDER;
    byte[] byteArrayResource = null;
    String renderedTemplate = renderTemplate(model, reportName, locale);
    File tempFile = null;
    try {
      Path directoryPath = Files.createDirectories(Paths.get(generatedFilePath));
      tempFile =
          File.createTempFile(
              reportName.replace(".ftl", ""), UUID.randomUUID() + ".html", directoryPath.toFile());
      try (BufferedWriter bufferedWriter =
          new BufferedWriter(new FileWriterWithEncoding(tempFile, StandardCharsets.UTF_8))) {
        bufferedWriter.write(renderedTemplate);
        bufferedWriter.flush();
      }
      byteArrayResource =
          playWrightComponent.initAndPrepareExport(tempFile.getAbsolutePath(), exportType);
    } catch (IOException e) {
      log.error("IOException While writing html template" + e.getLocalizedMessage());
    } finally {
      if (tempFile != null && !tempFile.delete()) {
        try {
          Files.delete(tempFile.toPath());
        } catch (IOException e) {
          log.error(e.getLocalizedMessage());
        }
      }
    }

    return byteArrayResource;
  }
}
