/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.UrinePHGoalCosmos;
import com.app.cargill.cosmos.model.tools.UrinePHToolCosmos;
import com.app.cargill.cosmos.model.tools.UrinePHToolItemCosmos;
import com.app.cargill.document.UrinePHGoal;
import com.app.cargill.document.UrinePHTool;
import com.app.cargill.document.UrinePHToolItem;
import java.time.Instant;
import java.util.UUID;

public class UrinePHToolMapper {

  private UrinePHToolMapper() {}

  public static UrinePHTool map(UrinePHToolCosmos input) {

    CosmosToModelMapper<UrinePHGoalCosmos, UrinePHGoal> goalMapper =
        source ->
            UrinePHGoal.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(
                    source.getLastModifiedTimeUtc() != null
                        ? source.getLastModifiedTimeUtc().getDate()
                        : Instant.MIN)
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .goalMin(source.getGoalMin())
                .goalMax(source.getGoalMax())
                .build();

    CosmosToModelMapper<UrinePHToolItemCosmos, UrinePHToolItem> itemMapper =
        source ->
            UrinePHToolItem.builder()
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(
                    source.getLastModifiedTimeUtc() != null
                        ? source.getLastModifiedTimeUtc().getDate()
                        : Instant.MIN)
                .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
                .penName(source.getPenName())
                .dietDCAD(source.getDietDCAD())
                .cowUrinePH(source.getCowUrinePH())
                .averageUrinePHVisitsSelected(
                    source.getAverageUrinePHVisitsSelected() != null
                        ? source.getAverageUrinePHVisitsSelected().stream()
                            .map(UUID::fromString)
                            .toList()
                        : null)
                .isToolItemNew(source.getIsToolItemNew())
                .urinePHGoal(
                    source.getUrinePHGoal() != null
                        ? goalMapper.map(source.getUrinePHGoal())
                        : null)
                .build();

    CosmosToModelMapper<UrinePHToolCosmos, UrinePHTool> mapper =
        source ->
            UrinePHTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .build();

    return mapper.map(input);
  }
}
