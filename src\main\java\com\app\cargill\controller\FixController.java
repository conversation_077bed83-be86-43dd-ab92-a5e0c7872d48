/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.cosmos.migration.AccountsDataFixService;
import com.app.cargill.cosmos.migration.SitesDataFixService;
import com.app.cargill.cosmos.migration.VisitsDataFixService;
import com.app.cargill.document.SitesDeletedTransform;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.LongRunningTask.TaskName;
import com.app.cargill.model.TaskStatus;
import com.app.cargill.model.tasks.CosmosMigrationMeta;
import com.app.cargill.service.data.MergeAccountsService;
import com.app.cargill.service.data.MergeSitesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/fix")
@Tag(name = "Fixes Controller", description = "Provides various fixes for data inconsistencies")
@RequiredArgsConstructor
@Slf4j
public class FixController {

  private final AccountsDataFixService accountsDataFixService;
  private final SitesDataFixService sitesDataFixService;
  private final VisitsDataFixService visitsDataFixService;
  private final MergeAccountsService mergeAccountsService;
  private final MergeSitesService mergeSitesService;

  @PostMapping("/accounts")
  @Operation(
      summary = "Fix Sites count and last visit for Accounts",
      description =
          "This api will recalculate site count and last visit populate it for each account")
  public LongRunningTask<CosmosMigrationMeta> runAccountsPostMigration() {
    CompletableFuture<List<Accounts>> accountsFuture =
        accountsDataFixService.runAccountsPostMigration().collectList().toFuture();
    accountsFuture.whenComplete(this::postFixLogging);
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/accounts/owner-in-users")
  @Operation(
      summary = "Fix Accounts missing owner in users",
      description =
          "Some accounts are missing the owner as part of the users array. This should fix them.")
  public LongRunningTask<CosmosMigrationMeta> runAccountsOwnerInUsersFix() {
    CompletableFuture<List<Accounts>> accountsFuture =
        accountsDataFixService.fixOwnerMissingFromUsers().collectList().toFuture();
    accountsFuture.whenComplete(
        (mr, t) -> {
          if (t != null) {
            log.error("Error completing runAccountsOwnerInUsersFix", t);
          } else {
            log.info("runAccountsOwnerInUsersFix completed for {} accounts", mr.size());
          }
        });
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/user")
  @Operation(
      summary = "Fix Sites count and last visit for Accounts",
      description =
          "This api will recalculate site count and last visit populate it for each account")
  public LongRunningTask<CosmosMigrationMeta> runUserAccountsPostMigration(
      @RequestBody List<String> users) {
    for (String user : users) {
      CompletableFuture<List<Accounts>> accountsFuture =
          accountsDataFixService.runUserAccountsPostMigration(user).collectList().toFuture();
      accountsFuture.whenComplete(this::postFixLogging);
    }
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/sites")
  @Operation(summary = "Fix SiteMappings for sites")
  public LongRunningTask<CosmosMigrationMeta> runSitesFix() {
    sitesDataFixService
        .fixSiteMissingSiteMappings()
        .doOnComplete(() -> log.info("FIX_SITES_COMPLETED"))
        .subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/sites/hasReport")
  @Operation(summary = "Fix has DDW report boolean for sites")
  public LongRunningTask<CosmosMigrationMeta> sitesHasReport() {
    sitesDataFixService
        .fixHasReportInSites()
        .doOnComplete(() -> log.info("Fixed hasReport boolean in sites"))
        .subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/tmrPenstate/index")
  @Operation(summary = "Fix Indexes inside TMR Penn State")
  public LongRunningTask<CosmosMigrationMeta> runTmrPenstateIndexesFix() {
    visitsDataFixService.tmrPenStateIndexesFix().subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/sites/external-id")
  @Operation(summary = "Fix ExternalId for LIFT sites")
  public LongRunningTask<CosmosMigrationMeta> runSitesExternalIdFix() {
    sitesDataFixService.fixSiteMissingExternalId().subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/sites/switched_mappings")
  @Operation(summary = "Fix Switched SiteMappings for all existing sites")
  public LongRunningTask<CosmosMigrationMeta> runSwitchedMappingsSitesFix() {
    sitesDataFixService.fixSiteSwitchedSiteMappings().subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/sites/visits")
  @Operation(summary = "Fix Visits reference object inside Sites")
  public LongRunningTask<CosmosMigrationMeta> runVisitsReferenceFix() {
    sitesDataFixService.fixSitesVisitReference().subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/siteIds/visits")
  @Operation(summary = "Fix Visits reference object inside Sites for site Ids")
  public List<String> runVisitsReferenceFixForSiteIds(@RequestBody List<String> siteIds) {

    return sitesDataFixService.fixSitesVisitReferenceWithSiteIds(siteIds);
  }

  @PostMapping("/accountOwners")
  @Operation(
      summary = "Fix Account Owner ID that contains email address",
      description =
          "This api will update the account documents that contains @ in the ownerID field and set"
              + " the value to one of the users")
  public Flux<Accounts> updateAccountOwnerId() {
    return accountsDataFixService.updateAccountsWithWrongOwnerId();
  }

  @PostMapping("/visits")
  @Operation(
      summary = "Fix Mobile Last Updated date for Recent visits listing",
      description = "This api will update Mobile Last Updated date for Recent visits listing")
  public LongRunningTask<CosmosMigrationMeta> updateMobileLastUpdatedDate() {
    visitsDataFixService.updateMobileLastUpdatedDate().subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/sites/ddw")
  @Operation(summary = "Fix Missing DDW mappings")
  public LongRunningTask<CosmosMigrationMeta> updateMissingDdwMappings() {
    sitesDataFixService.fixSitesWithMissingDdwMapping().subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/transfer/account/{source}/{target}")
  @Operation(summary = "Transfer all account data to another account")
  public Map<String, List<String>> transferAccountData(
      @PathVariable String source, @PathVariable String target) {
    return mergeAccountsService.transferAccountData(source, target);
  }

  @PostMapping("/transfer/notes-and-visits/{source}/{target}")
  @Operation(summary = "Transfer all account data to another account")
  public Map<String, List<String>> transferNotesAndVisits(
      @PathVariable UUID source, @PathVariable UUID target) {
    return mergeAccountsService.transferNotesAndVisits(source, target);
  }

  @PostMapping("/transfer/site/{source}/{target}")
  @Operation(summary = "Transfer all site data to another account")
  public Map<String, List<String>> transferSiteData(
      @PathVariable UUID source, @PathVariable UUID target) {
    return mergeSitesService.transferSitesData(source, target);
  }

  @PostMapping("/transfer/diet/site/{MaxSiteId}/{oldSiteId}")
  @Operation(summary = "Transfer diet from one site to another")
  public Map<String, List<String>> transferDietFromOneSiteToAnother(
      @PathVariable UUID MaxSiteId, @PathVariable UUID oldSiteId) {
    return mergeSitesService.transferDietFromOneSiteToAnother(MaxSiteId, oldSiteId);
  }

  @PostMapping("/transfer/merged/{goldenRecordId}")
  @Operation(summary = "Transfer all account data to another account based on goldenRecordId")
  public List<Map<String, List<String>>> transferMergedAccountsData(
      @PathVariable String goldenRecordId) {
    return mergeAccountsService.fixMergedAccounts(goldenRecordId);
  }

  @PostMapping("/sites/missing-mappings")
  @Operation(
      summary = "Fix Sites count and last visit for Accounts",
      description =
          "This api will recalculate site count and last visit populate it for each account")
  public LongRunningTask<CosmosMigrationMeta> fixSitesWithDeletedMappings(
      @RequestBody List<UUID> sites) {
    sitesDataFixService
        .fixSitesWithDeletedMappings(sites)
        .doOnComplete(() -> log.info("FIX_SITES_WITH_DELETED_MAPPINGS_COMPLETED"))
        .subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/locomotionScore")
  @Operation(
      summary = "Update locomotion score data with pens data",
      description = "This api will update locomotion score tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateLocomotionScorePensData() {
    visitsDataFixService.updateLocomotionScorePensData().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/animalAnalysis")
  @Operation(
      summary = "Update AnimalAnalysis score data with pens data",
      description =
          "This api will update Animal Analysis score tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateAnimalAnalysisPensData() {
    visitsDataFixService.updateAnimalAnalysisPensData().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/bodyCondition")
  @Operation(
      summary = "Update body condition data with pens data",
      description = "This api will update body condition tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateBodyConditionPensData() {
    visitsDataFixService.updateBodyConditionPensData().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/tmrParticleScore")
  @Operation(
      summary = "Update Tmr Particle Score data with pens data",
      description = "This api will update tmtparticlescore  tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateTmrParticleScore() {
    visitsDataFixService.updateTmrParticleScore().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/rumenHealthManureScore")
  @Operation(
      summary = "Update RumenHealthManureScore data with pens data",
      description =
          "This api will update RumenHealthManureScore tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateRumenHealthManureScore() {
    visitsDataFixService.updateRumenHealthManureScore().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/rumenFillManureScore")
  @Operation(
      summary = "Update RumenFillManureScore data with pens data",
      description =
          "This api will update RumenFillManureScore tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateRumenFillManureScore() {
    visitsDataFixService.updateRumenFillManureScore().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/cudChewing")
  @Operation(
      summary = "Update CudChewing data with pens data",
      description = "This api will update CudChewing tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateCudChewing() {
    visitsDataFixService.updateCudChewing().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/manureScreenerTool")
  @Operation(
      summary = "Update ManureScreenerTool data with pens data",
      description = "This api will update ManureScreenerTool tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updateManureScreenerToolScore() {
    visitsDataFixService.updateManureScreenerTool().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/pen/visit/penTimeBudgetTool")
  @Operation(
      summary = "Update PenTimeBudgetTool data with pens data",
      description = "This api will update PenTimeBudgetTool tool data with the related pens data")
  public LongRunningTask<CosmosMigrationMeta> updatePenTimeBudgetTool() {
    visitsDataFixService.updatePenTimeBudgetTool().subscribe();

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  @PostMapping("/accounts/users")
  @Operation(
      summary = "Fix Users array in accounts",
      description = "This api will fix users array in accounts")
  public LongRunningTask<CosmosMigrationMeta> fixAccountsUserArray() {
    accountsDataFixService
        .fixUserArrayInAccounts()
        .doOnComplete(() -> log.info("FIXED ACCOUNTS USER ARRAYS"))
        .subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }

  private void postFixLogging(List<Accounts> mr, Throwable t) {
    if (t != null) {
      log.error("Error completing Update SiteCount and LastVisitDate for Accounts", t);
    } else {
      log.info(
          "UPDATING_SITE_COUNT_AND_LAST_VISIT_DATE_COMPLETED for {} accounts updated", mr.size());
    }
  }

  @PostMapping("/site/sitesDeletionMigrationFix")
  @Operation(
      summary = "Fix for Site Deleted in DE when deleted in LIft ",
      description =
          "This api will update the From SiteId to To SiteIds. This is required when site is"
              + " deleted in Lift and it is auto deleted in DE App, those refernce with deleted"
              + " site ids has be updated")
  public void updateDeletedSiteId(@RequestBody SitesDeletedTransform sitesDeletedTransform) {
    visitsDataFixService.updateVisitsNotesDietsForDeletedSiteIds(sitesDeletedTransform);
  }

  @PostMapping("/visits/backslashes")
  @Operation(
      summary = "Fix visit name, remove backslashes and replace with dash",
      description = "This api will uFix visit name, remove backslashes and replace with dash")
  public LongRunningTask<CosmosMigrationMeta> fixVisitName() {
    visitsDataFixService.fixVisitName().subscribe();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.UNDEFINED, TaskStatus.RUNNING);
  }
}
