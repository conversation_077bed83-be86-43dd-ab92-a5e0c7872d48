/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.document.DateEpoch;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import lombok.Getter;

@Container(containerName = "CountryTools")
@Getter
public class CountryToolCosmos {

  private String id;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("CountryId")
  private Integer countryId;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("ToolId")
  private Integer toolId;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("ToolGroupId")
  private Integer toolGroupId;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("IsNew")
  private boolean isNew;
}
