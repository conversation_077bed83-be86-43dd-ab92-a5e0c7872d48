/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkRecording extends HerdBase {

  @JsonProperty("Average305Fat")
  private String average305Fat;

  @JsonProperty("Average305FatProtein")
  private String average305FatProtein;

  @JsonProperty("Average305Milk")
  private String average305Milk;

  @JsonProperty("Average305MilkME")
  private String average305MilkME;

  @JsonProperty("Average305Protein")
  private String average305Protein;

  @JsonProperty("AverageAge")
  private String averageAge;

  @JsonProperty("AverageDIM")
  private String averageDIM;

  @JsonProperty("AverageDailyYieldKg")
  private String averageDailyYieldKg;

  @JsonProperty("AverageECMKg")
  private String averageECMKg;

  @JsonProperty("AverageFCM")
  private String averageFCM;

  @JsonProperty("AverageFPCM")
  private String averageFPCM;

  @JsonProperty("AverageFatPerc")
  private String averageFatPerc;

  @JsonProperty("AverageLactationValue")
  private String averageLactationValue;

  @JsonProperty("AverageLactose")
  private String averageLactose;

  @JsonProperty("AverageNetProfit")
  private String averageNetProfit;

  @JsonProperty("AverageProtienPerc")
  private String averageProtienPerc;

  @JsonProperty("AverageSCC")
  private String averageSCC;

  @JsonProperty("AverageSPP")
  private String averageSPP;

  @JsonProperty("AverageUrea")
  private String averageUrea;

  @JsonProperty("ComponentEfficiency")
  private String componentEfficiency;

  @JsonProperty("DryMatterIntake")
  private String dryMatterIntake;

  @JsonProperty("FP1")
  private String fP1;

  @JsonProperty("FP14")
  private String fP14;

  @JsonProperty("NrSCC200To400")
  private String nrSCC200To400;

  @JsonProperty("NrSCCAbove400")
  private String nrSCCAbove400;

  @JsonProperty("NrSCCBelow200")
  private String nrSCCBelow200;

  @JsonProperty("NrSCCRecorded")
  private String nrSCCRecorded;

  @JsonProperty("NumberCowsInMilk")
  private String numberCowsInMilk;
}
