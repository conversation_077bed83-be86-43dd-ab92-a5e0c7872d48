/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkInformation implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("NumberOfMilkings")
  private String numberOfMilkings;

  @JsonProperty("TotalProductionHerd")
  private Double totalProductionHerd;

  @JsonProperty("TotalProduction")
  private Double totalProduction;

  @JsonProperty("MilkPrice")
  private Double milkPrice;

  @JsonProperty("DIM")
  private Double DIM;

  @JsonProperty("ProductionIn150DIM")
  private Double productionIn150DIM;

  @JsonProperty("MilkFatPercentage")
  private Double milkFatPercentage;

  @JsonProperty("MilkProteinPercentage")
  private Double milkProteinPercentage;

  @JsonProperty("SomanticCellCount")
  private Double somanticCellCount;

  @JsonProperty("BacteriaCellCount")
  private Double bacteriaCellCount;

  @JsonProperty("MUN")
  private Double MUN;
}
