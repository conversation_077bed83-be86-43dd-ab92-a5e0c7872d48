/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PurchaseBagFeed implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("TotalHerdPerDay")
  private Double totalHerdPerDay;

  @JsonProperty("FeedName")
  private String feedName;

  @JsonProperty("TotalDryMatter")
  private Double totalDryMatter;

  @JsonProperty("DryMatter")
  private Double dryMatter;

  @JsonProperty("PricePerTon")
  private Double pricePerTon;
}
