/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.RumenHealthManureScoreToolCosmos;
import com.app.cargill.cosmos.model.tools.RumenHealthManureScoreToolItemCosmos;
import com.app.cargill.document.RumenHealthManureScoreTool;
import com.app.cargill.document.RumenHealthManureScoreToolItem;
import java.util.UUID;

public class RumenHealthManureScoreToolMapper {

  private RumenHealthManureScoreToolMapper() {}

  public static RumenHealthManureScoreTool map(RumenHealthManureScoreToolCosmos input) {

    CosmosToModelMapper<RumenHealthManureScoreToolItemCosmos, RumenHealthManureScoreToolItem>
        itemMapper =
            source ->
                RumenHealthManureScoreToolItem.builder()
                    .penId(UUID.fromString(source.getPenId()))
                    .penName(source.getPenName())
                    .createTimeUtc(source.getCreateTimeUtc())
                    .lastModifiedTimeUtc(
                        source.getLastModifiedTimeUtc() != null
                            ? source.getLastModifiedTimeUtc().getDate()
                            : source.getCreateTimeUtc())
                    .manureScoreVisitsSelected(
                        source.getManureScoreVisitsSelected() != null
                            ? source.getManureScoreVisitsSelected().stream()
                                .map(UUID::fromString)
                                .toList()
                            : null)
                    .manureScores(source.getManureScores())
                    .isToolItemNew(source.getIsToolItemNew())
                    .daysInMilk(source.getDaysInMilk())
                    .isFirstTimeWithScore(source.getIsFirstTimeWithScore())
                    .build();

    CosmosToModelMapper<RumenHealthManureScoreToolCosmos, RumenHealthManureScoreTool> mapper =
        source ->
            RumenHealthManureScoreTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .goals(source.getGoals())
                .build();

    return mapper.map(input);
  }
}
