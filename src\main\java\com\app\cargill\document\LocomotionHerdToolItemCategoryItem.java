/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocomotionHerdToolItemCategoryItem implements Serializable {
  @JsonProperty("Category")
  public Integer category;

  @JsonProperty("HerdGoal")
  public Double herdGoal;

  @JsonProperty("AnimalsObserved")
  public Integer animalsObserved;

  @JsonProperty("HerdAverage")
  public Double herdAverage;

  @JsonProperty("TotalAnimals")
  public Integer totalAnimals;
}
