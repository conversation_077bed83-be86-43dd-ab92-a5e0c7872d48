/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import com.app.cargill.crescendo.model.CrescendoMetadata;
import com.app.cargill.crescendo.service.ICrescendoApiService;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.model.AuthToken;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@Service
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings({"java:S3077", "java:S2696"})
public class CrescendoApiServiceImpl implements ICrescendoApiService {

  private static final int HOUR = 1 * 60 * 60 * 1000;
  @Getter @Setter private static volatile CrescendoMetadata crescendoMetaData;
  @Getter private final CrescendoApiReactiveService crescendoApiReactiveService;

  @Override
  public AuthToken getAuthToken() {
    try {
      log.trace("Requesting CRESCENDO token");
      AuthToken authToken = crescendoApiReactiveService.getToken().block();
      log.info("CRESCENDO token obtained Successfully");
      return authToken;
    } catch (WebClientResponseException e) {
      log.error(e.getResponseBodyAsString());
      throw e;
    }
  }

  @Scheduled(fixedRate = HOUR)
  @EventListener(ApplicationReadyEvent.class)
  public CrescendoMetadata init() {
    AuthToken authToken = getAuthToken();
    String apiPath = getLatestApiPath(authToken);
    crescendoMetaData = CrescendoMetadata.builder().authToken(authToken).apiPath(apiPath).build();
    return crescendoMetaData;
  }

  public VersionObject getLatestApiVersion(AuthToken authToken) {
    return crescendoApiReactiveService.getLatestApiVersion(authToken).block();
  }

  public String getLatestApiPath(AuthToken authToken) {
    return getLatestApiVersion(authToken).getUrl();
  }
}
