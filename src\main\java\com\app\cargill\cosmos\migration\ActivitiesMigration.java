/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.model.ActivityCosmos;
import com.app.cargill.cosmos.repo.ActivitiesCosmosRepository;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.model.Activities;
import com.app.cargill.repository.ActivitiesRepository;
import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.PeriodType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class ActivitiesMigration implements CosmosDataMigration {

  private final ActivitiesCosmosRepository cosmosRepository;

  private final ActivitiesRepository dbRepository;

  @Override
  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult(MigrationType.ACTIVITIES.name());

    DateTime start = DateTime.now();
    AtomicInteger failedItems = new AtomicInteger(0);
    Flux<Activities> activitiesFlux = fetchAll(failedItems);
    return processRecords(activitiesFlux, failedItems)
        .reduce(0, (accumulator, a) -> accumulator + 1)
        .map(
            v -> {
              DateTime end = DateTime.now();
              Duration duration = new Duration(start, end);
              migrationResult.setFailed(failedItems.get());
              migrationResult.setSucceeded(v);
              log.info(
                  "Time taken for Activities migration: {} start: {} end: {}",
                  duration.toPeriod().normalizedStandard(PeriodType.standard()),
                  start,
                  end);

              log.info("Activities migration completed. {}", migrationResult);
              return migrationResult;
            })
        .toFuture();
  }

  public Flux<Activities> moveRecords(String accountId) {
    AtomicInteger failedRecords = new AtomicInteger(0);
    Flux<Activities> cosmosRecords = fetchAccountRecords(accountId, failedRecords);
    return processRecords(cosmosRecords, failedRecords);
  }

  @Override
  public MigrationType migrationType() {
    return MigrationType.ACTIVITIES;
  }

  private Flux<Activities> fetchAll(AtomicInteger failedItems) {
    return processCosmosRecords(cosmosRepository.findAll(), failedItems);
  }

  private boolean filterCosmosData(ActivityCosmos activitiesCosmos) {
    return true;
  }

  private Flux<Activities> fetchAccountRecords(String accountId, AtomicInteger failedRecords) {
    return processCosmosRecords(cosmosRepository.findAllByAccountId(accountId), failedRecords);
  }

  private Flux<Activities> processCosmosRecords(
      Flux<ActivityCosmos> cosmosFlux, AtomicInteger failedDocuments) {
    return cosmosFlux
        .filter(this::filterCosmosData)
        .map(this::cosmosToDocumentMapper)
        .map(
            document -> {
              Activities activity = new Activities(document);
              activity.setLocalId(document.getId().toString());
              return activity;
            })
        .onErrorContinue(
            (throwable, object) -> {
              failedDocuments.incrementAndGet();
              log.error("Error during Activities migration: {}", object);
              log.error("Error", throwable);
            });
  }

  private Flux<Activities> processRecords(
      Flux<Activities> activitiesFlux, AtomicInteger failedDetails) {
    return activitiesFlux
        .filterWhen(
            a ->
                Mono.fromCallable(() -> checkIfExists(failedDetails, a))
                    .subscribeOn(Schedulers.boundedElastic()))
        .flatMap(
            a ->
                Mono.fromCallable(() -> dbRepository.save(a))
                    .subscribeOn(Schedulers.boundedElastic()))
        .onErrorContinue(
            (t, o) -> {
              log.info(failedDetails.toString());
              failedDetails.incrementAndGet();
              log.error("Error with object: {}", o);
              log.error("Error during Activity save in DB", t);
            });
  }

  private ActivityDocument cosmosToDocumentMapper(ActivityCosmos activitiesCosmos) {
    ActivityDocument document = new ActivityDocument();
    document.setId(UUID.fromString(activitiesCosmos.getId()));
    document.setCreateUser(activitiesCosmos.getCreateUser());
    document.setDeleted(activitiesCosmos.getIsDeleted());
    document.setLastModifyUser(activitiesCosmos.getLastModifyUser());
    document.setCreateTimeUtc(activitiesCosmos.getCreateTimeUtc());
    if (activitiesCosmos.getLastModifiedTimeUtc() != null) {
      document.setLastModifiedTimeUtc(
          Instant.ofEpochSecond(activitiesCosmos.getLastModifiedTimeUtc().getEpoch()));
    }
    document.setLastSyncTimeUtc(activitiesCosmos.getLastSyncTimeUtc());
    document.setActivityDateTime(activitiesCosmos.getActivityDateTime());
    document.setActivityType(activitiesCosmos.getActivityType());
    document.setSubject(activitiesCosmos.getSubject());
    document.setIsPrivate(activitiesCosmos.getIsPrivate());
    document.setIsGroupEvent(activitiesCosmos.getIsGroupEvent());
    document.setEventLocation(activitiesCosmos.getEventLocation());
    document.setEventStartDate(activitiesCosmos.getEventStartDate());
    document.setAllDayEvent(activitiesCosmos.getAllDayEvent());
    document.setSfdcAccountId(activitiesCosmos.getSfdcAccountId());
    if (activitiesCosmos.getVisitId() != null) {
      document.setVisitId(UUID.fromString(activitiesCosmos.getVisitId()));
    }
    document.setName(activitiesCosmos.getName());
    document.setNeedsSync(activitiesCosmos.getNeedsSync());
    document.setSfdcId(activitiesCosmos.getSfdcId());
    document.setSfdcVisitId(activitiesCosmos.getSfdcVisitId());
    if (activitiesCosmos.getAccountId() != null) {
      document.setAccountId(UUID.fromString(activitiesCosmos.getAccountId()));
    }
    document.setAssignedToID(activitiesCosmos.getAssignedToID());
    document.setEndDateTime(activitiesCosmos.getEndDateTime());
    document.setOwnerId(activitiesCosmos.getOwnerId());

    return document;
  }

  private Boolean checkIfExists(AtomicInteger failedItems, Activities a) {
    try {
      return dbRepository.findByDocumentId(a.getActivityDocument().getId().toString()) == null;
    } catch (Exception e) {
      failedItems.incrementAndGet();
      log.error("Error with object: {}", a);
      log.error("Error during Activities find in DB", e);
      return false;
    }
  }
}
