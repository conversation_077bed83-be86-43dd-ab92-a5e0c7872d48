/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.CowFlowDesign;
import com.app.cargill.constants.RobotType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RoboticMilkEvaluationToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Outputs")
  private RoboticMilkEvaluationToolOutputToolItem outputs;

  @JsonProperty("SelectedVisits")
  private List<UUID> selectedVisits;

  @JsonProperty("RobotType")
  private RobotType robotType;

  @JsonProperty("CowFlowDesign")
  private CowFlowDesign cowFlowDesign;

  @JsonProperty("RobotsInHerd")
  private Integer robotsInHerd;

  @JsonProperty("LactatingCows")
  private Integer lactatingCows;

  @JsonProperty("AverageMilkYield")
  private Double averageMilkYield;

  @JsonProperty("Milkings")
  private Double milkings;

  @JsonProperty("RobotFreeTime")
  private Double robotFreeTime;

  @JsonProperty("MilkingRefusals")
  private Double milkingRefusals;

  @JsonProperty("TotalMilkingFailures")
  private Double totalMilkingFailures;

  @JsonProperty("MaximumConcentrate")
  private Double maximumConcentrate;

  @JsonProperty("AverageConcentrateFed")
  private Double averageConcentrateFed;

  @JsonProperty("MinimumConcentrate")
  private Double minimumConcentrate;

  @JsonProperty("AverageBoxTime")
  private Double averageBoxTime;

  @JsonProperty("MilkingSpeed")
  private Double milkingSpeed;

  @JsonProperty("ConcentratePer100KGMilk")
  private Double concentratePer100KGMilk;

  @JsonProperty("RestFeed")
  private Double restFeed;
}
