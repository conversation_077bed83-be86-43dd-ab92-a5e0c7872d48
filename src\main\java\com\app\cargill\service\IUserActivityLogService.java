/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.UserActivityLogDto;
import com.app.cargill.dto.UserActivityLogExportDto;
import com.app.cargill.dto.UserActivityLogResponseDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;
import java.util.Map;

public interface IUserActivityLogService {

    /**
     * Log user activity
     */
    UserActivityLogResponseDto logActivity(UserActivityLogDto activityLogDto);

    /**
     * Log multiple user activities in batch
     */
    List<UserActivityLogResponseDto> logActivitiesBatch(List<UserActivityLogDto> activityLogDtos);

    /**
     * Process a list of events iteratively, saving each one individually to the database
     */
    List<UserActivityLogResponseDto> processEventsListIteratively(List<UserActivityLogDto> eventsList, jakarta.servlet.http.HttpServletRequest request);

    /**
     * Get activity logs by username with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByUsername(String username, Pageable pageable);

    /**
     * Get activity logs by event name with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByEventName(String eventName, Pageable pageable);

    /**
     * Get activity logs by date range with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByDateRange(Instant startDate, Instant endDate, Pageable pageable);

    /**
     * Get activity logs by username and date range with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByUsernameAndDateRange(String username, Instant startDate, Instant endDate, Pageable pageable);

    /**
     * Get activity logs by event name and date range with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByEventNameAndDateRange(String eventName, Instant startDate, Instant endDate, Pageable pageable);

    /**
     * Get activity logs by path pattern with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByPathPattern(String pathPattern, Pageable pageable);

    /**
     * Get activity logs by account ID with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByAccountId(String accountId, Pageable pageable);

    /**
     * Get activity logs by site ID with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsBySiteId(String siteId, Pageable pageable);

    /**
     * Get activity logs by feature used with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByFeatureUsed(String featureUsed, Pageable pageable);

    /**
     * Get activity logs by module name with pagination
     */
    Page<UserActivityLogResponseDto> getActivityLogsByModuleName(String moduleName, Pageable pageable);

    /**
     * Get analytics data for event names
     */
    Map<String, Long> getEventNameAnalytics();

    /**
     * Get analytics data for paths
     */
    Map<String, Long> getPathAnalytics();

    /**
     * Get analytics data for users
     */
    Map<String, Long> getUserAnalytics();

    /**
     * Get daily activity analytics
     */
    Map<String, Long> getDailyActivityAnalytics(Instant startDate, Instant endDate);

    /**
     * Prepare data for Excel export
     */
    UserActivityLogExportDto prepareExportData(Instant startDate, Instant endDate, String username, String eventName, String accountId);

    /**
     * Get activity log by ID
     */
    UserActivityLogResponseDto getActivityLogById(Long id);

    /**
     * Delete activity log by ID (soft delete)
     */
    void deleteActivityLog(Long id);

    /**
     * Get all activity logs with pagination
     */
    Page<UserActivityLogResponseDto> getAllActivityLogs(Pageable pageable);
}
