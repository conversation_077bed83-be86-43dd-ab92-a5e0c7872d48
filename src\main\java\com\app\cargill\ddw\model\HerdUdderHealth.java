/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdUdderHealth extends HerdBase {

  @JsonProperty("AvgDailyYieldKg")
  private String avgDailyYieldKg;

  @JsonProperty("AvgSCC0DIM15")
  private String avgSCC0DIM15;

  @JsonProperty("AvgSCC120DIM200")
  private String avgSCC120DIM200;

  @JsonProperty("AvgSCC15DIM60")
  private String avgSCC15DIM60;

  @JsonProperty("AvgSCC200DIM305")
  private String avgSCC200DIM305;

  @JsonProperty("AvgSCC60DIM120")
  private String avgSCC60DIM120;

  @JsonProperty("AvgSCCDIM305plus")
  private String avgSCCDIM305plus;

  @JsonProperty("AvgSCCLact1")
  private String avgSCCLact1;

  @JsonProperty("AvgSCCLact2")
  private String avgSCCLact2;

  @JsonProperty("AvgSCCLact3plus")
  private String avgSCCLact3plus;

  @JsonProperty("Lactation")
  private String lactation;

  @JsonProperty("MilkRecordingDateId")
  private String milkRecordingDateId;

  @JsonProperty("NrSCC200To400")
  private String nrSCC200To400;

  @JsonProperty("NrSCCAbove400")
  private String nrSCCAbove400;

  @JsonProperty("NrSCCBelow200")
  private String nrSCCBelow200;

  @JsonProperty("avgDIM")
  private String avgDIM;

  @JsonProperty("avgParity")
  private String avgParity;

  @JsonProperty("avgSCC")
  private String avgSCC;

  @JsonProperty("avgSCCOverall")
  private String avgSCCOverall;

  @JsonProperty("avgSCCatDryOff")
  private String avgSCCatDryOff;

  @JsonProperty("avgSCCfirstTest")
  private String avgSCCfirstTest;
}
