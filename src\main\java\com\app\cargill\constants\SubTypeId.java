/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Constants Naming
public enum SubTypeId {
  FarmProducer(8);

  private final Integer typeId;

  SubTypeId(Integer code) {
    this.typeId = code;
  }

  public Integer getTypeId() {
    return typeId;
  }

  public static SubTypeId fromId(int id) {
    for (SubTypeId type : values()) {
      if (type.getTypeId() == id) {
        return type;
      }
    }
    return null;
  }

  public static SubTypeId fromString(String id) {
    for (SubTypeId type : values()) {
      if (type.toString().equals(id)) {
        return type;
      }
    }
    return null;
  }
}
