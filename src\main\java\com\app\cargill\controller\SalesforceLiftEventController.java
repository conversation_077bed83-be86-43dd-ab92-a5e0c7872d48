/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.document.EventDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.service.LiftEventService;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/salesforce/lift/event")
@Tag(
    name = "Salesforce Lift Event",
    description = "Controller related to actions over Event objects")
@RequiredArgsConstructor
@Slf4j
public class SalesforceLiftEventController {
  private final LiftEventService liftEventService;
  private final ResourceBundleMessageSource bundleMessageSource;

  @PostMapping
  @Operation(summary = "Create a LIFT event", description = "Create a lift event by")
  public EventDocument createEvent(@RequestBody EventDocument eventDocument)
      throws JsonProcessingException, CustomDEExceptions {
    return liftEventService.createEvent(eventDocument, Locale.ENGLISH, bundleMessageSource);
  }

  @PutMapping
  @Operation(summary = "Update a LIFT event", description = "Update a lift event by")
  public EventDocument updateEvent(@RequestBody EventDocument eventDocument) {
    return liftEventService.updateEvent(eventDocument);
  }
}
