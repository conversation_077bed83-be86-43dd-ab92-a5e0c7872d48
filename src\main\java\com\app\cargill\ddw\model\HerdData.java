/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdData {

  @JsonProperty("Cullings")
  private List<HerdCulling> cullings;

  @JsonProperty("Health")
  private HerdHealth health;

  @JsonProperty("Inventory")
  private HerdInventory inventory;

  @JsonProperty("Period")
  private HerdPeriod period;

  @JsonProperty("Milkproductions")
  private List<HerdMilkProduction> milkProductions;

  @JsonProperty("Milkrecordings")
  private List<MilkRecording> milkRecordings;

  @JsonProperty("Nutritions")
  private List<HerdNutrition> nutritions;

  @JsonProperty("PenDatas")
  private List<HerdPenData> penDatas;

  @JsonProperty("Reproduction")
  private HerdReproduction reproduction;

  @JsonProperty("Udderhealths")
  private List<HerdUdderHealth> udderHealths;
}
