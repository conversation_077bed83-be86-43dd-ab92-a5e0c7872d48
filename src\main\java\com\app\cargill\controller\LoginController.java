/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.ILoginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
@Tag(name = "Login Information Controller", description = "Login Information Controller")
@RequiredArgsConstructor
@SuppressWarnings("java:S125") // remove when all commented code is removed
public class LoginController extends BaseController {

  private final ILoginService loginServiceImpl;

  /* @GetMapping("/fetchAccessTokenByOktaCode")
  @Operation(summary = "Get access token", description = "This api will return access token")
  public ResponseEntity<ResponseEntityDto<Object>> fetchTokenByOktaCode(
      @RequestParam("code") String code, @RequestParam("state") String state)
      throws UnauthorizedUserDEException {
    try {
      return handleSuccessResponse(loginServiceImpl.fetchAccessTokenByOktaCode(code, state));
    } catch (JwtVerificationException e) {
      throw new UnauthorizedUserDEException(e.getLocalizedMessage());
    } catch (MalformedURLException e) {
      throw new RuntimeException(e);
    }
  }

  @GetMapping("/fetchAccessTokenByAzureAD")
  @Operation(
      summary = "Get token by Azure AD",
      description = "This api will return access token using Azure AD")
  public ResponseEntity<ResponseEntityDto<Object>> fetchAccessTokenByAzureAD(
      @RequestParam("code") String code, @RequestParam("state") String state)
      throws UnauthorizedUserDEException {

    try {
      return handleSuccessResponse(loginServiceImpl.fetchAccessTokenByAzureAD(code, state));
    } catch (JwtVerificationException e) {
      throw new UnauthorizedUserDEException(e.getLocalizedMessage());
    } catch (Exception e) {
      throw new UnauthorizedUserDEException(e.getLocalizedMessage());
    }
  }

  @GetMapping("/fetchAccessToken/{authServer}")
  @Operation(
      summary = "get token by Azure or Okta",
      description = "This api will return access token against Azure AD Or Okta tokens")
  public ResponseEntity<ResponseEntityDto<Object>> fetchAccessToken(
      @PathVariable("authServer") String authServer,
      @RequestParam(value = "accessToken") String token)
      throws UnauthorizedUserDEException {

    try {
      return handleSuccessResponse(loginServiceImpl.fetchAccessToken(authServer, token));
    } catch (Exception e) {
      throw new UnauthorizedUserDEException(e.getLocalizedMessage());
    }
  }*/

  @GetMapping("/logout")
  @Operation(summary = "logout", description = "This method will revoke token")
  public ResponseEntity<ResponseEntityDto<Boolean>> logout() {
    return handleSuccessResponse(loginServiceImpl.logout());
  }

  @PostMapping(value = "/okta-token", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "fetch cargill okta token using credentials",
      description = "This api will return jwt token from cargill okta")
  public Oauth2Dto fetchOktaToken(@RequestParam MultiValueMap<String, String> paramMap) {

    return loginServiceImpl.fetchAccessTokenUsingOkta(paramMap);
  }
}
