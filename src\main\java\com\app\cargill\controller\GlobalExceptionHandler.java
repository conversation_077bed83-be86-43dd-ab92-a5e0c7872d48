/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.exceptions.UnauthorizedUserDEException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler extends BaseController implements AuthenticationEntryPoint {

  private static final char[] CHAR_SUBSET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();

  @ExceptionHandler(Exception.class)
  public void handleMyCustomException(
      HttpServletRequest request, HttpServletResponse response, Exception e) throws IOException {
    sendBadRequestError(request, response, e);
  }

  @Override
  public void commence(
      HttpServletRequest request,
      HttpServletResponse response,
      AuthenticationException authException)
      throws IOException {
    // 401
    sendBadRequestError(request, response, authException);
  }

  private void sendBadRequestError(
      HttpServletRequest request, HttpServletResponse response, Exception e) throws IOException {
    String hash = randomHash();
    if (!response.isCommitted()) {
      modifyResponse(response, e, hash);
    }

    if (e instanceof HttpMessageNotReadableException) {
      String requestBody =
          request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
      log.error(String.format("%1$s - %2$s", hash, StringEscapeUtils.escapeJava(requestBody)));
    } else if (e instanceof InsufficientAuthenticationException) {
      log.warn(String.format("%1$s - %2$s", hash, e.getMessage()), e);
    } else {
      log.error(String.format("%1$s - %2$s", hash, e != null ? e.getMessage() : ""), e);
    }
  }

  private void modifyResponse(HttpServletResponse response, Exception e, String hash)
      throws IOException {
    String errorMessage = String.format("Error occurred: %s", hash);
    response.resetBuffer();
    if (e instanceof AuthenticationException || e instanceof UnauthorizedUserDEException) {
      errorMessage = String.format("Error occurred: %s", e.getLocalizedMessage());
      response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
      response.sendError(HttpServletResponse.SC_UNAUTHORIZED, errorMessage);
    } else {
      response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
      response.sendError(HttpServletResponse.SC_BAD_REQUEST, errorMessage);
    }
    response.flushBuffer();
  }

  private String randomHash() {
    int length = 8;
    try {
      SecureRandom r = SecureRandom.getInstanceStrong();
      char[] buf = new char[length];
      for (int i = 0; i < buf.length; i++) {
        int index = r.nextInt(CHAR_SUBSET.length);
        buf[i] = CHAR_SUBSET[index];
      }
      return new String(buf);
    } catch (NoSuchAlgorithmException e) {
      return "00000000";
    }
  }
}
