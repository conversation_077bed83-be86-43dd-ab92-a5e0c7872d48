/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.document.AnimalAnalysisToolCDP;
import com.app.cargill.dto.ProfitabilityAnalysisData;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.service.ICdpService;
import com.app.cargill.service.ICdpV1Service;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cdp_v1")
@Tag(name = "CDP Data Transfer Controller", description = "CDP Data Transfer Controller")
@Slf4j
@RequiredArgsConstructor
public class CDPV1Controller extends BaseController {

  private final ICdpV1Service cdpV1Service;
  private final ICdpService cdpService;

  @GetMapping(path = "/getAllAccountsByFromAndToDate")
  public ResponseEntity<ResponseEntityDto<List<AccountDocumentDTO>>> getAllAccountsByFromAndToDate(
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateFrom")
          Instant dateFrom,
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateTo")
          Instant dateTo) {
    log.info("getAllAccountsByFromAndToDate invoked");
    List<AccountDocumentDTO> page = cdpV1Service.getAllAccountsByFromAndToDateV1(dateFrom, dateTo);
    return handleSuccessResponse(page);
  }

  @GetMapping(path = "/getAllSitesByFromAndToDate")
  public ResponseEntity<ResponseEntityDto<List<SiteDocumentDTO>>> getAllSitesByFromAndToDate(
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateFrom")
          Instant dateFrom,
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateTo")
          Instant dateTo) {
    log.info("getAllSitesByFromAndToDate invoked");
    List<SiteDocumentDTO> page = cdpV1Service.getAllSitesByFromAndToDateV1(dateFrom, dateTo);
    return handleSuccessResponse(page);
  }

  @GetMapping(path = "/getAllVisitsByFromAndToDate")
  public ResponseEntity<ResponseEntityDto<List<VisitDocumentDTO>>> getAllVisitsByFromAndToDate(
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateFrom")
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          Instant dateFrom,
      @RequestParam(defaultValue = "${app.configurations.default-utc-timestamp}", name = "dateTo")
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
          Instant dateTo) {

    log.info("getAllVisitsByFromAndToDate invoked");
    List<VisitDocumentDTO> page = cdpV1Service.getAllVisitsByFromAndToDateV1(dateFrom, dateTo);
    log.info(
        "getAllVisitsByFromAndToDate {} {} {}",
        page.size(),
        StringEscapeUtils.escapeJava(dateFrom.toString()),
        StringEscapeUtils.escapeJava(dateTo.toString()));
    return handleSuccessResponse(page);
  }

  @GetMapping(path = "/getAllProfitabilityAnalysisDataByFromAndToDate")
  public ResponseEntity<ResponseEntityDto<List<ProfitabilityAnalysisData>>>
      getAllProfitabilityAnalysisDataByFromAndToDate(
          @RequestParam(
                  defaultValue = "${app.configurations.default-utc-timestamp}",
                  name = "dateFrom")
              @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
              Instant dateFrom,
          @RequestParam(
                  defaultValue = "${app.configurations.default-utc-timestamp}",
                  name = "dateTo")
              @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
              Instant dateTo) {

    log.info("getAllProftabilityAnalysisByFromAndToDate invoked");
    List<ProfitabilityAnalysisData> page =
        cdpV1Service.getAllProftabilityAnalysisDataByFromAndToDateV1(dateFrom, dateTo);
    log.info(
        "getAllProftabilityAnalysisByFromAndToDate {} {} {}",
        page.size(),
        StringEscapeUtils.escapeJava(dateFrom.toString()),
        StringEscapeUtils.escapeJava(dateTo.toString()));
    return handleSuccessResponse(page);
  }

  @GetMapping("/getAnimalAnalysisDetails")
  public ResponseEntity<ResponseEntityDto<List<AnimalAnalysisToolCDP>>> getAnimalAnalysisDetails() {
    log.info("getAnimalAnalysisDetails started");
    List<AnimalAnalysisToolCDP> animalAnalysisToolCDPS = cdpV1Service.getAnimalAnalysisDetails();
    log.info("getAnimalAnalysisDetails {}", animalAnalysisToolCDPS);
    return handleSuccessResponse(animalAnalysisToolCDPS);
  }
}
