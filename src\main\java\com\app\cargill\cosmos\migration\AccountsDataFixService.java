/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.DataSource;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.sf.cc.service.LiftAccountService;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * This service handles all Accounts data quality fixes required to run after migration of users or
 * whenever a recalculation is needed
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AccountsDataFixService {

  private final AccountsRepository accountsRepository;
  private final SitesRepository sitesRepository;
  private final VisitsRepository visitsRepository;
  private final LiftAccountService liftAccountService;
  private Integer count = 1;

  public Flux<Accounts> runAccountsPostMigration() {
    Flux<Accounts> accountsFlux =
        Mono.fromCallable(accountsRepository::findAll)
            .subscribeOn(Schedulers.boundedElastic())
            .flatMapMany(Flux::fromIterable);
    return runAccountsFix(accountsFlux);
  }

  public Flux<Accounts> runUserAccountsPostMigration(String userEmail) {
    Flux<Accounts> accountsFlux =
        Mono.just(userEmail)
            .map(accountsRepository::findAccountsByUserEmail)
            .subscribeOn(Schedulers.boundedElastic())
            .flatMapMany(Flux::fromIterable);
    return runAccountsFix(accountsFlux);
  }

  public Flux<Accounts> runAccountsFix(Flux<Accounts> accountsFlux) {
    return accountsFlux
        .flatMap(this::updateDataSource)
        .flatMap(this::updateSiteCount)
        .flatMap(this::updateLastVisit)
        .filter(Accounts::isRecordModified)
        .collectList()
        .flatMap(
            list ->
                Mono.fromCallable(() -> accountsRepository.saveAllAndFlush(list))
                    .subscribeOn(Schedulers.boundedElastic()))
        .flatMapMany(Flux::fromIterable);
  }

  public Mono<Accounts> updateSiteCount(Accounts account) {
    return Mono.fromCallable(
            () -> sitesRepository.countByAccountId(account.getAccountDocument().getId().toString()))
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(
            actualCount -> {
              if (!actualCount.equals(account.getAccountDocument().getSiteCount())) {
                account.getAccountDocument().setSiteCount(actualCount);
                account.setRecordModified(true);
              }
              return Mono.just(account);
            });
  }

  public Flux<Accounts> fixOwnerMissingFromUsers() {
    return Mono.fromCallable(accountsRepository::findAll)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .map(
            account -> {
              if (account.getAccountDocument().getUsers() == null) {
                account.getAccountDocument().setUsers(new HashSet<>());
                account.setRecordModified(true);
              }
              if (!account
                  .getAccountDocument()
                  .getUsers()
                  .contains(account.getAccountDocument().getOwnerId())) {
                account
                    .getAccountDocument()
                    .getUsers()
                    .add(account.getAccountDocument().getOwnerId());
                account.setRecordModified(true);
              }
              return account;
            })
        .filter(Accounts::isRecordModified)
        .flatMap(
            account ->
                Mono.fromCallable(() -> accountsRepository.save(account))
                    .subscribeOn(Schedulers.boundedElastic()))
        .onErrorResume(
            t -> {
              log.error("fixOwnerMissingFromUsers error", t);
              return Mono.empty();
            });
  }

  @Transactional
  public Flux<Accounts> updateAccountsWithWrongOwnerId() {

    return Flux.fromStream(accountsRepository.findAll().stream())
        .filter(accounts -> !hasOwnerWithEmail(accounts))
        .flatMap(this::updateAccountWithUser)
        .collectList()
        .flatMap(
            list ->
                Mono.fromCallable(() -> accountsRepository.saveAllAndFlush(list))
                    .subscribeOn(Schedulers.boundedElastic()))
        .flatMapMany(Flux::fromIterable);
  }

  private boolean hasOwnerWithEmail(Accounts accounts) {

    AccountDocument accountDocument = accounts.getAccountDocument();
    return accountDocument != null
        && accountDocument.getOwnerId() != null
        && accountDocument.getOwnerId().contains("@");
  }

  private Mono<Accounts> updateAccountWithUser(Accounts account) {
    AccountDocument accountDocument = account.getAccountDocument();
    String oldOwnerId = accountDocument.getOwnerId();
    log.info("Updating account old owner ID: {} ", oldOwnerId);
    Set<String> users = accountDocument.getUsers();
    String userEmail = getFirstUserEmailFromList(users);
    String ownerId = userEmail != null ? userEmail : oldOwnerId;
    log.info("OwnerId is set to: {}", ownerId);
    accountDocument.setOwnerId(ownerId);
    return Mono.just(account);
  }

  private String getFirstUserEmailFromList(Set<String> users) {
    return users != null && !users.isEmpty() ? users.stream().toList().get(0) : null;
  }

  private Mono<Accounts> updateLastVisit(Accounts account) {
    return Mono.fromCallable(
            () ->
                Optional.ofNullable(
                    visitsRepository.findLatestVisitByCustomerId(
                        account.getAccountDocument().getId().toString())))
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(latestVisit -> processAccountVisit(latestVisit, account));
  }

  private boolean hasVisitDateRecord(Visits visit) {
    return visit != null
        && visit.getVisitDocument() != null
        && visit.getVisitDocument().getVisitDate() != null;
  }

  private Mono<Accounts> processAccountVisit(
      Optional<Visits> latestVisitOptional, Accounts account) {
    Visits latestVisit = latestVisitOptional.orElse(null);
    if (!hasVisitDateRecord(latestVisit)
        && account.getAccountDocument().getDateOfLastVisit() != null) {
      account.getAccountDocument().setDateOfLastVisit(null);
      account.setRecordModified(true);
    } else if (hasVisitDateRecord(latestVisit)
        && !latestVisit
            .getVisitDocument()
            .getVisitDate()
            .equals(account.getAccountDocument().getDateOfLastVisit())) {
      account
          .getAccountDocument()
          .setDateOfLastVisit(latestVisit.getVisitDocument().getVisitDate());
      account.setRecordModified(true);
    }
    return Mono.just(account);
  }

  private Mono<Accounts> updateDataSource(Accounts account) {
    if (List.of(DataSource.LIFT, DataSource.CRESCENDO)
        .contains(account.getAccountDocument().getDataSource())) {
      // Do nothing if account is already set
      return Mono.just(account);
    }

    return Mono.fromCallable(
            () ->
                liftAccountService.getAllAccounts(
                    List.of(
                        String.format(
                            "id='%s'", account.getAccountDocument().getGoldenRecordId()))))
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(accountsLift -> processAccountDataSource(accountsLift, account))
        .onErrorReturn(account);
  }

  private Mono<Accounts> processAccountDataSource(
      List<AccountDocument> liftAccounts, Accounts input) {
    if (!liftAccounts.isEmpty()) {
      input.getAccountDocument().setDataSource(DataSource.LIFT);
      input.setRecordModified(true);
      processSitesDataSource(input);
    }
    return Mono.just(input);
  }

  private void processSitesDataSource(Accounts input) {
    try {
      List<Sites> accountSites =
          sitesRepository.findAllByAccountId(input.getAccountDocument().getId().toString());
      for (Sites site : accountSites) {
        site.getSiteDocument().setDataSource(input.getAccountDocument().getDataSource());
        saveSiteToDb(site);
      }
    } catch (Exception e) {
      log.error("ERROR_UPDATING_SITES_DATA_SOURCE", e);
    }
  }

  private void saveSiteToDb(Sites site) {
    try {
      sitesRepository.save(site);
    } catch (Exception e) {
      log.error("SITE_SAVE_ERROR");
    }
  }

  public Flux<Accounts> fixUserArrayInAccounts() {
    return Mono.fromCallable(accountsRepository::findAccountsWhereUserArrayIsNotNull)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            account ->
                Mono.fromCallable(() -> fixUsersArray(account))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Accounts fixUsersArray(Accounts account) {
    log.info("Account Number: {}", count);
    Set<String> users = new HashSet<>();
    log.info("accountId: {}", account.getAccountDocument().getId().toString());

    if (!Objects.isNull(account.getAccountDocument().getOwnerId())
        && !account.getAccountDocument().getOwnerId().contains(".invalid")) {
      users.add(account.getAccountDocument().getOwnerId());
    }
    if (!Objects.isNull(account.getAccountDocument().getUserRoles())
        && !account.getAccountDocument().getUserRoles().isEmpty()) {
      account.getAccountDocument().getUserRoles().stream()
          .forEach(
              userRole -> {
                if (userRole != null
                    && !users.contains(userRole.getUserName())
                    && !userRole.getUserName().contains(".invalid")) {
                  users.add(userRole.getUserName());
                }
              });
    }
    account.getAccountDocument().setUsers(users);
    count++;
    accountsRepository.save(account);
    return account;
  }
}
