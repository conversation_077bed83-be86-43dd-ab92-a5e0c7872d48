/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.UserCosmos;
import com.azure.spring.data.cosmos.repository.CosmosRepository;
import com.azure.spring.data.cosmos.repository.Query;
import java.util.List;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UsersCosmosRepository extends CosmosRepository<UserCosmos, String> {
  @Query(value = "Select * FROM c where STRINGEQUALS(@userName, c.UserName, true)")
  List<UserCosmos> getUsersByEmail(@Param("userName") String userName);
}
