/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SitesLiftDeletedWrapperList implements Serializable {

  private List<DeletedRecords> deletedRecords;

  private String earliestDateAvailable;

  private String latestDateCovered;
}
