/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings({"java:S115"})
public enum NoteCategoryType {
  // [Description("Action")]
  Action,
  // [Description("Event")]
  Event,
  // [Description("Observation")]
  Observation,
  // [Description("Task")]
  Task,
  General;

  public static NoteCategoryType findByIndex(Integer index) {
    if (index != null && index >= 0 && index < NoteCategoryType.values().length) {
      return NoteCategoryType.values()[index];
    }
    return null;
  }
}
