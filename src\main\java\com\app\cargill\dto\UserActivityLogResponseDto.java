/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserActivityLogResponseDto {

    private Long id;
    private String eventName;
    private String path;
    private String username;
    private String userId;
    private String sessionId;
    private String ipAddress;
    private String userAgent;
    private String browser;
    private String operatingSystem;
    private String deviceType;
    private String screenResolution;
    private String referrerUrl;
    private String pageTitle;
    private String actionType;
    private String elementClicked;
    private String formData;
    private Long responseTimeMs;
    private String errorMessage;
    private Integer httpStatusCode;
    private String requestMethod;
    private Long requestSizeBytes;
    private Long responseSizeBytes;
    private Long timeOnPageSeconds;
    private Double scrollDepthPercentage;
    private String country;
    private String region;
    private String city;
    private String timezone;
    private String language;
    private String accountId;
    private String siteId;
    private String visitId;
    private String featureUsed;
    private String moduleName;
    private Instant lastVisited;
    private String additionalData;
    private Instant createdDate;
    private Instant updatedDate;
}
