/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

@SuppressWarnings("java:S115") // Enum naming intentional
public enum MilkPickup {
  NotSet(0),
  // [Description("Daily")]
  Daily(1),
  // [Description("EveryOtherDay")]
  EveryOtherDay(2);

  private final Integer pickupCode;

  MilkPickup(Integer pickupCode) {
    this.pickupCode = pickupCode;
  }

  public Integer getPickupCode() {
    return pickupCode;
  }
}
