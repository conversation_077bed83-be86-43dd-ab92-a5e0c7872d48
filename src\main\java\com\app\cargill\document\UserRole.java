/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.converter.BusinessIdDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class UserRole implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("UserRole")
  public String roleType;

  @JsonProperty("UserName")
  public String userName;

  @JsonProperty("UserBusinessUnit")
  @JsonDeserialize(using = BusinessIdDeserializer.class)
  //  @JsonSerialize(using = BusinessIdSerializer.class)
  public Integer userBusinessUnit;
}
