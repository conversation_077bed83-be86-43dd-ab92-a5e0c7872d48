/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.PileAndBunkerCosmos;
import com.app.cargill.cosmos.model.tools.PileAndBunkerToolCosmos;
import com.app.cargill.document.PileAndBunker;
import com.app.cargill.document.PileAndBunkerTool;
import java.util.UUID;

public class PileAndBunkerToolMapper {

  private PileAndBunkerToolMapper() {}

  public static PileAndBunkerTool map(PileAndBunkerToolCosmos input) {

    CosmosToModelMapper<PileAndBunkerCosmos, PileAndBunker> pileAndBunkerMapper =
        source ->
            PileAndBunker.builder()
                .id(UUID.fromString(source.getId()))
                .name(source.getName())
                .isPileOrBunker(source.getIsPileOrBunker())
                .createTimeUtc(source.getCreateTimeUtc())
                .topWidthInMeters(source.getTopWidthInMeters())
                .bottomWidthInMeters(source.getBottomWidthInMeters())
                .heightInMeters(source.getHeightInMeters())
                .bottomLengthInMeters(source.getBottomLengthInMeters())
                .topLengthInMeters(source.getTopLengthInMeters())
                .dryMatterPercentage(source.getDryMatterPercentage())
                .silageDMDensityInKgPerMetersCubed(source.getSilageDMDensityInKgPerMetersCubed())
                .dryMatterOfFeedPerCowPerDay(source.getDryMatterOfFeedPerCowPerDay())
                .cowsToBeFed(source.getCowsToBeFed())
                .feedOutInclusionRate(source.getFeedOutInclusionRate())
                .tonnesOfDryMatter(source.getTonnesOfDryMatter())
                .tonnesAsFed(source.getTonnesAsFed())
                .footPrintArea(source.getFootPrintArea())
                .tonnesAsFedPerMeterSquaredFootPrintArea(
                    source.getTonnesAsFedPerMeterSquaredFootPrintArea())
                .slope(source.getSlope())
                .silageAsFedDensity(source.getSilageAsFedDensity())
                .feedOutSurfaceAreaMetersSquared(source.getFeedOutSurfaceAreaMetersSquared())
                .cowsPerDayNeededAtLowerFeedRate(source.getCowsPerDayNeededAtLowerFeedRate())
                .cowsPerDayNeededAtHigherFeedRate(source.getCowsPerDayNeededAtHigherFeedRate())
                .kilogramsDryMatterInOneMeter(source.getKilogramsDryMatterInOneMeter())
                .metersPerDay(source.getMetersPerDay())
                .filledHeightInMeters(source.getFilledHeightInMeters())
                .diameterInMeters(source.getDiameterInMeters())
                .silageLeftInMeters(source.getSilageLeftInMeters())
                .dryMatterPercentageSilo(source.getDryMatterPercentageSilo())
                .lengthInMeters(source.getLengthInMeters())
                .diameterBagInMeters(source.getDiameterBagInMeters())
                .dryMatterPercentageBag(source.getDryMatterPercentageBag())
                .silageDMDensityBagKgPerMeter(source.getSilageDMDensityBagKgPerMeter())
                .silageAsFedDensityBag(source.getSilageAsFedDensityBag())
                .startDate(source.getStartDate())
                .build();

    CosmosToModelMapper<PileAndBunkerToolCosmos, PileAndBunkerTool> mapper =
        source ->
            PileAndBunkerTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pileBunkers(
                    source.getPileBunkers() != null
                        ? source.getPileBunkers().stream().map(pileAndBunkerMapper::map).toList()
                        : null)
                .build();

    return mapper.map(input);
  }
}
