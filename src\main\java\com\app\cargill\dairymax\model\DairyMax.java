/* Cargill Inc.(C) 2022 */
package com.app.cargill.dairymax.model;

import com.app.cargill.constants.MilkingSystem;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteVisit;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class DairyMax {

  @JsonProperty("id")
  private UUID maxId;

  @JsonProperty("AccountId")
  private String maxExternalAccountId;

  @JsonProperty("CreateUser")
  private String maxCreateUser;

  @JsonProperty("IsDeleted")
  private boolean maxIsDeleted;

  @JsonProperty("LastModifyUser")
  private String maxLastModifyUser;

  @JsonProperty("CreateTimeUtc")
  private Instant maxCreateTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private Instant maxLastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  private Instant maxLastSyncTimeUtc;

  @JsonProperty("IsNew")
  private boolean maxIsNew;

  @JsonProperty("ExternalAccountId")
  private UUID maxAccountId;

  @JsonProperty("SiteName")
  private String maxSiteName;

  @JsonProperty("Barns")
  private List<MaxBarn> maxBarns;

  @JsonProperty("Visits")
  private List<SiteVisit> maxVisits;

  @JsonProperty("Diets")
  @Builder.Default
  private List<MaxDiet> maxDiets = new ArrayList<>();

  @JsonProperty("SiteMappings")
  private List<DataSourceMapping> maxDataSourceMappings;

  @JsonProperty("Origination")
  private String maxOrigination;
  // Max Changes
  @JsonProperty("CurrentMilkPrice")
  private Double maxCurrentMilkPrice;

  @JsonProperty("MilkingSystemType")
  private MilkingSystem maxMilkingSystemType;
  // UnitInSystem
  @JsonProperty("NumberOfParlorStalls")
  private Integer maxNumberOfParlorStalls;

  @JsonProperty("LactatingAnimal")
  private Integer maxLactatingAnimal;

  @JsonProperty("DaysInMilk")
  private Integer maxDaysInMilk;

  @JsonProperty("MilkYield")
  private Double maxMilk;

  @JsonProperty("MilkFatPercent")
  private Double maxMilkFatPercent;

  @JsonProperty("MilkProteinPercent")
  private Double maxMilkProteinPercent;

  @JsonProperty("MilkOtherSolidsPercent")
  private Double maxMilkOtherSolidsPercent;

  @JsonProperty("MilkSomaticCellCount")
  private Integer maxMilkSomaticCellCount;

  @JsonProperty("BacteriaCellCount")
  private Integer maxBacteriaCellCount;

  @JsonProperty("DryMatterIntake")
  private Double maxDryMatterIntake;

  @JsonProperty("AsFedIntake")
  private Double maxAsFedIntake;

  @JsonProperty("NELDairyDryMatter")
  private Double maxNetEnergyOfLactationDairy;

  @JsonProperty("RationCost")
  private Double maxRationCost;

  public DairyMax(SiteDocument site) {
    this.maxId = site.getId();
    this.maxAsFedIntake = site.getAsFedIntake();
    this.maxAccountId = site.getAccountId();
    this.maxBacteriaCellCount = site.getBacteriaCellCount();
    this.maxCreateTimeUtc = site.getCreateTimeUtc();
    this.maxCurrentMilkPrice = site.getCurrentMilkPrice();
    this.maxDaysInMilk = site.getDaysInMilk();
    this.maxDryMatterIntake = site.getDryMatterIntake();
    this.maxExternalAccountId = site.getExternalAccountId();
    this.maxCreateUser = site.getCreateUser();
    this.maxIsDeleted = site.isDeleted();
    this.maxIsNew = site.isNew();
    this.maxLactatingAnimal = site.getLactatingAnimal();
    this.maxLastModifiedTimeUtc = site.getLastModifiedTimeUtc();
    this.maxLastModifyUser = site.getLastModifyUser();
    this.maxLastSyncTimeUtc = site.getLastSyncTimeUtc();
    this.maxMilk = site.getMilk();
    this.maxMilkFatPercent = site.getMilkFatPercent();
    this.maxMilkingSystemType = site.getMilkingSystemType();
    this.maxMilkProteinPercent = site.getMilkProteinPercent();
    this.maxMilkOtherSolidsPercent = site.getMilkOtherSolidsPercent();
    this.maxMilkSomaticCellCount = site.getMilkSomaticCellCount();
    this.maxNetEnergyOfLactationDairy = site.getNetEnergyOfLactationDairy();
    this.maxNumberOfParlorStalls = site.getNumberOfParlorStalls();
    this.maxOrigination = site.getOrigination();
    this.maxRationCost = site.getRationCost();
    this.maxDataSourceMappings = site.getDataSourceMappings();
    this.maxSiteName = site.getSiteName();
    this.maxVisits = site.getVisits();
  }
}
