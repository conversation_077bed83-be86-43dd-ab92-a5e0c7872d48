/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SubTypeIdDeserializer extends JsonDeserializer<Integer> {

  @Override
  public Integer deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    try {
      return Integer.parseInt(node.asText());
    } catch (IllegalArgumentException e) {
      if (!"Farm Producer".equals(node.asText())) {
        log.warn(
            "Unknown SubTypeID value: {}. Falling back to default: \"Farm Producer\"",
            node.asText());
      }
      return 8;
    }
  }
}
