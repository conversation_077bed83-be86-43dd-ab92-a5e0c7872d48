/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.FeedStorageType;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PileAndBunker implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Name")
  public String name;

  @JsonProperty("IsPileOrBunker")
  public FeedStorageType isPileOrBunker;

  @JsonProperty("CreateTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant createTimeUtc;

  @JsonProperty("Id")
  public UUID id;

  @JsonProperty("TopWidthInMeters")
  public Double topWidthInMeters;

  @JsonProperty("BottomWidthInMeters")
  public Double bottomWidthInMeters;

  @JsonProperty("HeightInMeters")
  public Double heightInMeters;

  @JsonProperty("BottomLengthInMeters")
  public Double bottomLengthInMeters;

  @JsonProperty("TopLengthInMeters")
  public Double topLengthInMeters;

  @JsonProperty("DryMatterPercentage")
  public Double dryMatterPercentage;

  @JsonProperty("SilageDMDensityInKgPerMetersCubed")
  public Double silageDMDensityInKgPerMetersCubed;

  @JsonProperty("DryMatterOfFeedPerCowPerDay")
  public Integer dryMatterOfFeedPerCowPerDay;

  @JsonProperty("CowsToBeFed")
  public Integer cowsToBeFed;

  @JsonProperty("FeedOutInclusionRate")
  public Double feedOutInclusionRate;

  @JsonProperty("TonnesOfDryMatter")
  public Double tonnesOfDryMatter;

  @JsonProperty("TonnesAsFed")
  public Double tonnesAsFed;

  @JsonProperty("FootPrintArea")
  public Double footPrintArea;

  @JsonProperty("TonnesAsFedPerMeterSquaredFootPrintArea")
  public Double tonnesAsFedPerMeterSquaredFootPrintArea;

  @JsonProperty("Slope")
  public Double slope;

  @JsonProperty("SilageAsFedDensity")
  public Integer silageAsFedDensity;

  @JsonProperty("FeedOutSurfaceAreaMetersSquared")
  public Double feedOutSurfaceAreaMetersSquared;

  @JsonProperty("CowsPerDayNeededAtLowerFeedRate")
  public Integer cowsPerDayNeededAtLowerFeedRate;

  @JsonProperty("CowsPerDayNeededAtHigherFeedRate")
  public Integer cowsPerDayNeededAtHigherFeedRate;

  @JsonProperty("KilogramsDryMatterInOneMeter")
  public Double kilogramsDryMatterInOneMeter;

  @JsonProperty("MetersPerDay")
  public Double metersPerDay;
  // LoadingUnloadingSiloBag Changes
  @JsonProperty("FilledHeightInMeters")
  public Double filledHeightInMeters;

  @JsonProperty("DiameterInMeters")
  public Double diameterInMeters;

  @JsonProperty("SilageLeftInMeters")
  public Double silageLeftInMeters;

  @JsonProperty("DryMatterPercentageSilo")
  public Double dryMatterPercentageSilo;

  @JsonProperty("LengthInMeters")
  public Double lengthInMeters;

  @JsonProperty("DiameterBagInMeters")
  public Double diameterBagInMeters;

  @JsonProperty("DryMatterPercentageBag")
  public Double dryMatterPercentageBag;

  @JsonProperty("SilageDMDensityBagKgPerMeter")
  public Double silageDMDensityBagKgPerMeter;

  @JsonProperty("SilageAsFedDensityBag")
  public Double silageAsFedDensityBag;

  @JsonProperty("StartDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant startDate;

  @JsonProperty("DateGone")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant dateGone;

  @JsonProperty("TonnesPerDay")
  public Double tonnesPerDay;

  @JsonProperty("CentimetersPerDay")
  public Double centimetersPerDay;

  @JsonProperty("TonnesDMSilo")
  public Double tonnesDMSilo;

  @JsonProperty("TonnesAFSilo")
  public Double tonnesAFSilo;

  @JsonProperty("TonnesDMBag")
  public Double tonnesDMBag;

  @JsonProperty("TonnesAFBag")
  public Double tonnesAFBag;
}
