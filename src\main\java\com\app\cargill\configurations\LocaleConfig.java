/* Cargill Inc.(C) 2022 */
package com.app.cargill.configurations;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Locale;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;

@Configuration
public class LocaleConfig {

  @Bean
  public AcceptHeaderLocaleResolver localeResolver() {
    final AcceptHeaderLocaleResolver resolver = new AcceptHeaderLocaleResolver();
    resolver.setDefaultLocale(Locale.US);
    return resolver;
  }

  @Bean
  public ResourceBundleMessageSource messageSource() {
    final ResourceBundleMessageSource source = new ResourceBundleMessageSource();
    source.setBasename("internationalization/messages");
    source.setDefaultEncoding(StandardCharsets.UTF_8.name());
    Locale.setDefault(Locale.US);
    return source;
  }

  @Bean
  public LocaleChangeInterceptor localeChangeInterceptor() {
    LocaleChangeInterceptor lci = new LocaleChangeInterceptor();
    lci.setParamName("lang");
    return lci;
  }

  @Bean
  public RestTemplate restTemplate(RestTemplateBuilder builder) {
    int connectTimeout = 120000;
    HttpComponentsClientHttpRequestFactory requestFactory =
        new HttpComponentsClientHttpRequestFactory();
    RestTemplate restTemplate =
        builder
            .setConnectTimeout(Duration.of(connectTimeout, ChronoUnit.MILLIS))
            .setReadTimeout(Duration.of(connectTimeout, ChronoUnit.MILLIS))
            .build();
    restTemplate.setRequestFactory(requestFactory);
    return restTemplate;
  }
}
