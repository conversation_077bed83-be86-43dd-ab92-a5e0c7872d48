/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller.admin;

import com.app.cargill.model.AccountRoles;
import com.app.cargill.service.forecast.AccountRolesService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/account")
@Tag(name = "Accounts management controller")
@RequiredArgsConstructor
public class AccountAdminController {

  private final AccountRolesService accountRolesService;

  @GetMapping("/user-roles")
  //  @PreAuthorize(("authentication.name == @securityConfiguration.getApiAdminId()"))
  public List<AccountRoles> getAllUsers() {
    return accountRolesService.getAllAccountsRoles();
  }
}
