/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.WalkThroughQualityType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.MappedSuperclass;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class WalkThroughReport extends EditableDocumentBase {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("Quality")
  public WalkThroughQualityType quality;

  @JsonProperty("PenId")
  public UUID penId;

  @JsonProperty("PenName")
  public String penName;

  @JsonProperty("IsToolItemNew")
  public Boolean isToolItemNew;

  @JsonProperty("WaterQuality")
  public WalkThroughQualityType waterQuality;

  @JsonProperty("BeddingQuality")
  public WalkThroughQualityType beddingQuality;

  @JsonProperty("HockAbrasion")
  public Double hockAbrasion;

  @JsonProperty("HockAbrasionGoal")
  public Double hockAbrasionGoal;

  @JsonProperty("UterineDischarge")
  public Double uterineDischarge;

  @JsonProperty("UterineDischargeGoal")
  public Double uterineDischargeGoal;

  @JsonProperty("NasalDischarge")
  public Double nasalDischarge;

  @JsonProperty("NasalDischargeGoal")
  public Double nasalDischargeGoal;

  @JsonProperty("CowComfort")
  public Double cowComfort;

  @JsonProperty("CowComfortGoal")
  public Double cowComfortGoal;

  @JsonProperty("ManureScoreComment")
  public String manureScoreComment;

  @JsonProperty("Appearance")
  public String appearance;

  @JsonProperty("RumenFill")
  public String rumenFill;

  @JsonProperty("BeddingDepth")
  public String beddingDepth;

  @JsonProperty("PositiveTrends")
  public String positiveTrends;

  @JsonProperty("Opportunities")
  public String opportunities;

  @JsonProperty("Comments")
  public String comments;

  @JsonProperty("RuminationChewing")
  public Double ruminationChewing;

  @JsonProperty("RuminationChewingGoal")
  public Double ruminationChewingGoal;

  @JsonProperty("ChewsPerCud")
  public Double chewsPerCud;

  @JsonProperty("ChewsPerCudGoal")
  public Double chewsPerCudGoal;

  @JsonProperty("ManureScore")
  public Double manureScore;

  @JsonProperty("ManureScoreGoal")
  public Double manureScoreGoal;

  @JsonProperty("LocomotionScore")
  public Double locomotionScore;

  @JsonProperty("LocomotionScoreGoal")
  public Double locomotionScoreGoal;

  @JsonProperty("BodyConditionScore")
  public Double bodyConditionScore;

  @JsonProperty("BodyConditionScoreGoal")
  public Double bodyConditionScoreGoal;
}
