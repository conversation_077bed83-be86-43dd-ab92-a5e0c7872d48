/* Cargill Inc.(C) 2022 */
package com.app.cargill.authconfig;

import com.app.cargill.constants.AuthenticationPlatform;
import org.springframework.security.core.GrantedAuthority;

public final class CustomGrantedAuthority implements GrantedAuthority {
  private static final long serialVersionUID = 570L;
  private final AuthenticationPlatform authenticationPlatform;

  public CustomGrantedAuthority(AuthenticationPlatform authenticationPlatform) {
    this.authenticationPlatform = authenticationPlatform;
  }

  public String getAuthority() {
    return this.authenticationPlatform.name();
  }
}
