@font-face {
    font-family: 'Helvetica';
    src: url('../fonts/Helvetica.eot');
    src: url('../fonts/Helvetica.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Helvetica.woff2') format('woff2'),
        url('../fonts/Helvetica.woff') format('woff'),
        url('../fonts/Helvetica.ttf') format('truetype'),
        url('../fonts/Helvetica.svg#Helvetica') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'HelveticaNeueMedium';
    src: url('../fonts/HelveticaNeueMedium.eot');
    src: url('../fonts/HelveticaNeueMedium.eot?#iefix') format('embedded-opentype'),
        url('../fonts/HelveticaNeueMedium.woff2') format('woff2'),
        url('../fonts/HelveticaNeueMedium.woff') format('woff'),
        url('../fonts/HelveticaNeueMedium.ttf') format('truetype'),
        url('../fonts/HelveticaNeueMedium.svg#HelveticaNeueMedium') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}


body{
    font-family: 'Helvetica', sans-serif;
}

.container{
    padding: 0 30px;
}

.row{
    display: flex;
    margin-left: -15px;
    margin-right: -15px;
}

body, figure, p{
    margin: 0;
}

/* Utlities */

.pt-5{
    padding-top: 50px !important;
}
.mb-5{
    margin-bottom: 50px !important;
}
.mb-4{
    margin-bottom: 40px !important;
}
.mb-3{
    margin-bottom: 30px !important;
}

.mb-2{
    margin-bottom: 20px !important;
}

.mb-1{
    margin-bottom: 10px !important;
}

.mx-5{
    margin: 0 50px;
}

.mx-4{
    margin: 0 40px;
}

.mx-3{
    margin: 0 30px;
}

.mx-2{
    margin: 0 20px;
}

.d-flex{
    display: flex;
}

.flex-wrap{
    flex-wrap: wrap;
}

.flex-nowrap{
    flex-wrap: nowrap;
}

.justify-space-between{
    justify-content: space-between;
}

.template-header{
    text-align: center;
    padding: 25px 0 25px 0;
}

.card{
    background: #fff;
    box-shadow: 0px 0px 9.29167px rgba(0, 0, 0, 0.13);
    border-radius: 4px;
}

.card-header{
    padding: 20px;
    background: #F8F8F8;
}

.card-header h4{
    margin: 0;
    font-size: 16px;
    color: #323F4B;
    text-transform: capitalize;
    font-family: 'HelveticaNeueMedium', sans-serif;
    font-weight: normal;
}

.card-body{
    padding: 20px;
    background: #fff;
}


.content-set{
    padding: 0 15px;
}

.content-set label{
    color: #6C7782;
    font-size: 14px;
    margin-bottom: 7px;
    display: inline-block;
}

.content-set h4{
    font-size: 14px;
}

.attribute-name{
    font-size: 12px;
    color: #6C7782;
}

.attribute-name span{
    font-family: 'HelveticaNeueMedium', sans-serif;
}

.single-line-data{
    margin: 0 0 20px;
    font-weight: 400;
}

.single-line-data-royalblue-text-color{
    margin: 0 0 20px;
    color: ROYALBLUE;
}

.single-line-data span{
    font-weight: 600;
}

.legend-wrap{
    padding: 0 15px;
}

.legend-wrap p{
    font-size: 12px;
    font-family: 'HelveticaNeueMedium', sans-serif;
    letter-spacing: 0.3px;
    color: #323F4B;
    position: relative;
    display: flex;
    align-items: center;
    padding-left: 16px;
    text-transform: none;
}

.legend-wrap .green-solid::before{
    content: '';
    background: #1BACA7;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .dynamic-solid-left::before{
    content: '';
    background: var(--background);
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .dynamic-solid-right::before{
    content: '';
    background: var(--background);
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .purple-solid::before{
    content: '';
    background: #A160E6;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .blue-solid::before{
    content: '';
    background: #307698;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .blue-light-solid::before{
    content: '';
    background: #61ADDE;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}


.legend-wrap .purple-strip::before{
    content: '';
    background: #fff;
    border: 1px dashed #A160E6;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 10px;
    position: absolute;
    left: 0;
}

.legend-wrap .orange-strip::before{
    content: '';
    background: #fff;
    border: 1px dashed #DA9E44;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 10px;
    position: absolute;
    left: 0;
}

.legend-wrap .orange-solid::before{
    content: '';
    background: #FFA500;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 10px;
    position: absolute;
    left: 0;
}

.legend-wrap .copper-solid::before{
    content: '';
    background: #D98773;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 10px;
    position: absolute;
    left: 0;
}

.legend-wrap .red-strip::before{
    content: '';
    background: #fff;
    border: 1px dashed #8D0909;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 10px;
    position: absolute;
    left: 0;
}

.legend-wrap .lagoon-solid::before{
    content: '';
    background: #67B7DC;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .voilet-solid::before{
    content: '';
    background: #6794DC;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .voilet-2-solid::before{
    content: '';
    background: #8067DC;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .voilet-3-solid::before{
    content: '';
    background: #A367DC;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .pink-solid::before{
    content: '';
    background: #DC67CE;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .red-2-solid::before{
    content: '';
    background: #DC6967;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .gold-solid::before{
    content: '';
    background: #DCAF67;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .middle-blue-solid::before{
    content: '';
    background: #7AD8DC;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .middle-blue-solid-light::before{
    content: '';
    background: #7AD8DC4D;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}
.legend-wrap .middle-blue-solid-dark::before{
    content: '';
    background: #60B5B9;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .jordy-blue-solid::before{
    content: '';
    background: #83BEF4;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}
.legend-wrap .jordy-blue-solid-light::before{
    content: '';
    background: #83BEF44D;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}
.legend-wrap .jordy-blue-solid-dark::before{
    content: '';
    background: #4D88BE;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .blue-purple-solid::before{
    content: '';
    background: #ABA1E3;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}
.legend-wrap .blue-purple-solid-light::before{
    content: '';
    background: #9C90DE4D;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}
.legend-wrap .blue-purple-solid-dark::before{
    content: '';
    background: #7A6EBE;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .tulip-solid::before{
    content: '';
    background: #F18494;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

/* Start Heat Stress Evaluation Screen */

.evaluation-wrapper {
    background-color: rgba(248, 248, 248, 1);
    width: 100%;
    max-width: 652px;
    margin: 0 auto;
    padding-bottom: 50px;
}
.main-header {
    padding: 25px 20px;
    background: #F8F8F8;
}
.main-header h3 {
    margin-top: 0;
    margin-bottom: 25px;
}

.main-header-row {
    display: flex;
}
.main-header-row p {
    color: #6C7782;
    margin-bottom: 8px;
}
.main-header-row > div {
    flex: 1;
}
.evaluation-wrapper .main {
    max-width: 80%;
    margin: 0 auto;
    box-shadow: 0px 0px 9.29167px rgba(0, 0, 0, 0.13);
    border-radius: 10px;
    overflow: hidden;
}
.evaluation-wrapper .logo {
    display: block;
    text-align: center;
    margin-bottom: 21px;
    padding-top: 30px;
}
.main-body {
    background: #FFFFFF;
    padding: 20px;
}
.cards-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 15px;
}
.card-col {
    background: #FFFFFF;
    border: 1px solid #E3E7EB;
    border-radius: 8px;
    padding: 15px;
}
.card-col p {
    margin-bottom: 20px;
}
.card-col strong {
    font-weight: 600;
}
.fullwidth-card-row {
    display: flex;
    align-items: center;
    background: #FFFFFF;
    border: 1px solid #E3E7EB;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 15px;
}
.fullwidth-card-content {
    padding-left: 20px;
}
.fullwidth-card-row h4 {
    margin-top: 0;
    margin-bottom: 5px;
}
.fullwidth-card-row h5 {
    font-size: 18px;
    margin-bottom: 3px;
    margin-top: 5px;
}
.fullwidth-card-row p,
.card-col p {
    font-size: 12px;
}
.fullwidth-card-row span,
.main-header-row *,
.custom-table thead tr:nth-child(1) th {
    font-size: 14px;
}
.fullwidth-card-row h4,
.legend-title,
.card-col strong,
.main-header h3 {
    font-size: 16px;
}

.main-header h3,
.main-header-row *,
.card-col p,
.card-col strong,
.fullwidth-card-row h4,
.fullwidth-card-row p,
.fullwidth-card-row span {
    color: #323F4B;
}

.legend-title {
    color: #0B5E86;
}

/* Start Custom Table */
.custom-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    margin-top: 20px;
    margin-bottom: 30px;
}
.custom-table * {
    color: #fff;
}
.custom-table td,
.custom-table th {
    vertical-align: middle;
    text-align: center;
    padding: 9px;
    font-weight: 500;
}
.custom-table th {
    font-size: 17px;
}
.custom-table td {
    font-size: 15.58px;
}

.navy-blue {
    background: #0F4C6A;
}
.blue {
    background: #0B5E86;
}
.sky-blue {
    background: #459ED8;
}
.green {
    background: #6C8A32;
}
.yellow {
    background: #D3AA39;
}
.orange {
    background: #B4592C;
}
.red {
    background: #9A3532;
}

.legend-wrap .tropic-wave-cyan::before {
    content: '';
    background: #7AD8DC;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .sky-breeze-blue::before {
    content: '';
    background: #83BEF4;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}

.legend-wrap .lavender-mist::before {
    content: '';
    background: #ABA1E3;
    display: inline-block;
    border-radius: 100%;
    height: 10px;
    width: 11px;
    position: absolute;
    left: 0;
}


/* End Custom Table */


