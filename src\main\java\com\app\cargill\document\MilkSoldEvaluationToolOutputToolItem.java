/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkSoldEvaluationToolOutputToolItem implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("EvaluationDays")
  public Double evaluationDays;

  @JsonProperty("AverageMilkProduction")
  public Double averageMilkProduction;

  @JsonProperty("AverageMilkProductionAnimalsTank")
  public Double averageMilkProductionAnimalsTank;

  @JsonProperty("AverageMilkFatPer")
  public Double averageMilkFatPer;

  @JsonProperty("MilkFatYield")
  public Double milkFatYield;

  @JsonProperty("AverageMilkProteinPer")
  public Double averageMilkProteinPer;

  @JsonProperty("MilkProteinYield")
  public Double milkProteinYield;

  @JsonProperty("MilkFatProteinYield")
  public Double milkFatProteinYield;

  @JsonProperty("ComponentEfficiency")
  public Double componentEfficiency;

  @JsonProperty("FeedEfficiency")
  public Double feedEfficiency;

  @JsonProperty("MUN")
  public Double mun;

  @JsonProperty("AverageSCC")
  public Double averageSCC;

  @JsonProperty("AverageBacteriaCount")
  public Double averageBacteriaCount;

  @JsonProperty("IsOutputUpdated")
  @Builder.Default
  public Boolean isOutputUpdated = false;

  @JsonProperty("MilkSolidNonFat")
  public Double milkSolidNonFat;

  @JsonProperty("AverageMilkSolidNonFat")
  public Double averageMilkSolidNonFat;

  @JsonProperty("ToolStatus")
  public ToolStatuses toolStatus;
}
