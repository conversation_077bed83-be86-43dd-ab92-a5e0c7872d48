/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static com.app.cargill.service.impl.DDWReportServiceImpl.MAX_RETRY_ATTEMPTS_DURATION;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.app.cargill.crescendo.config.CrescendoConfig;
import com.app.cargill.exceptions.ExceptionInstanceValidator;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.service.SalesforceClientFactory;
import java.time.Duration;
import java.util.Comparator;
import java.util.function.Consumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.math.MathFlux;
import reactor.util.retry.Retry;

@RequiredArgsConstructor
@Service
@Slf4j
public class CrescendoApiReactiveService {

  private static final String API_VERSION_LOCATION = "/services/data/";
  private static final Integer MAX_RETRY_ATTEMPTS = 3;
  protected static final String GRANT_TYPE = "grant_type";
  protected static final String CLOSE = "close";
  private final CrescendoConfig crescendoConfig;
  private final SalesforceClientFactory clientFactory;

  public Mono<AuthToken> getToken() {
    log.info(
        "{} {} {} {}",
        crescendoConfig.getScheme(),
        crescendoConfig.getTokenHost(),
        crescendoConfig.getTokenPath(),
        crescendoConfig.getPort());
    return clientFactory
        .createClient()
        .post()
        .uri(
            uriBuilder ->
                uriBuilder
                    .scheme(crescendoConfig.getScheme())
                    .host(crescendoConfig.getTokenHost())
                    .path(crescendoConfig.getTokenPath())
                    .port(crescendoConfig.getPort())
                    .build())
        .header(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON_VALUE)
        .header(HttpHeaders.CONNECTION, CLOSE)
        .header(HttpHeaders.AUTHORIZATION, crescendoConfig.getTokenAuthHeader())
        .body(BodyInserters.fromFormData(GRANT_TYPE, crescendoConfig.getGrantType()))
        .exchangeToMono(
            clientResponse -> {
              if (clientResponse.statusCode().isError()) {
                return clientResponse.createException().flatMap(Mono::error);
              } else {
                return clientResponse.bodyToMono(AuthToken.class);
              }
            })
        .retryWhen(
            Retry.fixedDelay(MAX_RETRY_ATTEMPTS, Duration.ofSeconds(MAX_RETRY_ATTEMPTS_DURATION))
                .filter(ExceptionInstanceValidator::isUnknownHostExceptionError));
  }

  public Mono<VersionObject> getLatestApiVersion(AuthToken authToken) {
    return MathFlux.max(
        getApiVersions(authToken),
        Comparator.comparingDouble(o -> Double.parseDouble(o.getVersion())));
  }

  public Flux<VersionObject> getApiVersions(AuthToken authToken) {
    return clientFactory
        .createClient(authToken.getInstanceUrl())
        .get()
        .uri(uriBuilder -> uriBuilder.path(API_VERSION_LOCATION).build())
        .headers(defaultHeaders(authToken))
        .retrieve()
        .bodyToFlux(VersionObject.class)
        .retryWhen(
            Retry.fixedDelay(MAX_RETRY_ATTEMPTS, Duration.ofSeconds(MAX_RETRY_ATTEMPTS_DURATION)));
  }

  private Consumer<HttpHeaders> defaultHeaders(AuthToken authToken) {
    return httpHeaders -> {
      httpHeaders.add(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON_VALUE);
      httpHeaders.add(
          HttpHeaders.AUTHORIZATION, String.format("Bearer %s", authToken.getAccessToken()));
      httpHeaders.add(HttpHeaders.CONNECTION, CLOSE);
      httpHeaders.add(HttpHeaders.ACCEPT, APPLICATION_JSON_VALUE);
    };
  }
}
