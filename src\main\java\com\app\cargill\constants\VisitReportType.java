/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import java.util.Objects;

@SuppressWarnings("java:S115") // Constants Naming
public enum VisitReportType {
  SiteVisit(0),
  Walk<PERSON>hrough(1),
  Tool(2);

  private final Integer value;

  VisitReportType(Integer value) {
    this.value = value;
  }

  public Integer getValue() {
    return value;
  }

  public static VisitReportType fromId(Integer id) {
    for (VisitReportType type : values()) {
      if (Objects.equals(type.getValue(), id)) {
        return type;
      }
    }
    return null;
  }
}
