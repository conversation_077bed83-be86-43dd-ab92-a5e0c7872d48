/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class ManureScreenerToolCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("MSTScores")
  private List<ManureScreenerToolItemCosmos> mstScores;

  @JsonProperty("MSTGoal")
  private ManureScreenerScoreGoalToolItemCosmos mstGoal;

  @JsonProperty("Observation")
  private String observation;
}
