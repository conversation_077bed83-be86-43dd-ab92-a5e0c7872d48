/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller.external;

import com.app.cargill.ddw.model.HerdData;
import com.app.cargill.ddw.service.IHerdService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ddw")
@Tag(
    name = "DDW(Dairy Data Warehouse) Controller",
    description = "Endpoint for communication with DDW service")
@RequiredArgsConstructor
@Slf4j
public class DDWController {

  private final IHerdService herdService;

  @PostMapping
  @Operation(
      summary = "Update site, pens and diet data from DDW ",
      description =
          "This api will update the site ,pens and diets data " + "as received from DDW service.")
  public ResponseEntity<List<String>> post(@RequestBody List<HerdData> herds) {
    log.debug("in controller");
    return ResponseEntity.ok().body(herdService.updateHerds(herds));
  }
}
