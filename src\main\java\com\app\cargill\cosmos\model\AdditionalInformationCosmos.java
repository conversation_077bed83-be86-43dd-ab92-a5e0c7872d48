/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class AdditionalInformationCosmos {
  @JsonProperty("ID")
  private String id;

  @JsonProperty("AccountID")
  private String accountId;

  @JsonProperty("PaymentTerms")
  private String paymentTerms;

  @JsonProperty("VAT")
  private String vAT;

  @JsonProperty("DUNS")
  private String duns;

  @JsonProperty("CANBusinessID")
  private Integer cANBusinessID;

  @JsonProperty("StatisticalID")
  private String statisticalID;

  @JsonProperty("CustomerServiceNotes")
  private String customerServiceNotes;

  @JsonProperty("VATFarmer")
  private Boolean vATFarmer;
}
