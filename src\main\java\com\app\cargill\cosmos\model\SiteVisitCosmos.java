/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.VisitStatus;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import lombok.Getter;

@Getter
public class SiteVisitCosmos {
  @JsonProperty("LabyrinthVisitId")
  private String labyrinthVisitId;

  @JsonProperty("VisitDate")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant visitDate;

  @JsonProperty("Status")
  private VisitStatus status;

  @JsonProperty("VisitName")
  private String visitName;
}
