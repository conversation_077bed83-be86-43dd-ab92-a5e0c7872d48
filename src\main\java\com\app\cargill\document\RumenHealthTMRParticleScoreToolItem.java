/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class RumenHealthTMRParticleScoreToolItem extends EditableToolPenEntityBase
    implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("VisitsSelected")
  private List<UUID> visitsSelected;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("TopScaleAmountInGrams")
  private Double topScaleAmountInGrams;

  @JsonProperty("TopGoalMinimumPercent")
  private Double topGoalMinimumPercent;

  @JsonProperty("TopGoalMaximumPercent")
  private Double topGoalMaximumPercent;

  @JsonProperty("TopScreenTareAmountInGrams")
  private Double topScreenTareAmountInGrams;

  @JsonProperty("Mid1ScaleAmountInGrams")
  private Double mid1ScaleAmountInGrams;

  @JsonProperty("Mid1GoalMinimumPercent")
  private Double mid1GoalMinimumPercent;

  @JsonProperty("Mid1GoalMaximumPercent")
  private Double mid1GoalMaximumPercent;

  @JsonProperty("Mid1ScreenTareAmountInGrams")
  private Double mid1ScreenTareAmountInGrams;

  @JsonProperty("Mid2ScaleAmountInGrams")
  private Double mid2ScaleAmountInGrams;

  @JsonProperty("Mid2GoalMinimumPercent")
  private Double mid2GoalMinimumPercent;

  @JsonProperty("Mid2GoalMaximumPercent")
  private Double mid2GoalMaximumPercent;

  @JsonProperty("Mid2ScreenTareAmountInGrams")
  private Double mid2ScreenTareAmountInGrams;

  @JsonProperty("TrayScaleAmountInGrams")
  private Double trayScaleAmountInGrams;

  @JsonProperty("TrayGoalMinimumPercent")
  private Double trayGoalMinimumPercent;

  @JsonProperty("TrayGoalMaximumPercent")
  private Double trayGoalMaximumPercent;

  @JsonProperty("TrayScreenTareAmountInGrams")
  private Double trayScreenTareAmountInGrams;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;
  // added below for mr8
  @JsonProperty("TMRScoreName")
  private String tmrScoreName;

  @JsonProperty("TMRScoreIndex")
  private String tmrScoreIndex;

  @JsonProperty("TMRScoreId")
  private String tmrScoreId;

  @JsonProperty("TotalScaleAmount")
  private Double totalScaleAmount;

  @JsonProperty("TopScalePercentage")
  private Double topScalePercentage;

  @JsonProperty("Mid1ScalePercentage")
  private Double mid1ScalePercentage;

  @JsonProperty("Mid2ScalePercentage")
  private Double mid2ScalePercentage;

  @JsonProperty("TrayScalePercentage")
  private Double trayScalePercentage;

  @JsonProperty("TopStdDevValue")
  private Double topStdDevValue;

  @JsonProperty("Mid1StdDevValue")
  private Double mid1StdDevValue;

  @JsonProperty("Mid2StdDevValue")
  private Double mid2StdDevValue;

  @JsonProperty("TrayStdDevValue")
  private Double trayStdDevValue;
}
