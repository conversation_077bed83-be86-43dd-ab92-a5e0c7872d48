/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.DietCosmos;
import com.azure.spring.data.cosmos.repository.Query;
import com.azure.spring.data.cosmos.repository.ReactiveCosmosRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface DietsCosmosRepository extends ReactiveCosmosRepository<DietCosmos, String> {
  @Query("Select * from d")
  Flux<DietCosmos> findAllDiets();

  @Query("Select * from c where c.SiteId = @siteId")
  Flux<DietCosmos> findAllBySiteId(@Param("siteId") String siteId);
}
