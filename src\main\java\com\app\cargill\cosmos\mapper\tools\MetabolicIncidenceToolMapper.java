/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.MetabolicIncidenceToolCosmos;
import com.app.cargill.cosmos.model.tools.MetabolicIncidenceToolItemCosmos;
import com.app.cargill.document.MetabolicIncidenceTool;
import com.app.cargill.document.MetabolicIncidenceToolItem;
import java.util.UUID;

public class MetabolicIncidenceToolMapper {

  private MetabolicIncidenceToolMapper() {}

  public static MetabolicIncidenceTool map(MetabolicIncidenceToolCosmos input) {

    CosmosToModelMapper<MetabolicIncidenceToolItemCosmos, MetabolicIncidenceToolItem> itemMapper =
        source ->
            MetabolicIncidenceToolItem.builder()
                .selectedVisits(
                    source.getSelectedVisits() != null
                        ? source.getSelectedVisits().stream().map(UUID::fromString).toList()
                        : null)
                .outputs(source.getOutputs())
                .totalFreshCowsPerYear(source.getTotalFreshCowsPerYear())
                .replacementCowCost(source.getReplacementCowCost())
                .costOfExtraDaysOpen(source.getCostOfExtraDaysOpen())
                .totalFreshCowsForEvaluation(source.getTotalFreshCowsForEvaluation())
                .retainedPlacentaIncidence(source.getRetainedPlacentaIncidence())
                .metritisIncidence(source.getMetritisIncidence())
                .displacedAbomasumCost(source.getDisplacedAbomasumCost())
                .ketosisIncidence(source.getKetosisIncidence())
                .milkFeverIncidence(source.getMilkFeverIncidence())
                .dystociaIncidence(source.getDystociaIncidence())
                .deathLossIncidence(source.getDeathLossIncidence())
                .retainedPlacentaWeight(source.getRetainedPlacentaWeight())
                .retainedPlacentaDaysOpen(source.getRetainedPlacentaDaysOpen())
                .retainedPlacentaCost(source.getRetainedPlacentaCost())
                .metritisWeight(source.getMetritisWeight())
                .metritisDaysOpen(source.getMetritisDaysOpen())
                .metritisCost(source.getMetritisCost())
                .displacedAbomasumWeight(source.getDisplacedAbomasumWeight())
                .displacedAbomasumDaysOpen(source.getDisplacedAbomasumDaysOpen())
                .displacedAbomasumCost(source.getDisplacedAbomasumCost())
                .ketosisWeight(source.getKetosisWeight())
                .ketosisDaysOpen(source.getKetosisDaysOpen())
                .ketosisDaysOpen(source.getKetosisDaysOpen())
                .ketosisCost(source.getKetosisCost())
                .milkFeverWeight(source.getMilkFeverWeight())
                .milkFeverDaysOpen(source.getMilkFeverDaysOpen())
                .milkFeverCost(source.getMilkFeverCost())
                .dystociaWeight(source.getDystociaWeight())
                .dystociaOpen(source.getDystociaOpen())
                .dystociaCost(source.getDystociaCost())
                .deathLossWeight(source.getDeathLossWeight())
                .deathLossOpen(source.getDeathLossOpen())
                .deathLossCost(source.getDeathLossCost())
                .milkPrice(source.getMilkPrice())
                .build();

    CosmosToModelMapper<MetabolicIncidenceToolCosmos, MetabolicIncidenceTool> mapper =
        source ->
            MetabolicIncidenceTool.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .visitMetabolicIncidenceData(
                    source.getVisitMetabolicIncidenceData() != null
                        ? itemMapper.map(source.getVisitMetabolicIncidenceData())
                        : null)
                .build();

    return mapper.map(input);
  }
}
