/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.SiteDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.ISiteService;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/site")
@Tag(name = "Site Information Controller", description = "Endpoint for handling the Site objects")
@RequiredArgsConstructor
@Slf4j
public class SiteController extends BaseController {

  private final ISiteService siteServiceImpl;
  private final ResourceBundleMessageSource resourceBundleMessageSource;

  @Value("${app.configurations.default-utc-timestamp}")
  private Instant defaultTime;

  @GetMapping("/paginated")
  @Operation(
      summary = "get all sites paginated by current logged in user",
      description = "This api will return all sites paginated by current logged in user")
  public ResponseEntity<ResponseEntityDto<Page<SiteDto>>> getAllSites(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(
              defaultValue = "${app.configurations.default-page-sorting}",
              name = "sorting",
              required = true)
          String sorting) {

    return handleSuccessResponse(
        siteServiceImpl.getAllSitesByCurrentLoggedInUser(
            page, size, sortBy, lastSyncTime, sorting));
  }

  @GetMapping("/filteredSites")
  @Operation(
      summary = "get all site ids which are accessible for the user",
      description = "This api will return all site ids which are accessible for the user")
  public ResponseEntity<ResponseEntityDto<List<String>>> getFilteredSiteIds() {

    return handleSuccessResponse(siteServiceImpl.getFilteredSiteIds());
  }

  @GetMapping("/byAccountId/paginated")
  @Operation(
      summary = "get all sites paginated by account Id",
      description = "This api will return all sites paginated by account Id")
  public ResponseEntity<ResponseEntityDto<Page<SiteDto>>> getAllSitesByAccountId(
      @RequestParam(defaultValue = "accountId", name = "accountId") String accountId,
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(
              defaultValue = "${app.configurations.default-page-sorting}",
              name = "sorting",
              required = true)
          String sorting) {

    return handleSuccessResponse(
        siteServiceImpl.getAllSitesByAccountId(
            accountId, page, size, sortBy, lastSyncTime, sorting));
  }

  @PostMapping
  @Operation(summary = "Save a Site", description = "This api will save the Site")
  public ResponseEntity<ResponseEntityDto<Object>> save(
      @Valid @RequestBody SiteDto siteDto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {
    try {
      Locale locale = Locale.forLanguageTag(localeString);
      LocaleContextHolder.setLocale(locale);
      return handleSuccessResponse(
          siteServiceImpl.save(siteDto, locale, resourceBundleMessageSource));
    } catch (CustomDEExceptions e) {
      log.error("Error in site create", e);
      return handleResponse(
          "Error: Something happened.",
          e.getLocalizedMessage(),
          ResponseStatus.FAILED,
          HttpStatus.NOT_ACCEPTABLE);
    }
  }

  @PutMapping
  @Operation(
      summary = "Update a Site",
      description = "This api will update an existing Site object")
  public ResponseEntity<ResponseEntityDto<Object>> update(
      @Valid @RequestBody SiteDto siteDto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws JsonProcessingException, NotFoundDEException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    try {
      Locale locale = Locale.forLanguageTag(localeString);
      LocaleContextHolder.setLocale(locale);
      return handleSuccessResponse(
          siteServiceImpl.update(siteDto, locale, resourceBundleMessageSource));
    } catch (CustomDEExceptions e) {
      log.error("Error in site update", e);
      return handleResponse(
          "Error: Something happened.",
          e.getLocalizedMessage(),
          ResponseStatus.FAILED,
          HttpStatus.NOT_ACCEPTABLE);
    }
  }
}
