/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.dto.VisitReportDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.service.IVisitReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbookType;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/reports")
@Tag(name = "Reports Information Controller", description = "Reports Information Controller")
@RequiredArgsConstructor
public class ReportsController extends BaseController {

  private final BeanFactory beanFactory;
  private final ResourceBundleMessageSource source;
  private final IVisitReportService visitReportServiceImpl;

  @PostMapping("/exportToExcel/{report-name}")
  @Operation(
      summary = "download Tool Reports By ReportName",
      description = "This api will download Reports By ReportName")
  public ResponseEntity<ByteArrayResource> exportToExcel(
      @RequestBody Object dto,
      @PathVariable("report-name") ReportsToBeanMappings report,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException, IOException {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    IExcelReportService service = (IExcelReportService) beanFactory.getBean(report.getClazz());
    ByteArrayResource byteArrayResource = service.prepareExportToExcel(dto, source, locale);
    String fileName = service.getFileName(dto);
    if (StringUtils.isBlank(fileName)) {
      fileName = "ToolReport";
    }
    if (!fileName.endsWith("." + XSSFWorkbookType.XLSX.getExtension())) {
      fileName += "." + XSSFWorkbookType.XLSX.getExtension();
    }
    return new ResponseEntity<>(byteArrayResource, initHeaders(fileName, true), HttpStatus.CREATED);
  }

  @PostMapping("/exportToImage/{report-name}")
  @Operation(
      summary = "download Tool Reports By ReportName in png",
      description = "This api will download Reports By ReportName in png Format")
  public ResponseEntity<ByteArrayResource> exportToImage(
      @RequestBody Object dto,
      @PathVariable("report-name") ReportsToBeanMappings report,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException, IOException, URISyntaxException {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    IExcelReportService service = (IExcelReportService) beanFactory.getBean(report.getClazz());
    ByteArrayResource byteArrayResource = service.prepareExportToImage(dto, source, locale);
    String fileName = service.getFileName(dto);
    if (StringUtils.isBlank(fileName)) {
      fileName = "ToolReport";
    }

    if (!fileName.endsWith(ExportFileExtensions.ZIP.getExtension())
        || !fileName.endsWith(ExportFileExtensions.PNG.getExtension())) {
      if (byteArrayResource != null
          && "PK".equals(new String(byteArrayResource.getByteArray(), 0, 2))) {
        fileName += ExportFileExtensions.ZIP.getExtension();
      } else {
        fileName += ExportFileExtensions.PNG.getExtension();
      }
    }
    return new ResponseEntity<>(
        byteArrayResource, initHeaders(fileName, false), HttpStatus.CREATED);
  }

  @PostMapping("/visitReport")
  @Operation(
      summary = "Download Visit Reports",
      description = "This api will download Visit Reports")
  public ResponseEntity<ByteArrayResource> visitReport(
      @RequestBody VisitReportDto dto,
      @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN")
          String localeString)
      throws NotFoundDEException, AlreadyExistsDEException, IOException {
    Locale locale = Locale.forLanguageTag(localeString);
    LocaleContextHolder.setLocale(locale);
    ByteArrayResource byteArrayResource =
        visitReportServiceImpl.downloadVisitReport(dto, source, locale);
    String fileName = visitReportServiceImpl.getFileName(dto);
    if (StringUtils.isBlank(fileName)) {
      fileName = "VisitReport";
    }
    if (!fileName.endsWith(ExportFileExtensions.PDF.getExtension())) {
      fileName += ExportFileExtensions.PDF.getExtension();
    }
    String encodedFilename = URLEncoder.encode(fileName, "UTF-8");

    return new ResponseEntity<>(
        byteArrayResource,
        DDWReportsController.initHeaders(encodedFilename, byteArrayResource),
        HttpStatus.CREATED);
  }

  @PostMapping("/sharepoint/visitReport")
  @Operation(
      summary = "Share Visit Report To Sharepoint",
      description = "This api will share visit report to Sharepoint")
  public ResponseEntity<ByteArrayResource> shareVisitReportToSharePoint(
      @RequestParam String visitId, @RequestParam MultipartFile visitReportFile)
      throws NotFoundDEException, AlreadyExistsDEException, CustomDEExceptions, IOException {
    visitReportServiceImpl.shareVisitReportToSharePoint(visitId, visitReportFile);
    return new ResponseEntity<>(HttpStatus.ACCEPTED);
  }

  HttpHeaders initHeaders(String fileName, boolean isExcelDownload) {
    HttpHeaders header = new HttpHeaders();
    if (isExcelDownload) {
      header.setContentType(
          MediaType.parseMediaType(
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
    } else {
      header.setContentType(new MediaType("application", "force-download"));
    }

    header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
    return header;
  }
}
