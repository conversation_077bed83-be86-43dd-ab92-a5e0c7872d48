/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.document.BodyConditionToolGoalItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class BodyConditionToolCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Pens")
  private List<BodyConditionToolItemCosmos> pens;

  @JsonProperty("Goals")
  private List<BodyConditionToolGoalItem> goals;

  @JsonProperty("SelectedPointScale")
  public BCSPointScale selectedPointScale;
}
