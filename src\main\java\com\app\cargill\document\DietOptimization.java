/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder(toBuilder = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DietOptimization implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Ingredients")
  @Builder.Default
  private List<DietOptimizationIngredient> ingredients = new ArrayList<>();

  @JsonProperty("Nutrients")
  @Builder.Default
  private List<DietOptimizationNutrient> nutrients = new ArrayList<>();
}
