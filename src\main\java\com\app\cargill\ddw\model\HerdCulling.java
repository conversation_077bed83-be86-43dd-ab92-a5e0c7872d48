/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdCulling extends HerdBase {

  @JsonProperty("Cull0DIM15")
  private String cull0DIM15;

  @JsonProperty("Cull120DIM200")
  private String cull120DIM200;

  @JsonProperty("Cull15DIM60")
  private String cull15DIM60;

  @JsonProperty("Cull200DIM305")
  private String cull200DIM305;

  @JsonProperty("Cull60DIM120")
  private String cull60DIM120;

  @JsonProperty("CullDIM305plus")
  private String cullDIM305plus;

  @JsonProperty("CullParity1")
  private String cullParity1;

  @JsonProperty("CullParity2")
  private String cullParity2;

  @JsonProperty("CullParity3Plus")
  private String cullParity3Plus;

  @JsonProperty("CullTotal")
  private String cullTotal;

  @JsonProperty("CullingRate")
  private String cullingRate;

  @JsonProperty("DeadRate")
  private String deadRate;

  @JsonProperty("Died0DIM15")
  private String died0DIM15;

  @JsonProperty("Died120DIM200")
  private String died120DIM200;

  @JsonProperty("Died15DIM60")
  private String died15DIM60;

  @JsonProperty("Died200DIM305")
  private String died200DIM305;

  @JsonProperty("Died60DIM120")
  private String died60DIM120;

  @JsonProperty("DiedBulls")
  private String diedBulls;

  @JsonProperty("DiedDIM305plus")
  private String diedDIM305plus;

  @JsonProperty("DiedParity1")
  private String diedParity1;

  @JsonProperty("DiedParity2")
  private String diedParity2;

  @JsonProperty("DiedParity3Plus")
  private String diedParity3Plus;

  @JsonProperty("DiedTotal")
  private String diedTotal;

  @JsonProperty("HeiferCalfMortality")
  private String heiferCalfMortality;

  @JsonProperty("HeiferCalfMortalityV2")
  private String heiferCalfMortalityV2;

  @JsonProperty("HeiferCullRateAbove60Days")
  private String heiferCullRateAbove60Days;

  @JsonProperty("LactatingTotal")
  private String lactatingTotal;

  @JsonProperty("NrBornYS")
  private String nrBornYS;

  @JsonProperty("NrDeadYS")
  private String nrDeadYS;

  @JsonProperty("PercCulled0DIM60")
  private String percCulled0DIM60;

  @JsonProperty("PurchasedBulls")
  private String purchasedBulls;

  @JsonProperty("PurchasedTotal")
  private String purchasedTotal;

  @JsonProperty("Sold0DIM15")
  private String sold0DIM15;

  @JsonProperty("Sold120DIM200")
  private String sold120DIM200;

  @JsonProperty("Sold15DIM60")
  private String sold15DIM60;

  @JsonProperty("Sold200DIM305")
  private String sold200DIM305;

  @JsonProperty("Sold60DIM120")
  private String sold60DIM120;

  @JsonProperty("SoldBulls")
  private String soldBulls;

  @JsonProperty("SoldDIM305plus")
  private String soldDIM305plus;

  @JsonProperty("SoldParity1")
  private String soldParity1;

  @JsonProperty("SoldParity2")
  private String soldParity2;

  @JsonProperty("SoldParity3Plus")
  private String soldParity3Plus;

  @JsonProperty("SoldTotal")
  private String soldTotal;

  @JsonProperty("YSFNrDied0To30d")
  private String ySFNrDied0To30d;

  @JsonProperty("YSFNrDied184To365d")
  private String ySFNrDied184To365d;

  @JsonProperty("YSFNrDied31To60d")
  private String ySFNrDied31To60d;

  @JsonProperty("YSFNrDied61To90d")
  private String ySFNrDied61To90d;

  @JsonProperty("YSFNrDied91To183d")
  private String ySFNrDied91To183d;

  @JsonProperty("pen_uuid")
  private UUID penId;
}
