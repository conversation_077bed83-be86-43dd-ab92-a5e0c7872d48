/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.BreedReturnOverFeed;
import com.app.cargill.constants.Feeding;
import com.app.cargill.constants.SupplementTypes;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdProfile implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("Breed")
  private BreedReturnOverFeed breed;

  @JsonProperty("OtherBreedTypes")
  private String otherBreedType;

  @JsonProperty("FeedingType")
  private Feeding feedingType;

  @JsonProperty("NumberOfTmrGroups")
  private Integer numberOfTmrGroups;

  @JsonProperty("TypeOfSupplement")
  private SupplementTypes typeOfSupplement;

  @JsonProperty("CoolAid")
  private Boolean coolAid;

  @JsonProperty("FortissaFit")
  private Boolean fortissaFit;

  @JsonProperty("Mun")
  private Double mun;

  @JsonProperty("MilkingPerDay")
  private Integer milkingPerDay;
}
