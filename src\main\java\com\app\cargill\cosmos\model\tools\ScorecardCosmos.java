/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.ScorecardSection;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class ScorecardCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  public String visitId;

  @JsonProperty("Sections")
  public List<ScorecardSection> sections;
}
