/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.constants.Business;
import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.UserCosmos;
import com.app.cargill.cosmos.repo.UsersCosmosRepository;
import com.app.cargill.document.UserDocument;
import com.app.cargill.model.User;
import com.app.cargill.repository.UserRepository;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class UsersMigration {

  private final UsersCosmosRepository cosmosRepository;
  private final UserRepository usersRepository;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("User");
    try {
      List<User> usersList = fetchAll(migrationResult);
      usersRepository.saveAll(usersList);
      log.info("Users migration completed. {} records copied to PostgresSQL", usersList.size());
    } catch (Exception e) {
      log.error("Error occurred during Users migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  public Mono<User> moveRecord(String email) {
    return Mono.fromCallable(() -> findUserInDb(email))
        .subscribeOn(Schedulers.boundedElastic())
        .switchIfEmpty(
            Mono.fromCallable(() -> handleCosmosUser(email))
                .subscribeOn(Schedulers.boundedElastic()))
        .onErrorResume(
            t -> {
              log.error("Error during record process", t);
              return Mono.error(t);
            });
  }

  private User findUserInDb(String email) {
    try {
      return usersRepository.findByUserName(email);
    } catch (Exception e) {
      throw new MigrationException(
          String.format("There was an error getting record from the DB: %s", email), e);
    }
  }

  private User handleCosmosUser(String email) {
    List<UserCosmos> users = cosmosRepository.getUsersByEmail(email);
    if (users.size() > 1) {
      throw new MigrationException(
          String.format("More than one record found in CosmosDB for this email: %s", email));
    } else if (users.isEmpty()) {
      throw new MigrationException(
          String.format("User not found in CosmosDB for this email: %s", email));
    } else {
      User user = new User(usersMapper.map(users.get(0)));
      user.setLocalId(user.getUserDocument().getId().toString());
      return usersRepository.save(user);
    }
  }

  private List<User> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    Iterator<UserCosmos> cosmosIterator = cosmosRepository.findAll().iterator();
    List<User> usersList = new ArrayList<>();
    int failures = 0;
    int recordsFetched = 0;
    while (cosmosIterator.hasNext()) {
      try {
        recordsFetched++;
        User user = new User(usersMapper.map(cosmosIterator.next()));
        user.setLocalId(user.getUserDocument().getId().toString());
        usersList.add(user);
      } catch (Exception e) {
        log.error("There was an error fetching a User from CosmosDB", e);
        failures++;
      }
    }
    log.info("{} Users fetched from CosmosDB", recordsFetched);
    if (failures > 0) {
      log.warn("{} users failed to map during the fetching process", failures);
    }
    migrationResult.setSucceeded(usersList.size());
    migrationResult.setFailed(failures);
    return usersList;
  }

  private final CosmosToModelMapper<UserCosmos, UserDocument> usersMapper =
      source ->
          UserDocument.builder()
              .id(UUID.fromString(source.getId()))
              .salesforceCountryId(source.getSalesforceCountryId())
              .countryId(Business.handleCountryId(source.getCountryId()))
              .userName(source.getUserName())
              .build();
}
