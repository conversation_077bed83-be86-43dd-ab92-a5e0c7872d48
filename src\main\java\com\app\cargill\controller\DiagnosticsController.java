/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.dto.EmptyJsonBody;
import com.app.cargill.dto.ForceUpdateDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.IForceUpdateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Date;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.context.MessageSource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/diagnostics")
@Tag(
    name = "Diagnostics Controller",
    description = "Publicly accessible for System health and diagnostics")
@RequiredArgsConstructor
@Slf4j
public class DiagnosticsController extends BaseController {
  private final IForceUpdateService iForceUpdateServiceImpl;
  private final MessageSource source;

  @GetMapping(value = "/heartbeat", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Service heartbeat check",
      description = "This api will respond if server is alive")
  public ResponseEntity<ResponseEntityDto<EmptyJsonBody>> heartbeat() {
    return handleSuccessResponse(
        "Alive! My time is " + new Date() + " and build is @COMMIT_HASH@", new EmptyJsonBody());
  }

  @GetMapping(value = "/check-locale", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Check locale ", description = "This api will validate Locale")
  public ResponseEntity<ResponseEntityDto<EmptyJsonBody>> getLocaleMessage(
      @RequestHeader(name = "Accept-Language", required = false) String localeString) {
    Locale locale = Locale.forLanguageTag(localeString);
    log.info(
        "Returning greetings for locale = {}", StringEscapeUtils.escapeJava(locale.toString()));
    String message = String.format("Returning greetings for locale = %1$s", locale);
    return handleSuccessResponse(
        source.getMessage(LangKeys.WELCOME_MESSAGE, new Object[] {message}, locale),
        new EmptyJsonBody());
  }

  @GetMapping(value = "/buildInfo")
  @Operation(
      summary = "Get latest build versions for Android and IOS",
      description = "This method will return the latest build version for Android and IOS")
  public ResponseEntity<ResponseEntityDto<ForceUpdateDto>> getBuildInfo() {

    return handleSuccessResponse(iForceUpdateServiceImpl.getBuildInfo());
  }
}
