/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScorecardSection implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Used to order sections within a larger container.
  /// </summary>
  /// <value>The index.</value>
  @JsonProperty("Index")
  private Integer index;
  /// <summary>
  /// The name of the section to display to the user.
  /// </summary>
  /// <value>The name of the section.</value>
  /// <remarks>This will be a key to the string resources file.</remarks>
  @JsonProperty("SectionName")
  private String sectionName;

  /// <summary>
  /// Category Silage option to select question
  /// </summary>
  @JsonProperty("ScorecardSilages")
  private List<ScorecardSilage> scorecardSilages;

  @JsonProperty("Status")
  private ToolStatuses status;
}
