/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdInventory extends HerdBase {

  @JsonProperty("AvgAgeDaysExclDry")
  private String avgAgeDaysExclDry;

  @JsonProperty("AvgAgeDaysInclDry")
  private String avgAgeDaysInclDry;

  @JsonProperty("AvgDimDaysExclDry")
  private String avgDimDaysExclDry;

  @JsonProperty("AvgDimDaysInclDry")
  private String avgDimDaysInclDry;

  @JsonProperty("NrDoNotBreed")
  private String nrDoNotBreed;

  @JsonProperty("NrDry")
  private String nrDry;

  @JsonProperty("NrDryParity1")
  private String nrDryParity1;

  @JsonProperty("NrDryParity2Plus")
  private String nrDryParity2Plus;

  @JsonProperty("NrMilkingCows")
  private String nrMilkingCows;

  @JsonProperty("NrMilkingCowsBred")
  private String nrMilkingCowsBred;

  @JsonProperty("NrMilkingCowsDims0To15")
  private String nrMilkingCowsDims0To15;

  @JsonProperty("NrMilkingCowsDims0To60")
  private String nrMilkingCowsDims0To60;

  @JsonProperty("NrMilkingCowsDims120To200")
  private String nrMilkingCowsDims120To200;

  @JsonProperty("NrMilkingCowsDims15To60")
  private String nrMilkingCowsDims15To60;

  @JsonProperty("NrMilkingCowsDims200To305")
  private String nrMilkingCowsDims200To305;

  @JsonProperty("NrMilkingCowsDims305")
  private String nrMilkingCowsDims305;

  @JsonProperty("NrMilkingCowsDims60To120")
  private String nrMilkingCowsDims60To120;

  @JsonProperty("NrMilkingCowsOpen")
  private String nrMilkingCowsOpen;

  @JsonProperty("NrMilkingCowsParity1")
  private String nrMilkingCowsParity1;

  @JsonProperty("NrMilkingCowsParity2")
  private String nrMilkingCowsParity2;

  @JsonProperty("NrMilkingCowsParity3Plus")
  private String nrMilkingCowsParity3Plus;

  @JsonProperty("NrMilkingCowsPregnant")
  private String nrMilkingCowsPregnant;

  @JsonProperty("NrYoungStock")
  private String nrYoungStock;

  @JsonProperty("NrYoungStock10To16Months")
  private String nrYoungStock10To16Months;

  @JsonProperty("NrYoungStock10To16MonthsBred")
  private String nrYoungStock10To16MonthsBred;

  @JsonProperty("NrYoungStock10To16MonthsOpen")
  private String nrYoungStock10To16MonthsOpen;

  @JsonProperty("NrYoungStock10To16MonthsPregnant")
  private String nrYoungStock10To16MonthsPregnant;

  @JsonProperty("NrYoungStock12To24Months")
  private String nrYoungStock12To24Months;

  @JsonProperty("NrYoungStockAbove16Months")
  private String nrYoungStockAbove16Months;

  @JsonProperty("NrYoungStockAbove16MonthsBred")
  private String nrYoungStockAbove16MonthsBred;

  @JsonProperty("NrYoungStockAbove16MonthsOpen")
  private String nrYoungStockAbove16MonthsOpen;

  @JsonProperty("NrYoungStockAbove16MonthsPregnant")
  private String nrYoungStockAbove16MonthsPregnant;

  @JsonProperty("NrYoungStockAbove24Months")
  private String nrYoungStockAbove24Months;

  @JsonProperty("NrYoungStockTo10Months")
  private String nrYoungStockTo10Months;

  @JsonProperty("NrYoungStockTo12Months")
  private String nrYoungStockTo12Months;
}
