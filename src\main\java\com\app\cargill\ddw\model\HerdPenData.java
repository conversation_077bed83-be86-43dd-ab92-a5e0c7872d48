/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdPenData extends HerdBase {

  @JsonProperty("AvgNrAnimalsInGroup")
  private String avgNrAnimalsInGroup;

  @JsonProperty("AvgNrAnimalsInPen")
  private String avgNrAnimalsInPen;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("avgDIM")
  private String avgDIM;

  @JsonProperty("avgYield")
  private String avgYield;
}
