/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.NotificationType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class NotificationDocument extends EditableDocumentBase implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("Title")
  private String title;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("Type")
  private NotificationType type;

  @JsonProperty("MobileCreatedAt")
  private Instant mobileCreatedAt;

  @JsonProperty("UserId")
  private String userId;

  @JsonProperty("IsRead")
  private Boolean isRead;

  @JsonProperty("Keys")
  @Builder.Default
  private Map<String, String> keys = new HashMap<>();
}
