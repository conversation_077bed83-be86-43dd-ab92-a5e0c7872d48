/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MetabolicIncidenceToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("SelectedVisits")
  private List<UUID> selectedVisits;

  @JsonProperty("Outputs")
  private MetabolicIncidenceOutputToolItem outputs;

  @JsonProperty("TotalFreshCowsPerYear")
  private Integer totalFreshCowsPerYear;

  @JsonProperty("ReplacementCowCost")
  private Double replacementCowCost;

  @JsonProperty("CostOfExtraDaysOpen")
  private Double costOfExtraDaysOpen;

  @JsonProperty("TotalFreshCowsForEvaluation")
  private Integer totalFreshCowsForEvaluation;

  @JsonProperty("RetainedPlacentaIncidence")
  private Integer retainedPlacentaIncidence;

  @JsonProperty("MetritisIncidence")
  private Integer metritisIncidence;

  @JsonProperty("DisplacedAbomasumIncidence")
  private Integer displacedAbomasumIncidence;

  @JsonProperty("KetosisIncidence")
  private Integer ketosisIncidence;

  @JsonProperty("MilkFeverIncidence")
  private Integer milkFeverIncidence;

  @JsonProperty("DystociaIncidence")
  private Integer dystociaIncidence;

  @JsonProperty("DeathLossIncidence")
  private Integer deathLossIncidence;

  @JsonProperty("RetainedPlacentaWeight")
  private Double retainedPlacentaWeight;

  @JsonProperty("RetainedPlacentaDaysOpen")
  private Integer retainedPlacentaDaysOpen;

  @JsonProperty("RetainedPlacentaCost")
  private Double retainedPlacentaCost;

  @JsonProperty("MetritisWeight")
  private Double metritisWeight;

  @JsonProperty("MetritisDaysOpen")
  private Integer metritisDaysOpen;

  @JsonProperty("MetritisCost")
  private Double metritisCost;

  @JsonProperty("DisplacedAbomasumWeight")
  private Double displacedAbomasumWeight;

  @JsonProperty("DisplacedAbomasumDaysOpen")
  private Integer displacedAbomasumDaysOpen;

  @JsonProperty("DisplacedAbomasumCost")
  private Double displacedAbomasumCost;

  @JsonProperty("KetosisWeight")
  private Double ketosisWeight;

  @JsonProperty("KetosisDaysOpen")
  private Integer ketosisDaysOpen;

  @JsonProperty("KetosisCost")
  private Double ketosisCost;

  @JsonProperty("MilkFeverWeight")
  private Double milkFeverWeight;

  @JsonProperty("MilkFeverDaysOpen")
  private Integer milkFeverDaysOpen;

  @JsonProperty("MilkFeverCost")
  private Double milkFeverCost;

  @JsonProperty("DystociaWeight")
  private Double dystociaWeight;

  @JsonProperty("DystociaOpen")
  private Integer dystociaOpen;

  @JsonProperty("DystociaCost")
  private Double dystociaCost;

  @JsonProperty("DeathLossWeight")
  private Double deathLossWeight;

  @JsonProperty("DeathLossOpen")
  private Integer deathLossOpen;

  @JsonProperty("DeathLossCost")
  private Double deathLossCost;

  @JsonProperty("MilkPrice")
  private Double milkPrice;
}
