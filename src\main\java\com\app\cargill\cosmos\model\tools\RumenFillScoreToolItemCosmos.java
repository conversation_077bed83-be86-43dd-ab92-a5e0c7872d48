/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.RumenHealthManureScores;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class RumenFillScoreToolItemCosmos extends EditableToolPenEntityBaseCosmos {
  @JsonProperty("RumenFillScoreVisitsSelected")
  private List<String> rumenFillScoreVisitsSelected;

  @JsonProperty("RumenFillScores")
  private RumenHealthManureScores rumenFillScores;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;
}
