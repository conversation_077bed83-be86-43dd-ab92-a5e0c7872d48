/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.ResponseEntityDto;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * @TODO no need of class inheritance Rename to ResponseDecorator Can use static methods since it
 * doesn't depend on anything Spring.Bean is also fine but will just needlessly pollute the context
 */
public class BaseController {
  public <T> ResponseEntity<ResponseEntityDto<T>> handleSuccessResponse(T data) {
    return handleSuccessResponse("", data);
  }

  public <T> ResponseEntity<ResponseEntityDto<T>> handleSuccessResponse(String message, T data) {
    return handleResponse(message, data, ResponseStatus.SUCCESS, HttpStatus.OK);
  }

  public <T> ResponseEntity<ResponseEntityDto<T>> handleResponse(
      String message, T data, ResponseStatus responseStatus, HttpStatus httpStatus) {
    ResponseEntityDto<T> responseEntityDto = new ResponseEntityDto<>(responseStatus, message, data);
    return new ResponseEntity<>(responseEntityDto, httpStatus);
  }
}
