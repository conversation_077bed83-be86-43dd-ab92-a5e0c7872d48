# User Journey Analytics API Examples

This document provides examples of how to use the User Journey Analytics API for tracking user activities and generating analytics reports.

## API Endpoints

### 1. Log Single User Activity
**POST** `/user-journey-analytics/log-activity`

#### Example Request:
```json
{
  "eventName": "PAGE_VIEW",
  "path": "/dashboard",
  "username": "<EMAIL>",
  "userId": "user123",
  "sessionId": "session456",
  "pageTitle": "Dashboard - Dairy Analytics",
  "actionType": "NAVIGATION",
  "browser": "Chrome",
  "operatingSystem": "Windows 10",
  "deviceType": "Desktop",
  "screenResolution": "1920x1080",
  "country": "United States",
  "region": "California",
  "city": "San Francisco",
  "language": "en-US",
  "accountId": "acc789",
  "siteId": "site101",
  "featureUsed": "Dashboard",
  "moduleName": "Analytics",
  "lastVisited": "2024-01-15T10:30:00Z",
  "responseTimeMs": 250,
  "timeOnPageSeconds": 45,
  "scrollDepthPercentage": 75.5
}
```

### 2. Log Multiple User Activities (Batch)
**POST** `/user-journey-analytics/log-activities-batch`

#### Example Request:
```json
[
  {
    "eventName": "BUTTON_CLICK",
    "path": "/reports",
    "username": "<EMAIL>",
    "actionType": "CLICK",
    "elementClicked": "generate-report-btn",
    "featureUsed": "Report Generation",
    "moduleName": "Reports",
    "lastVisited": "2024-01-15T11:00:00Z"
  },
  {
    "eventName": "FORM_SUBMIT",
    "path": "/settings",
    "username": "<EMAIL>",
    "actionType": "SUBMIT",
    "formData": "user_preferences_updated",
    "featureUsed": "User Settings",
    "moduleName": "Settings",
    "lastVisited": "2024-01-15T11:05:00Z"
  }
]
```

### 3. Log Events List with Iteration
**POST** `/user-journey-analytics/log-events-list`

This endpoint accepts a list of events and iterates through each one individually, saving them one by one to the database.

#### Example Request:
```json
[
  {
    "eventName": "SESSION_START",
    "path": "/login",
    "username": "<EMAIL>",
    "actionType": "LOGIN",
    "lastVisited": "2024-01-15T09:00:00Z"
  },
  {
    "eventName": "PAGE_VIEW",
    "path": "/dashboard",
    "username": "<EMAIL>",
    "actionType": "NAVIGATION",
    "lastVisited": "2024-01-15T09:00:30Z"
  },
  {
    "eventName": "BUTTON_CLICK",
    "path": "/reports",
    "username": "<EMAIL>",
    "actionType": "CLICK",
    "elementClicked": "generate-btn",
    "lastVisited": "2024-01-15T09:02:00Z"
  }
]
```

#### Key Features:
- **Individual Processing**: Each event is processed and saved separately
- **Error Resilience**: Continues processing even if individual events fail
- **Detailed Response**: Shows exactly how many events were successfully saved
- **Request Enrichment**: Automatically adds IP, User-Agent, and Referer information

### 4. Get User Activities with Filtering
**GET** `/user-journey-analytics/activities`

#### Query Parameters:
- `username` (optional): Filter by username
- `eventName` (optional): Filter by event name
- `accountId` (optional): Filter by account ID
- `siteId` (optional): Filter by site ID
- `featureUsed` (optional): Filter by feature used
- `moduleName` (optional): Filter by module name
- `pathPattern` (optional): Filter by path pattern
- `startDate` (optional): Start date for date range filter (ISO 8601)
- `endDate` (optional): End date for date range filter (ISO 8601)
- `page` (optional): Page number (default: 0)
- `size` (optional): Page size (default: 20)

#### Example Request:
```
GET /user-journey-analytics/activities?username=<EMAIL>&startDate=2024-01-01T00:00:00Z&endDate=2024-01-31T23:59:59Z&page=0&size=10
```

### 5. Export to Excel (Main Feature)
**GET** `/user-journey-analytics/export/excel`

#### Query Parameters:
- `lastUpdatedTime` (required): Only include data updated after this timestamp (ISO 8601)
- `endDate` (optional): End date for the export (default: current time)
- `username` (optional): Filter by username
- `eventName` (optional): Filter by event name
- `accountId` (optional): Filter by account ID

#### Example Request:
```
GET /user-journey-analytics/export/excel?lastUpdatedTime=2024-01-01T00:00:00Z&endDate=2024-01-31T23:59:59Z&username=<EMAIL>
```

This will return an Excel file containing all user journey data that was updated after the specified `lastUpdatedTime`.

### 6. Get Analytics Data

#### Event Analytics
**GET** `/user-journey-analytics/analytics/events`

Returns count of activities grouped by event name.

#### Path Analytics
**GET** `/user-journey-analytics/analytics/paths`

Returns count of activities grouped by path.

#### User Analytics
**GET** `/user-journey-analytics/analytics/users`

Returns count of activities grouped by username.

#### Daily Analytics
**GET** `/user-journey-analytics/analytics/daily?startDate=2024-01-01T00:00:00Z&endDate=2024-01-31T23:59:59Z`

Returns daily activity counts for the specified date range.

## Common Event Names for User Journey Tracking

- `PAGE_VIEW` - User views a page
- `BUTTON_CLICK` - User clicks a button
- `FORM_SUBMIT` - User submits a form
- `SEARCH` - User performs a search
- `DOWNLOAD` - User downloads a file
- `LOGIN` - User logs in
- `LOGOUT` - User logs out
- `NAVIGATION` - User navigates between sections
- `FEATURE_ACCESS` - User accesses a specific feature
- `ERROR_OCCURRED` - An error occurred during user interaction
- `SESSION_START` - User session starts
- `SESSION_END` - User session ends
- `REPORT_GENERATED` - User generates a report
- `DATA_EXPORT` - User exports data
- `SETTINGS_CHANGED` - User changes settings

## Common Action Types

- `CLICK` - Click action
- `SUBMIT` - Form submission
- `NAVIGATION` - Page navigation
- `SEARCH` - Search action
- `DOWNLOAD` - File download
- `UPLOAD` - File upload
- `VIEW` - Content viewing
- `EDIT` - Content editing
- `DELETE` - Content deletion
- `CREATE` - Content creation

## Response Format

All API responses follow this format:

```json
{
  "status": "SUCCESS",
  "message": "Operation completed successfully",
  "data": { ... },
  "currentTimeStamp": "2024-01-15T12:00:00Z"
}
```

## Excel Export Features

The Excel export includes **4 required fields for analytics**:
1. **Event Name** - The type of user action (PAGE_VIEW, BUTTON_CLICK, etc.)
2. **Path** - The URL/route where the action occurred
3. **Username** - The user who performed the action
4. **Last Visited** - Timestamp when the action occurred

The Excel file includes metadata about the export such as:
- Export date and time
- Date range of the data
- Total number of records
- Filter criteria applied

This simplified format is optimized for analytics tools and focuses on the core user journey data needed for analysis.

### Sample Excel Export Output:
```
Event Name          | Path                    | Username                | Last Visited
PAGE_VIEW          | /dashboard              | <EMAIL>    | 2024-01-15 14:30:00
BUTTON_CLICK       | /reports/milk-production| <EMAIL>  | 2024-01-15 15:45:30
FORM_SUBMIT        | /settings/preferences   | <EMAIL> | 2024-01-15 16:20:15
FEATURE_ACCESS     | /mobile/herd-monitoring | <EMAIL>| 2024-01-15 17:10:45
ERROR_OCCURRED     | /reports/export         | <EMAIL>| 2024-01-15 18:30:00
```

## Notes

1. The `lastUpdatedTime` parameter in the Excel export is crucial - it ensures you only get new/updated data since your last export.
2. All timestamps should be in ISO 8601 format (e.g., "2024-01-15T10:30:00Z").
3. The API automatically enriches requests with IP address, user agent, and referrer information if not provided.
4. All user activity data is soft-deleted (marked as deleted rather than physically removed).
5. The system creates comprehensive indexes for optimal query performance.
