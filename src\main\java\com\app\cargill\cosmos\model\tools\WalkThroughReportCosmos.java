/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.WalkThroughQualityType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class WalkThroughReportCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("Quality")
  private WalkThroughQualityType quality;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("WaterQuality")
  private WalkThroughQualityType waterQuality;

  @JsonProperty("BeddingQuality")
  private WalkThroughQualityType beddingQuality;

  @JsonProperty("HockAbrasion")
  private Double hockAbrasion;

  @JsonProperty("HockAbrasionGoal")
  private Double hockAbrasionGoal;

  @JsonProperty("UterineDischarge")
  private Double uterineDischarge;

  @JsonProperty("UterineDischargeGoal")
  private Double uterineDischargeGoal;

  @JsonProperty("NasalDischarge")
  private Double nasalDischarge;

  @JsonProperty("NasalDischargeGoal")
  private Double nasalDischargeGoal;

  @JsonProperty("CowComfort")
  private Double cowComfort;

  @JsonProperty("CowComfortGoal")
  private Double cowComfortGoal;

  @JsonProperty("ManureScoreComment")
  private String manureScoreComment;

  @JsonProperty("Appearance")
  private String appearance;

  @JsonProperty("RumenFill")
  private String rumenFill;

  @JsonProperty("BeddingDepth")
  private String beddingDepth;

  @JsonProperty("PositiveTrends")
  private String positiveTrends;

  @JsonProperty("Opportunities")
  private String opportunities;

  @JsonProperty("Comments")
  private String comments;

  @JsonProperty("RuminationChewing")
  private Double ruminationChewing;

  @JsonProperty("RuminationChewingGoal")
  private Double ruminationChewingGoal;

  @JsonProperty("ChewsPerCud")
  private Double chewsPerCud;

  @JsonProperty("ChewsPerCudGoal")
  private Double chewsPerCudGoal;

  @JsonProperty("ManureScore")
  private Double manureScore;

  @JsonProperty("ManureScoreGoal")
  private Double manureScoreGoal;

  @JsonProperty("LocomotionScore")
  private Double locomotionScore;

  @JsonProperty("LocomotionScoreGoal")
  private Double locomotionScoreGoal;

  @JsonProperty("BodyConditionScore")
  private Double bodyConditionScore;

  @JsonProperty("BodyConditionScoreGoal")
  private Double bodyConditionScoreGoal;
}
