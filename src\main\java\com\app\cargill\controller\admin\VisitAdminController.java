/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller.admin;

import com.app.cargill.dto.admin.Visit;
import com.app.cargill.service.admin.VisitAdminService;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/visit")
@Tag(name = "User management controller")
@RequiredArgsConstructor
public class VisitAdminController {

  private final VisitAdminService visitAdminService;

  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  @PreAuthorize(("authentication.name == @securityConfiguration.getApiAdminId()"))
  public List<Visit> getAllUsers() {
    return visitAdminService.getLatestVisits();
  }
}
