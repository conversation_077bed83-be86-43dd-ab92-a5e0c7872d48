/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.azure.core.annotation.QueryParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/lift/account")
@Tag(
    name = "Salesforce Lift Account",
    description = "Controller related to actions over Account objects")
@RequiredArgsConstructor
@Slf4j
public class SalesforceLiftAccountController {

  private final LiftAccountService liftAccountService;

  @GetMapping
  @Operation(summary = "Get a LIFT account", description = "Get a lift account by owner's email")
  public List<AccountDocument> getAccount(@QueryParam("email") String email) {
    return liftAccountService.getAllAccounts(
        Collections.singletonList(String.format("Owner.Email='%s'", email)));
  }

  @GetMapping("/getDeletedAccounts")
  @Operation(
      summary =
          "Fetch all the Db accounts.Check if it exists on LIFT by Golden Record Id.Return List of"
              + " accounts that do not exist\n",
      description = "Deleted accounts check service")
  public List<Accounts> getDeletedAccounts() {
    return liftAccountService.getDeletedAccounts();
  }

  @GetMapping("/getMismatchId")
  @Operation(
      summary = "Get LIFT External_Data_Source__c mismatched ID",
      description = "Find LIFT External_Data_Source__c mismatched ID")
  public List<AccountDocument> getMismatchId(@RequestParam(required = false) String email) {
    return liftAccountService.getMismatchedId(email);
  }
}
