/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdNutrition extends HerdBase {

  @JsonProperty("BatchID")
  private String batchID;

  @JsonProperty("Currency")
  private String currency;

  @JsonProperty("IngredientName")
  private String ingredientName;

  @JsonProperty("MappedIngredientId")
  private String mappedIngredientId;

  @JsonProperty("MappedIngredientName")
  private String mappedIngredientName;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("PricePerKg")
  private String pricePerKg;

  @JsonProperty("avgDryMatterPerc")
  private String avgDryMatterPerc;

  @JsonProperty("avgNrCowsFS")
  private String avgNrCowsFS;

  @JsonProperty("avgNrCowsMS")
  private String avgNrCowsMS;

  @JsonProperty("avgPMRAsCalled")
  private String avgPMRAsCalled;

  @JsonProperty("avgPMRAsFed")
  private String avgPMRAsFed;

  @JsonProperty("avgPMRDMAsCalled")
  private String avgPMRDMAsCalled;

  @JsonProperty("avgPMRDMAsFed")
  private String avgPMRDMAsFed;

  @JsonProperty("avgPMRDMWeighBack")
  private String avgPMRDMWeighBack;

  @JsonProperty("avgPMRWeighBack")
  private String avgPMRWeighBack;

  @JsonProperty("avgPMRasFedCosts")
  private String avgPMRasFedCosts;

  @JsonProperty("avgTMRAsCalled")
  private String avgTMRAsCalled;

  @JsonProperty("avgTMRAsFed")
  private String avgTMRAsFed;

  @JsonProperty("avgTMRDMAsCalled")
  private String avgTMRDMAsCalled;

  @JsonProperty("avgTMRDMAsFed")
  private String avgTMRDMAsFed;

  @JsonProperty("avgTMRDMWeighBack")
  private String avgTMRDMWeighBack;

  @JsonProperty("avgTMRWBCosts")
  private String avgTMRWBCosts;

  @JsonProperty("avgTMRWeighBack")
  private String avgTMRWeighBack;

  @JsonProperty("avgTMRasFedCosts")
  private String avgTMRasFedCosts;
}
