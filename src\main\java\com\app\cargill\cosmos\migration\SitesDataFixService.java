/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.cosmos.model.SiteCosmos;
import com.app.cargill.cosmos.repo.SitesCosmosRepository;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.SiteVisit;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.sf.de.LiftSyncService;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class SitesDataFixService {

  private final SitesRepository sitesRepository;
  private final LiftSyncService liftSyncService;
  private final VisitsRepository visitsRepository;
  private final SitesCosmosRepository sitesCosmosRepository;
  private final SiteMappingsRepository siteMappingsRepository;
  private int count;
  private int mappingFixCount;
  private int unnecessaryMappingRemoved;
  private int siteMappingsCount = 0;
  private List<String> ids = new ArrayList<>();

  public Flux<SyncResult> fixSiteMissingSiteMappings() {
    return Mono.fromCallable(sitesRepository::findSitesWithMissingSiteMappings)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            site ->
                Mono.fromCallable(
                        () ->
                            liftSyncService.executeMissingMappingSiteSync(
                                site.getSiteDocument().getId().toString()))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Flux<SiteMappings> fixHasReportInSites() {
    return Mono.fromCallable(siteMappingsRepository::findAllWithDDWHerdId)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            siteMapping ->
                Mono.fromCallable(() -> fixSitesHasReport(siteMapping))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  private SiteMappings fixSitesHasReport(SiteMappings siteMapping) {
    siteMappingsCount++;
    Sites site =
        sitesRepository.findBySiteId(
            siteMapping.getSiteMappingDocument().getLabyrinthSiteId().toString());
    if (!Objects.isNull(site)) {
      site.getSiteDocument().setHasReport(true);
      sitesRepository.save(site);
      log.info("Fixed site Mapping {} with id: {}", siteMappingsCount, siteMapping);
    }
    return siteMapping;
  }

  public Mono<Sites> fixSitesVisitReference() {
    return Mono.fromCallable(sitesRepository::findAll)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(this::fixVisitsReference);
  }

  public Mono<Sites> fixVisitsReference(List<Sites> sites) {
    log.info("Sites FOUND: {} ", sites.size());
    count = 0;
    mappingFixCount = 0;
    unnecessaryMappingRemoved = 0;
    if (!sites.isEmpty()) {
      sites.parallelStream()
          .forEach(
              site -> {
                count++;
                log.info(
                    "Searching visits for site number {} : {}",
                    count,
                    site.getSiteDocument().getId());
                List<Visits> visits =
                    visitsRepository.findVisitsBySiteId(site.getSiteDocument().getId().toString());
                if (!visits.isEmpty()) {
                  log.info(
                      "FIXING VISITS REFERENCE INSIDE SITE: {}", site.getSiteDocument().getId());
                  mappingFixCount++;
                  List<SiteVisit> siteVisits = new ArrayList<>();
                  visits.stream()
                      .forEach(
                          visit -> {
                            SiteVisit sitevisit =
                                SiteVisit.builder()
                                    .labyrinthVisitId(visit.getVisitDocument().getId())
                                    .status(visit.getVisitDocument().getStatus())
                                    .visitDate(visit.getVisitDocument().getVisitDate())
                                    .visitName(visit.getVisitDocument().getVisitName())
                                    .build();
                            siteVisits.add(sitevisit);
                          });
                  ids.add(site.getSiteDocument().getId().toString());
                  site.getSiteDocument().setVisits(siteVisits);
                } else {
                  unnecessaryMappingRemoved++;
                  site.getSiteDocument().setVisits(new ArrayList<>());
                }
              });
      log.info("Mapping fixed for Sites: {}", mappingFixCount);
      log.info("Unnecessary visits mapping removed for Sites: {}", unnecessaryMappingRemoved);
      for (Sites site : sites) {
        try {
          sitesRepository.save(site);
        } catch (Exception e) {
          log.error("SITE_SAVE_ERROR {}", site.getId(), e);
        }
      }
    }
    return Mono.just(!sites.isEmpty() ? sites.get(0) : new Sites());
  }

  public Flux<SyncResult> fixSiteSwitchedSiteMappings() {
    return Mono.fromCallable(sitesRepository::findAll)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            site ->
                Mono.fromCallable(
                        () ->
                            liftSyncService.fixSwitchedSiteMappings(
                                site.getSiteDocument().getId().toString()))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Flux<SyncResult> fixSiteMissingExternalId() {
    return Mono.fromCallable(sitesRepository::findSitesWithMissingExternalId)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            site ->
                Mono.fromCallable(
                        () ->
                            liftSyncService.executeExternalIdSiteSync(
                                site.getSiteDocument().getId().toString()))
                    .subscribeOn(Schedulers.boundedElastic()));
  }

  public Flux<UUID> fixSitesWithDeletedMappings(List<UUID> sites) {
    return Flux.fromIterable(sites).map(this::processSiteWithDeletedMappings);
  }

  public Mono<SyncResult> fixSitesWithMissingDdwMapping() {
    return Mono.fromCallable(sitesRepository::findCrescendoSitesWithMissingDdw)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            site -> {
              Sites processedSite = processSiteWithMissingDdw(site);
              if (processedSite != null) {
                return Mono.just(processedSite);
              } else {
                return Mono.empty();
              }
            })
        .subscribeOn(Schedulers.boundedElastic())
        .reduce(0, (accumulator, a) -> accumulator + 1)
        .map(
            i -> {
              log.info("DDW_MAPPING_FIX {} sites", i);
              return new SyncResult("DDW_MAPPING_FIX");
            });
  }

  private Sites processSiteWithMissingDdw(Sites site) {
    log.debug("PROCESSING_DDW_MAPPING_FOR_SITE {}", site.getId());
    Optional<SiteCosmos> cosmosOptional =
        sitesCosmosRepository.findById(site.getSiteDocument().getId().toString());
    if (cosmosOptional.isEmpty()) {
      return null;
    }
    SiteCosmos siteCosmos = cosmosOptional.get();
    if (siteCosmos.getDataSourceMappings() == null) {
      return null;
    }
    Optional<DataSourceMapping> dataSourceMappingOptional =
        siteCosmos.getDataSourceMappings().stream()
            .filter(sm -> sm.getSystemName() != null && sm.getSystemName().equals("DDW"))
            .findAny();
    if (dataSourceMappingOptional.isEmpty()) {
      return null;
    } else {
      log.debug("FOUND_MAPPING {}", site.getId());
      DataSourceMapping dsm = dataSourceMappingOptional.get();
      site.getSiteDocument().getDataSourceMappings().add(dsm);
      return sitesRepository.save(site);
    }
  }

  private UUID processSiteWithDeletedMappings(UUID siteId) {
    log.debug("PROCESSING_SITE_WITH_DELETED_MAPPING {}", siteId);
    Sites site = sitesRepository.findBySiteIdUnfiltered(siteId.toString());
    SiteMappings siteMappings = siteMappingsRepository.findBySiteIdWithDeleted(siteId.toString());
    if (siteMappings == null || site == null) {
      log.warn("SITE_MISSING_DATA {}", siteId);
      return siteId;
    }
    SiteMappingDocument siteMappingDocument = siteMappings.getSiteMappingDocument();
    List<DataSourceMapping> mappings = new ArrayList<>();

    // LM_SITE
    DataSourceMapping lmSiteMapping = new DataSourceMapping();
    lmSiteMapping.setSystemName(ApplicationMapping.LM_SITE_SYSTEM_NAME);
    lmSiteMapping.setSystemId(siteMappingDocument.getLabyrinthSiteId().toString());
    mappings.add(lmSiteMapping);

    // DCGO
    if (siteMappingDocument.getDcgoId() != null) {
      DataSourceMapping dcgoSiteMapping = new DataSourceMapping();
      dcgoSiteMapping.setSystemName(ApplicationMapping.DCGO_SYSTEM_NAME);
      dcgoSiteMapping.setSystemId(siteMappingDocument.getDcgoId());
      mappings.add(dcgoSiteMapping);
    }

    // DDW
    if (siteMappingDocument.getDdwHerdId() != null) {
      DataSourceMapping ddwSiteMapping = new DataSourceMapping();
      ddwSiteMapping.setSystemName(ApplicationMapping.DDW_SYSTEM_NAME);
      ddwSiteMapping.setSystemId(siteMappingDocument.getDdwHerdId());
      mappings.add(ddwSiteMapping);
    }

    // MAX
    if (siteMappingDocument.getMaxSiteId() != null) {
      DataSourceMapping maxSiteMapping = new DataSourceMapping();
      maxSiteMapping.setSystemName(ApplicationMapping.MAX_SYSTEM_NAME);
      maxSiteMapping.setSystemId(siteMappingDocument.getMaxSiteId().toString());
      mappings.add(maxSiteMapping);
    }

    // MilkProcessor
    if (siteMappingDocument.getMilkProcessorId() != null) {
      DataSourceMapping mpSiteMapping = new DataSourceMapping();
      mpSiteMapping.setSystemName(ApplicationMapping.MP_SYSTEM_NAME);
      mpSiteMapping.setSystemId(siteMappingDocument.getMilkProcessorId());
      mappings.add(mpSiteMapping);
    }

    site.getSiteDocument().setDataSourceMappings(mappings);
    sitesRepository.save(site);
    return siteId;
  }

  public List<String> fixSitesVisitReferenceWithSiteIds(List<String> siteIds) {
    List<Sites> sites = sitesRepository.findBySiteIds(siteIds);
    fixVisitsReference(sites);
    return ids;
  }
}
