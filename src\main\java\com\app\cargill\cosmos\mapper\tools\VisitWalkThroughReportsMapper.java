/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.model.tools.VisitWalkThroughReportsCosmos;
import com.app.cargill.cosmos.model.tools.WalkThroughHerdFinalObservationsCosmos;
import com.app.cargill.cosmos.model.tools.WalkThroughReportCosmos;
import com.app.cargill.document.VisitWalkThroughReports;
import com.app.cargill.document.WalkThroughHerdFinalObservations;
import com.app.cargill.document.WalkThroughReport;
import java.time.Instant;
import java.util.UUID;

public class VisitWalkThroughReportsMapper {

  private VisitWalkThroughReportsMapper() {}

  public static VisitWalkThroughReports map(VisitWalkThroughReportsCosmos input) {

    CosmosToModelMapper<WalkThroughHerdFinalObservationsCosmos, WalkThroughHerdFinalObservations>
        observationsMapper =
            source ->
                WalkThroughHerdFinalObservations.builder()
                    .id(UUID.fromString(source.getId()))
                    .createUser(source.getCreateUser())
                    .isDeleted(source.isDeleted())
                    .lastModifyUser(source.getLastModifyUser())
                    .createTimeUtc(source.getCreateTimeUtc())
                    .lastModifiedTimeUtc(
                        source.getLastModifiedTimeUtc() != null
                            ? source.getLastModifiedTimeUtc().getDate()
                            : Instant.MIN)
                    .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                    .isNew(source.isNew())
                    .positiveTrend(source.getPositiveTrend())
                    .opportunities(source.getOpportunities())
                    .comment(source.getComment())
                    .build();

    CosmosToModelMapper<WalkThroughReportCosmos, WalkThroughReport> itemMapper =
        source ->
            WalkThroughReport.builder()
                .penId(source.getPenId() != null ? UUID.fromString(source.getPenId()) : null)
                .penName(source.getPenName())
                .quality(source.getQuality())
                .isToolItemNew(source.getIsToolItemNew())
                .waterQuality(source.getWaterQuality())
                .beddingQuality(source.getBeddingQuality())
                .hockAbrasion(source.getHockAbrasion())
                .hockAbrasionGoal(source.getHockAbrasionGoal())
                .uterineDischarge(source.getUterineDischarge())
                .uterineDischargeGoal(source.getUterineDischargeGoal())
                .nasalDischarge(source.getNasalDischarge())
                .nasalDischargeGoal(source.getNasalDischargeGoal())
                .cowComfort(source.getCowComfort())
                .cowComfortGoal(source.getCowComfortGoal())
                .manureScoreComment(source.getManureScoreComment())
                .appearance(source.getAppearance())
                .rumenFill(source.getRumenFill())
                .beddingDepth(source.getBeddingDepth())
                .positiveTrends(source.getPositiveTrends())
                .opportunities(source.getOpportunities())
                .comments(source.getComments())
                .ruminationChewing(source.getRuminationChewing())
                .ruminationChewingGoal(source.getRuminationChewingGoal())
                .chewsPerCud(source.getChewsPerCud())
                .chewsPerCudGoal(source.getChewsPerCudGoal())
                .manureScore(source.getManureScore())
                .manureScoreGoal(source.getManureScoreGoal())
                .locomotionScore(source.getLocomotionScore())
                .locomotionScoreGoal(source.getLocomotionScoreGoal())
                .bodyConditionScore(source.getBodyConditionScore())
                .bodyConditionScoreGoal(source.getBodyConditionScoreGoal())
                .build();

    CosmosToModelMapper<VisitWalkThroughReportsCosmos, VisitWalkThroughReports> mapper =
        source ->
            VisitWalkThroughReports.builder()
                .id(UUID.fromString(source.getId()))
                .createUser(source.getCreateUser())
                .isDeleted(source.isDeleted())
                .lastModifyUser(source.getLastModifyUser())
                .createTimeUtc(source.getCreateTimeUtc())
                .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                .isNew(source.isNew())
                .visitId(UUID.fromString(source.getVisitId()))
                .pens(
                    source.getPens() != null
                        ? source.getPens().stream().map(itemMapper::map).toList()
                        : null)
                .finalObservations(
                    source.getFinalObservations() != null
                        ? observationsMapper.map(source.getFinalObservations())
                        : null)
                .build();

    return mapper.map(input);
  }
}
