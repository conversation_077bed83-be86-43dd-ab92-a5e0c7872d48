/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.mapper.tools.BodyConditionToolMapper;
import com.app.cargill.cosmos.mapper.tools.CalfHeiferScorecardMapper;
import com.app.cargill.cosmos.mapper.tools.CudChewingToolMapper;
import com.app.cargill.cosmos.mapper.tools.HeatStressToolMapper;
import com.app.cargill.cosmos.mapper.tools.LocomotionToolMapper;
import com.app.cargill.cosmos.mapper.tools.ManureScreenerToolMapper;
import com.app.cargill.cosmos.mapper.tools.MetabolicIncidenceToolMapper;
import com.app.cargill.cosmos.mapper.tools.MilkSoldEvaluationToolMapper;
import com.app.cargill.cosmos.mapper.tools.PenTimeBudgetToolMapper;
import com.app.cargill.cosmos.mapper.tools.PileAndBunkerToolMapper;
import com.app.cargill.cosmos.mapper.tools.ReadyToMilkToolMapper;
import com.app.cargill.cosmos.mapper.tools.ReportTypeMapper;
import com.app.cargill.cosmos.mapper.tools.RevenueInputsMapper;
import com.app.cargill.cosmos.mapper.tools.RoboticMilkEvaluationToolMapper;
import com.app.cargill.cosmos.mapper.tools.RumenFillToolMapper;
import com.app.cargill.cosmos.mapper.tools.RumenHealthManureScoreToolMapper;
import com.app.cargill.cosmos.mapper.tools.RumenHealthTMRParticleScoreToolMapper;
import com.app.cargill.cosmos.mapper.tools.RumenHealthToolMapper;
import com.app.cargill.cosmos.mapper.tools.ScorecardMapper;
import com.app.cargill.cosmos.mapper.tools.UrinePHToolMapper;
import com.app.cargill.cosmos.mapper.tools.VisitWalkThroughReportsMapper;
import com.app.cargill.cosmos.model.VisitCosmos;
import com.app.cargill.cosmos.repo.VisitsCosmosRepository;
import com.app.cargill.document.RumenHealthTMRParticleScoreTool;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.VisitsRepository;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class VisitsMigration implements CosmosDataMigration {

  private final VisitsCosmosRepository cosmosRepository;
  private final VisitsRepository visitsRepository;

  Integer count = 0;

  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult("Visits");
    try {
      List<Visits> visitsList = fetchAll(migrationResult);
      visitsRepository.saveAll(visitsList);
      log.info("Visits migration completed. {} records copied to PostgresSQL", visitsList.size());
    } catch (Exception e) {
      log.error("Error occurred during Visits migration", e);
    }
    return CompletableFuture.completedFuture(migrationResult);
  }

  public Flux<Visits> moveRecords(String accountId) {
    Flux<Visits> cosmosRecords =
        processCosmosFlux(
            cosmosRepository.findByCustomerId(accountId).delayElements(Duration.ofMillis(200)),
            new AtomicInteger(0));
    return processRecords(cosmosRecords);
  }

  public CompletableFuture<MigrationResult> fixAllPensTmrPenId() {
    log.debug("Starting fixPenId for visits");
    Flux<Visits> visitsList = processCosmosFlux(cosmosRepository.findAll(), new AtomicInteger(0));
    visitsList
        .filter(
            cv ->
                cv.getVisitDocument().getTmrParticleScore() != null
                    && !cv.getVisitDocument().getTmrParticleScore().getTmrScores().isEmpty())
        .flatMap(
            visit ->
                Mono.fromCallable(
                        () ->
                            Optional.ofNullable(
                                visitsRepository.findByVisitId(
                                    visit.getVisitDocument().getId().toString())))
                    .subscribeOn(Schedulers.boundedElastic())
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(dbVisit -> updateTmrPen(visit, dbVisit)))
        .filter(Optional::isPresent)
        .map(Optional::get)
        .onErrorContinue((t, o) -> log.error("FIX_PEN_ID_MIGRATION_PROCESS {}", o, t))
        .doOnComplete(() -> log.debug("FIX_PEN_ID_COMPLETE"))
        .subscribe();
    return CompletableFuture.completedFuture(new MigrationResult("FIX_PEN_ID"));
  }

  private Optional<Visits> updateTmrPen(Visits cosmosVisit, Visits dbVisit) {
    RumenHealthTMRParticleScoreTool cosmosTmrTool =
        cosmosVisit.getVisitDocument().getTmrParticleScore();
    RumenHealthTMRParticleScoreTool dbTmrTool = dbVisit.getVisitDocument().getTmrParticleScore();
    if (cosmosTmrTool == null
        || dbTmrTool == null
        || cosmosTmrTool.getTmrScores().isEmpty()
        || dbTmrTool.getTmrScores().stream().noneMatch(score -> score.getPenId() == null)) {
      return Optional.empty();
    }
    dbTmrTool.setTmrScores(cosmosTmrTool.getTmrScores());
    log.debug("UPDATE_PEN_ID {}", dbVisit.getVisitDocument().getId());
    return Optional.of(visitsRepository.save(dbVisit));
  }

  private List<Visits> fetchAll(MigrationResult migrationResult) {
    // Fetch data from CosmosDB
    AtomicInteger failedRecords = new AtomicInteger(0);
    Flux<Visits> visitsList = processCosmosFlux(cosmosRepository.findAll(), failedRecords);
    List<Visits> result = processRecords(visitsList).collectList().block();
    assert result != null;
    migrationResult.setSucceeded(result.size());
    migrationResult.setFailed(failedRecords.get());
    return result;
  }

  private Flux<Visits> processCosmosFlux(
      Flux<VisitCosmos> cosmosFlux, AtomicInteger failedDocuments) {
    return cosmosFlux
        .delayElements(Duration.ofMillis(200))
        .map(visitsMapper::map)
        .map(
            document -> {
              Visits visit = new Visits(document);
              visit.setLocalId(document.getId().toString());
              return visit;
            })
        .onErrorContinue(
            (throwable, object) -> {
              failedDocuments.incrementAndGet();
              log.error("Error during Visits migration", throwable);
            });
  }

  private Flux<Visits> processRecords(Flux<Visits> records) {
    return records
        .flatMap(
            visit ->
                Mono.fromCallable(
                        () -> {
                          try {
                            Visits existingRecord =
                                visitsRepository.findByVisitId(
                                    visit.getVisitDocument().getId().toString());
                            return Objects.requireNonNullElseGet(
                                existingRecord, () -> visitsRepository.save(visit));
                          } catch (Exception e) {
                            throw new MigrationException("Error during Visits process", e);
                          }
                        })
                    .subscribeOn(Schedulers.boundedElastic()))
        .onErrorContinue(
            (t, o) -> {
              log.error("Error with object: {}", o);
              log.error("Error during Visit save in DB", t);
            });
  }

  private final CosmosToModelMapper<VisitCosmos, VisitDocument> visitsMapper =
      source ->
          VisitDocument.builder()
              .id(UUID.fromString(source.getId()))
              .createUser(source.getCreateUser())
              .isDeleted(source.isDeleted())
              .lastModifyUser(source.getLastModifyUser())
              .createTimeUtc(source.getCreateTimeUtc())
              .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
              .lastSyncTimeUtc(source.getLastSyncTimeUtc())
              .isNew(source.isNew())
              .customerId(UUID.fromString(source.getCustomerId()))
              .siteId(UUID.fromString(source.getSiteId()))
              .visitDate(source.getVisitDate())
              .firstName(source.getFirstName())
              .lastName(source.getLastName())
              .status(source.getStatus())
              .selected(source.isSelected())
              .visitName(source.getVisitName())
              .formattedCreationDate(source.getFormattedCreationDate())
              .isVisitAutoPublished(source.isVisitAutoPublished())
              .needsSync(source.isNeedsSync())
              .cudChewing(
                  source.getCudChewing() != null
                      ? CudChewingToolMapper.map(source.getCudChewing())
                      : null)
              .heatStress(
                  source.getHeatStress() != null
                      ? HeatStressToolMapper.map(source.getHeatStress())
                      : null)
              .forageAuditScorecard(
                  source.getForageAuditScorecard() != null
                      ? ScorecardMapper.map(source.getForageAuditScorecard())
                      : null)
              .pileAndBunker(
                  source.getPileAndBunker() != null
                      ? PileAndBunkerToolMapper.map(source.getPileAndBunker())
                      : null)
              .rumenHealth(
                  source.getRumenHealth() != null
                      ? RumenHealthToolMapper.map(source.getRumenHealth())
                      : null)
              .tmrParticleScore(
                  source.getTmrParticleScore() != null
                      ? RumenHealthTMRParticleScoreToolMapper.map(source.getTmrParticleScore())
                      : null)
              .rumenHealthManureScore(
                  source.getRumenHealthManureScore() != null
                      ? RumenHealthManureScoreToolMapper.map(source.getRumenHealthManureScore())
                      : null)
              .locomotionScore(
                  source.getLocomotionScore() != null
                      ? LocomotionToolMapper.map(source.getLocomotionScore())
                      : null)
              .bodyCondition(
                  source.getBodyCondition() != null
                      ? BodyConditionToolMapper.map(source.getBodyCondition())
                      : null)
              .revenue(
                  source.getRevenue() != null ? RevenueInputsMapper.map(source.getRevenue()) : null)
              .metabolicIncidence(
                  source.getMetabolicIncidence() != null
                      ? MetabolicIncidenceToolMapper.map(source.getMetabolicIncidence())
                      : null)
              .penTimeBudgetTool(
                  source.getPenTimeBudgetTool() != null
                      ? PenTimeBudgetToolMapper.map(source.getPenTimeBudgetTool())
                      : null)
              .selectedCurrency(source.getSelectedCurrency())
              .walkThroughReports(
                  source.getWalkThroughReports() != null
                      ? VisitWalkThroughReportsMapper.map(source.getWalkThroughReports())
                      : null)
              .readyToMilk(
                  source.getReadyToMilk() != null
                      ? ReadyToMilkToolMapper.map(source.getReadyToMilk())
                      : null)
              .milkSoldEvaluation(
                  source.getMilkSoldEvaluation() != null
                      ? MilkSoldEvaluationToolMapper.map(source.getMilkSoldEvaluation())
                      : null)
              .urinePHTool(
                  source.getUrinePHTool() != null
                      ? UrinePHToolMapper.map(source.getUrinePHTool())
                      : null)
              .calfHeiferScorecard(
                  source.getCalfHeiferScorecard() != null
                      ? CalfHeiferScorecardMapper.map(source.getCalfHeiferScorecard())
                      : null)
              .roboticMilkEvaluation(
                  source.getRoboticMilkEvaluation() != null
                      ? RoboticMilkEvaluationToolMapper.map(source.getRoboticMilkEvaluation())
                      : null)
              .manureScreenerTool(
                  source.getManureScreenerTool() != null
                      ? ManureScreenerToolMapper.map(source.getManureScreenerTool())
                      : null)
              .rumenFillManureScore(
                  source.getRumenFillManureScore() != null
                      ? RumenFillToolMapper.map(source.getRumenFillManureScore())
                      : null)
              .reportType(
                  source.getReportType() != null
                      ? source.getReportType().stream().map(ReportTypeMapper::map).toList()
                      : null)
              .visitPublishedDateTimeUtc(source.getVisitPublishedDateTimeUtc())
              .mobileLastUpdatedTime(
                  source.getVisitPublishedDateTimeUtc() != null
                      ? source.getVisitPublishedDateTimeUtc()
                      : source.getLastModifiedTimeUtc().getDate())
              .build();

  @Override
  @Async
  public CompletableFuture<MigrationResult> migrationFix(String fixesToRun) {

    if (fixesToRun != null
        && (fixesToRun.equals(MigrationFix.VISITS.toString())
            || fixesToRun.equals(MigrationFix.ALL.toString()))) {
      List<VisitCosmos> cosmosVisits =
          Objects.requireNonNullElseGet(
              cosmosRepository.findByRumenHealthManureScore().collectList().block(),
              ArrayList::new);
      log.info(
          "Visits Fetched from cosmos where RumenHealth manure Score is not null: "
              + cosmosVisits.size());
      List<Visits> postgresVisits = visitsRepository.findAll();
      log.info("Visits Fetched from Postgres :" + postgresVisits.size());
      List<Visits> visitsToSave = new ArrayList<>();
      cosmosVisits.stream()
          .forEach(
              visitInCosmos ->
                  postgresVisits.parallelStream()
                      .forEach(
                          visitInPostgres -> {
                            if (visitInPostgres
                                .getVisitDocument()
                                .getId()
                                .toString()
                                .equals(visitInCosmos.getId())) {
                              count++;
                              visitInPostgres
                                  .getVisitDocument()
                                  .setRumenHealthManureScore(
                                      RumenHealthManureScoreToolMapper.map(
                                          visitInCosmos.getRumenHealthManureScore()));
                              visitsToSave.add(visitInPostgres);
                            }
                          }));
      visitsRepository.saveAllAndFlush(visitsToSave);
      log.info("RECORDS UPDATED FOR RUMEN HEALTH MANURE SCORE : " + count);

      log.info("MIGRATION FIX COMPLETED");
    }
    return CompletableFuture.completedFuture(new MigrationResult("Data Fixed", count, 0));
  }
}
