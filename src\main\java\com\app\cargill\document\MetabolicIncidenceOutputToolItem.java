/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MetabolicIncidenceOutputToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("RetainedPlacentaGoal")
  public Double retainedPlacentaGoal;

  @JsonProperty("MetritisGoal")
  public Double metritisGoal;

  @JsonProperty("DisplacedAbomasumGoal")
  public Double displacedAbomasumGoal;

  @JsonProperty("KetosisGoal")
  public Double ketosisGoal;

  @JsonProperty("MilkFeverGoal")
  public Double milkFeverGoal;

  @JsonProperty("DeathLossGoal")
  public Double deathLossGoal;

  @JsonProperty("DystociaGoal")
  public Double dystociaGoal;

  @JsonProperty("RetainedPlacentaIncidencePercent")
  public Double retainedPlacentaIncidencePercent;

  @JsonProperty("MetritisIncidencePercent")
  public Double metritisIncidencePercent;

  @JsonProperty("DisplacedAbomasumIncidencePercent")
  public Double displacedAbomasumIncidencePercent;

  @JsonProperty("KetosisIncidencePercent")
  public Double ketosisIncidencePercent;

  @JsonProperty("MilkFeverIncidencePercent")
  public Double milkFeverIncidencePercent;

  @JsonProperty("DystociaIncidencePercent")
  public Double dystociaIncidencePercent;

  @JsonProperty("DeathLossIncidencePercent")
  public Double deathLossIncidencePercent;

  @JsonProperty("RetainedPlacentaIncidenceDifference")
  public Double retainedPlacentaIncidenceDifference;

  @JsonProperty("MetritisIncidenceDifference")
  public Double metritisIncidenceDifference;

  @JsonProperty("DisplacedAbomasumIncidenceDifference")
  public Double displacedAbomasumIncidenceDifference;

  @JsonProperty("KetosisIncidenceDifference")
  public Double ketosisIncidenceDifference;

  @JsonProperty("MilkFeverIncidenceDifference")
  public Double milkFeverIncidenceDifference;

  @JsonProperty("DystociaIncidenceDifference")
  public Double dystociaIncidenceDifference;

  @JsonProperty("DeathLossIncidenceDifference")
  public Double deathLossIncidenceDifference;

  @JsonProperty("RetainedPlacentaMilkLoss")
  public Integer retainedPlacentaMilkLoss;

  @JsonProperty("MetritisMilkLoss")
  public Integer metritisMilkLoss;

  @JsonProperty("DisplacedAbomasumMilkLoss")
  public Integer displacedAbomasumMilkLoss;

  @JsonProperty("KetosisMilkLoss")
  public Integer ketosisMilkLoss;

  @JsonProperty("MilkFeverMilkLoss")
  public Integer milkFeverMilkLoss;

  @JsonProperty("DystociaMilkLoss")
  public Integer dystociaMilkLoss;

  @JsonProperty("MetritisIncreasedDaysOpen")
  public Integer metritisIncreasedDaysOpen;

  @JsonProperty("DisplacedAbomasumIncreasedDaysOpen")
  public Integer displacedAbomasumIncreasedDaysOpen;

  @JsonProperty("KetosisIncreasedDaysOpen")
  public Integer ketosisIncreasedDaysOpen;

  @JsonProperty("MilkFeverIncreasedDaysOpen")
  public Integer milkFeverIncreasedDaysOpen;

  @JsonProperty("RetainedPlacentaIncreasedDaysOpen")
  public Integer retainedPlacentaIncreasedDaysOpen;

  @JsonProperty("DystociaIncreasedDaysOpen")
  public Integer dystociaIncreasedDaysOpen;

  @JsonProperty("MetritisTreatmentCost")
  public Integer metritisTreatmentCost;

  @JsonProperty("DisplacedAbomasumTreatmentCost")
  public Integer displacedAbomasumTreatmentCost;

  @JsonProperty("KetosisTreatmentCost")
  public Integer ketosisTreatmentCost;

  @JsonProperty("MilkFeverTreatmentCost")
  public Integer milkFeverTreatmentCost;

  @JsonProperty("RetainedPlacentaTreatmentCost")
  public Integer retainedPlacentaTreatmentCost;

  @JsonProperty("DystociaTreatmentCost")
  public Integer dystociaTreatmentCost;

  @JsonProperty("MilkLossTotalLosses")
  public Integer milkLossTotalLosses;

  @JsonProperty("IncreasedDaysOpenTotalLosses")
  public Integer increasedDaysOpenTotalLosses;

  @JsonProperty("TreatmentCostTotalLosses")
  public Integer treatmentCostTotalLosses;

  @JsonProperty("RetainedPlacentaTotalCost")
  public Integer retainedPlacentaTotalCost;

  @JsonProperty("MetritisTotalCost")
  public Integer metritisTotalCost;

  @JsonProperty("DisplacedAbomasumTotalCost")
  public Integer displacedAbomasumTotalCost;

  @JsonProperty("KetosisTotalCost")
  public Integer ketosisTotalCost;

  @JsonProperty("MilkFeverTotalCost")
  public Integer milkFeverTotalCost;

  @JsonProperty("DystociaTotalCost")
  public Integer dystociaTotalCost;

  @JsonProperty("DeathLossTotalCost")
  public Integer deathLossTotalCost;

  @JsonProperty("TotalCost")
  public Integer totalCost;

  @JsonProperty("RetainedPlacentaCostPerCow")
  public Double retainedPlacentaCostPerCow;

  @JsonProperty("MetritisCostPerCow")
  public Double metritisCostPerCow;

  @JsonProperty("DisplacedAbomasumCostPerCow")
  public Double displacedAbomasumCostPerCow;

  @JsonProperty("KetosisCostPerCow")
  public Double ketosisCostPerCow;

  @JsonProperty("MilkFeverCostPerCow")
  public Double milkFeverCostPerCow;

  @JsonProperty("DystociaCostPerCow")
  public Double dystociaCostPerCow;

  @JsonProperty("DeathLossCostPerCow")
  public Double deathLossCostPerCow;

  @JsonProperty("TotalCostPerCow")
  public Double totalCostPerCow;

  @JsonProperty("ToolStatus")
  public ToolStatuses toolStatus;
}
