/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.dto.UserActivityLogDto;
import com.app.cargill.dto.UserActivityLogExportDto;
import com.app.cargill.dto.UserActivityLogResponseDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.UserActivityLog;
import com.app.cargill.repository.UserActivityLogRepository;
import com.app.cargill.service.IUserActivityLogService;
import jakarta.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserActivityLogServiceImpl implements IUserActivityLogService {

  private final UserActivityLogRepository userActivityLogRepository;
  private final ModelMapper modelMapper;

  @Override
  public UserActivityLogResponseDto logActivity(UserActivityLogDto activityLogDto) {
    log.debug("Logging user activity: {}", activityLogDto.getEventName());

    UserActivityLog activityLog = modelMapper.map(activityLogDto, UserActivityLog.class);
    UserActivityLog savedLog = userActivityLogRepository.save(activityLog);

    log.info("User activity logged successfully with ID: {}", savedLog.getId());
    return modelMapper.map(savedLog, UserActivityLogResponseDto.class);
  }

  @Override
  public List<UserActivityLogResponseDto> logActivitiesBatch(
      List<UserActivityLogDto> activityLogDtos) {
    log.debug("Logging batch of {} user activities", activityLogDtos.size());

    List<UserActivityLog> activityLogs =
        activityLogDtos.stream()
            .map(dto -> modelMapper.map(dto, UserActivityLog.class))
            .collect(Collectors.toList());

    List<UserActivityLog> savedLogs = userActivityLogRepository.saveAll(activityLogs);

    log.info("Batch of {} user activities logged successfully", savedLogs.size());
    return savedLogs.stream()
        .map(log -> modelMapper.map(log, UserActivityLogResponseDto.class))
        .collect(Collectors.toList());
  }

  @Override
  @Transactional
  public List<UserActivityLogResponseDto> processEventsListIteratively(
      List<UserActivityLogDto> eventsList, HttpServletRequest request) {
    log.info("Starting iterative processing of {} events", eventsList.size());

    List<UserActivityLogResponseDto> savedEvents = new ArrayList<>();
    int processedCount = 0;

    for (UserActivityLogDto eventDto : eventsList) {
      try {
        processedCount++;
        log.debug(
            "Processing event {}/{}: {} for user {}",
            processedCount,
            eventsList.size(),
            eventDto.getEventName(),
            eventDto.getUsername());

        // Enrich with request information if not provided
        enrichEventFromRequest(eventDto, request);

        // Map DTO to entity
        UserActivityLog activityLog = modelMapper.map(eventDto, UserActivityLog.class);

        // Save individual event to database
        UserActivityLog savedLog = userActivityLogRepository.save(activityLog);

        // Map back to response DTO
        UserActivityLogResponseDto responseDto =
            modelMapper.map(savedLog, UserActivityLogResponseDto.class);
        savedEvents.add(responseDto);

        log.debug(
            "Successfully saved event {}/{} with ID: {}",
            processedCount,
            eventsList.size(),
            savedLog.getId());

      } catch (Exception e) {
        log.error(
            "Error processing event {}/{} ({}): {}",
            processedCount,
            eventsList.size(),
            eventDto.getEventName(),
            e.getMessage(),
            e);
        // Continue processing other events even if one fails
      }
    }

    log.info(
        "Completed iterative processing: {}/{} events successfully saved to database",
        savedEvents.size(),
        eventsList.size());

    return savedEvents;
  }

  /** Enrich event with request information if not already provided */
  private void enrichEventFromRequest(UserActivityLogDto eventDto, HttpServletRequest request) {
    if (request == null) {
      return;
    }

    if (eventDto.getIpAddress() == null || eventDto.getIpAddress().trim().isEmpty()) {
      eventDto.setIpAddress(getClientIpAddress(request));
    }

    if (eventDto.getUserAgent() == null || eventDto.getUserAgent().trim().isEmpty()) {
      eventDto.setUserAgent(request.getHeader("User-Agent"));
    }

    if (eventDto.getReferrerUrl() == null || eventDto.getReferrerUrl().trim().isEmpty()) {
      eventDto.setReferrerUrl(request.getHeader("Referer"));
    }
  }

  /** Get client IP address from request */
  private String getClientIpAddress(HttpServletRequest request) {
    String xForwardedFor = request.getHeader("X-Forwarded-For");
    if (xForwardedFor != null
        && !xForwardedFor.isEmpty()
        && !"unknown".equalsIgnoreCase(xForwardedFor)) {
      return xForwardedFor.split(",")[0].trim();
    }

    String xRealIp = request.getHeader("X-Real-IP");
    if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
      return xRealIp;
    }

    return request.getRemoteAddr();
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByUsername(
      String username, Pageable pageable) {
    log.debug("Fetching activity logs for username: {}", username);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByUsernameOrderByCreatedDateDesc(username, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByEventName(
      String eventName, Pageable pageable) {
    log.debug("Fetching activity logs for event name: {}", eventName);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByEventNameOrderByCreatedDateDesc(eventName, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByDateRange(
      Instant startDate, Instant endDate, Pageable pageable) {
    log.debug("Fetching activity logs for date range: {} to {}", startDate, endDate);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByDateRange(startDate, endDate, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByUsernameAndDateRange(
      String username, Instant startDate, Instant endDate, Pageable pageable) {
    log.debug(
        "Fetching activity logs for username: {} and date range: {} to {}",
        username,
        startDate,
        endDate);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByUsernameAndDateRange(
            username, startDate, endDate, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByEventNameAndDateRange(
      String eventName, Instant startDate, Instant endDate, Pageable pageable) {
    log.debug(
        "Fetching activity logs for event name: {} and date range: {} to {}",
        eventName,
        startDate,
        endDate);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByEventNameAndDateRange(
            eventName, startDate, endDate, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByPathPattern(
      String pathPattern, Pageable pageable) {
    log.debug("Fetching activity logs for path pattern: {}", pathPattern);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByPathContaining(pathPattern, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByAccountId(
      String accountId, Pageable pageable) {
    log.debug("Fetching activity logs for account ID: {}", accountId);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByAccountIdOrderByCreatedDateDesc(accountId, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsBySiteId(
      String siteId, Pageable pageable) {
    log.debug("Fetching activity logs for site ID: {}", siteId);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findBySiteIdOrderByCreatedDateDesc(siteId, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByFeatureUsed(
      String featureUsed, Pageable pageable) {
    log.debug("Fetching activity logs for feature used: {}", featureUsed);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByFeatureUsedOrderByCreatedDateDesc(featureUsed, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getActivityLogsByModuleName(
      String moduleName, Pageable pageable) {
    log.debug("Fetching activity logs for module name: {}", moduleName);

    Page<UserActivityLog> activityLogs =
        userActivityLogRepository.findByModuleNameOrderByCreatedDateDesc(moduleName, pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  @Override
  @Transactional(readOnly = true)
  public Map<String, Long> getEventNameAnalytics() {
    log.debug("Fetching event name analytics");

    List<Object[]> results = userActivityLogRepository.getEventNameAnalytics();
    return results.stream()
        .collect(Collectors.toMap(result -> (String) result[0], result -> (Long) result[1]));
  }

  @Override
  @Transactional(readOnly = true)
  public Map<String, Long> getPathAnalytics() {
    log.debug("Fetching path analytics");

    List<Object[]> results = userActivityLogRepository.getPathAnalytics();
    return results.stream()
        .collect(Collectors.toMap(result -> (String) result[0], result -> (Long) result[1]));
  }

  @Override
  @Transactional(readOnly = true)
  public Map<String, Long> getUserAnalytics() {
    log.debug("Fetching user analytics");

    List<Object[]> results = userActivityLogRepository.getUserAnalytics();
    return results.stream()
        .collect(Collectors.toMap(result -> (String) result[0], result -> (Long) result[1]));
  }

  @Override
  @Transactional(readOnly = true)
  public Map<String, Long> getDailyActivityAnalytics(Instant startDate, Instant endDate) {
    log.debug("Fetching daily activity analytics for date range: {} to {}", startDate, endDate);

    List<Object[]> results =
        userActivityLogRepository.getDailyActivityAnalytics(startDate, endDate);
    return results.stream()
        .collect(Collectors.toMap(result -> result[0].toString(), result -> (Long) result[1]));
  }

  @Override
  @Transactional(readOnly = true)
  public UserActivityLogExportDto prepareExportData(
      Instant startDate, Instant endDate, String username, String eventName, String accountId) {
    log.debug("Preparing export data for lastUpdatedTime: {} to endDate: {}", startDate, endDate);

    // Use the new method that filters by lastUpdatedTime (startDate is actually lastUpdatedTime in
    // this context)
    List<UserActivityLog> activityLogs =
        userActivityLogRepository.findByMultipleCriteriaForExportAfterLastUpdate(
            username, eventName, accountId, startDate, endDate);

    List<UserActivityLogResponseDto> responseDtos =
        activityLogs.stream()
            .map(log -> modelMapper.map(log, UserActivityLogResponseDto.class))
            .collect(Collectors.toList());

    String filterCriteria = buildFilterCriteria(username, eventName, accountId);

    return UserActivityLogExportDto.builder()
        .activityLogs(responseDtos)
        .fileName(
            "UserJourney_Analytics_"
                + startDate.toString().substring(0, 10)
                + "_to_"
                + endDate.toString().substring(0, 10)
                + ".xlsx")
        .exportDate(Instant.now())
        .totalRecords((long) responseDtos.size())
        .dateFrom(startDate)
        .dateTo(endDate)
        .filterCriteria(filterCriteria)
        .build();
  }

  @Override
  @Transactional(readOnly = true)
  public UserActivityLogResponseDto getActivityLogById(Long id) {
    log.debug("Fetching activity log by ID: {}", id);

    UserActivityLog activityLog =
        userActivityLogRepository
            .findById(id)
            .orElseThrow(() -> new NotFoundDEException("Activity log not found with ID: " + id));

    return modelMapper.map(activityLog, UserActivityLogResponseDto.class);
  }

  @Override
  public void deleteActivityLog(Long id) {
    log.debug("Deleting activity log with ID: {}", id);

    UserActivityLog activityLog =
        userActivityLogRepository
            .findById(id)
            .orElseThrow(() -> new NotFoundDEException("Activity log not found with ID: " + id));

    activityLog.setDeleted(true);
    userActivityLogRepository.save(activityLog);

    log.info("Activity log with ID: {} deleted successfully", id);
  }

  @Override
  @Transactional(readOnly = true)
  public Page<UserActivityLogResponseDto> getAllActivityLogs(Pageable pageable) {
    log.debug("Fetching all activity logs with pagination");

    Page<UserActivityLog> activityLogs = userActivityLogRepository.findAll(pageable);
    return activityLogs.map(log -> modelMapper.map(log, UserActivityLogResponseDto.class));
  }

  private String buildFilterCriteria(String username, String eventName, String accountId) {
    StringBuilder criteria = new StringBuilder();

    if (username != null && !username.trim().isEmpty()) {
      criteria.append("Username: ").append(username).append("; ");
    }
    if (eventName != null && !eventName.trim().isEmpty()) {
      criteria.append("Event: ").append(eventName).append("; ");
    }
    if (accountId != null && !accountId.trim().isEmpty()) {
      criteria.append("Account ID: ").append(accountId).append("; ");
    }

    return criteria.length() > 0 ? criteria.toString() : "No filters applied";
  }
}
