/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller.external;

import com.app.cargill.service.forecast.AccountRolesService;
import com.app.cargill.service.forecast.DairyForecastService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/dairyforecast")
@Tag(
    name = "DairyForecast Controller",
    description = "Endpoint for communication with DairyForecast service")
@RequiredArgsConstructor
@Slf4j
public class DairyForecastController {

  private final DairyForecastService dairyForecastService;
  private final AccountRolesService accountRolesService;

  @GetMapping
  public Flux<String> getRecords() {
    return dairyForecastService.getRecords();
  }

  @PostMapping("/sync-roles")
  public Mono<String> pushAccountsRolesDataToForecast() {
    return dairyForecastService.syncAccountsRoles(accountRolesService.getAllAccountsRoles());
  }
}
