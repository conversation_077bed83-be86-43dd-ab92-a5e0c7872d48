/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class PenTimeBudgetToolItemCosmos extends EditableToolPenEntityBaseCosmos {
  @JsonProperty("VisitForComparison")
  private String visitForComparison;

  @JsonProperty("SelectedRestingVisits")
  private List<String> selectedRestingVisits;

  @JsonProperty("SelectedMilkVisits")
  private List<String> selectedMilkVisits;

  @JsonProperty("StallsInPen")
  private Integer stallsInPen;

  @JsonProperty("WalkingTimeToParlor")
  private Double walkingTimeToParlor;

  @JsonProperty("TimeInParlor")
  private Double timeInParlor;

  @JsonProperty("WalkingTimeFromParlor")
  private Double walkingTimeFromParlor;

  @JsonProperty("StallsInParlor")
  private Double stallsInParlor;

  @JsonProperty("TimeInLockUp")
  private Double timeInLockUp;

  @JsonProperty("OtherNonRestTime")
  private Double otherNonRestTime;

  @JsonProperty("RestingRequirement")
  private Double restingRequirement;

  @JsonProperty("EatingTime")
  private Double eatingTime;

  @JsonProperty("DrinkingGroomingTime")
  private Double drinkingGroomingTime;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("Animals")
  private Double animals;

  @JsonProperty("MilkingFrequency")
  private Double milkingFrequency;
}
