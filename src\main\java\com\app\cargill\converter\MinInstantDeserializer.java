/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import java.time.Instant;
import java.time.format.DateTimeParseException;

public class MinInstantDeserializer extends JsonDeserializer<Object> {

  @Override
  public Instant deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    try {
      return Instant.parse(node.asText());
    } catch (DateTimeParseException e) {
      return Instant.MIN;
    } catch (Exception e) {
      return null;
    }
  }
}
