/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.repo;

import com.app.cargill.cosmos.model.SiteCosmos;
import com.azure.spring.data.cosmos.repository.CosmosRepository;
import com.azure.spring.data.cosmos.repository.Query;
import java.util.List;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SitesCosmosRepository extends CosmosRepository<SiteCosmos, String> {

  @Query("Select * from c where c.AccountId = @accountId")
  List<SiteCosmos> findByAccountId(@Param("accountId") String accountId);
}
