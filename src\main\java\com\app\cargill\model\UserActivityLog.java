/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "user_activity_log")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
public class UserActivityLog extends BaseEntity {

  @Column(name = "event_name", nullable = false)
  private String eventName;

  @Column(name = "path", nullable = false)
  private String path;

  @Column(name = "username", nullable = false)
  private String username;

  @Column(name = "user_id")
  private String userId;

  @Column(name = "session_id")
  private String sessionId;

  @Column(name = "ip_address")
  private String ipAddress;

  @Column(name = "user_agent")
  private String userAgent;

  @Column(name = "browser")
  private String browser;

  @Column(name = "operating_system")
  private String operatingSystem;

  @Column(name = "device_type")
  private String deviceType;

  @Column(name = "screen_resolution")
  private String screenResolution;

  @Column(name = "referrer_url")
  private String referrerUrl;

  @Column(name = "page_title")
  private String pageTitle;

  @Column(name = "action_type")
  private String actionType;

  @Column(name = "element_clicked")
  private String elementClicked;

  @Column(name = "form_data", columnDefinition = "TEXT")
  private String formData;

  @Column(name = "response_time_ms")
  private Long responseTimeMs;

  @Column(name = "error_message", columnDefinition = "TEXT")
  private String errorMessage;

  @Column(name = "http_status_code")
  private Integer httpStatusCode;

  @Column(name = "request_method")
  private String requestMethod;

  @Column(name = "request_size_bytes")
  private Long requestSizeBytes;

  @Column(name = "response_size_bytes")
  private Long responseSizeBytes;

  @Column(name = "time_on_page_seconds")
  private Long timeOnPageSeconds;

  @Column(name = "scroll_depth_percentage")
  private Double scrollDepthPercentage;

  @Column(name = "country")
  private String country;

  @Column(name = "region")
  private String region;

  @Column(name = "city")
  private String city;

  @Column(name = "timezone")
  private String timezone;

  @Column(name = "language")
  private String language;

  @Column(name = "account_id")
  private String accountId;

  @Column(name = "site_id")
  private String siteId;

  @Column(name = "visit_id")
  private String visitId;

  @Column(name = "feature_used")
  private String featureUsed;

  @Column(name = "module_name")
  private String moduleName;

  @Column(name = "last_visited")
  private Instant lastVisited;

  @Column(name = "additional_data", columnDefinition = "TEXT")
  private String additionalData;
}
