/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.document.RevenueInputToolItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;

@Getter
public class RevenueInputsCosmos extends EditableDocumentBaseCosmos {
  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("SelectedProcessor")
  private MilkProcessorListItemCosmos selectedProcessor;

  @JsonProperty("Items")
  private List<RevenueInputItemCosmos> items;

  @JsonProperty("ScenarioItems")
  private RevenueInputToolItem scenarioItems;
}
