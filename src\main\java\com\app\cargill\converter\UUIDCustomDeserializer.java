/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UUIDCustomDeserializer extends JsonDeserializer<UUID> {

  @Override
  public UUID deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);
    try {
      return UUID.fromString(node.asText());
    } catch (Exception e) {
      log.error("Error deserializing UUID", e);
      return UUID.randomUUID();
    }
  }
}
