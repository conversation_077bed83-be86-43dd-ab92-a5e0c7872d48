/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.document.DateEpoch;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.Instant;
import lombok.Getter;

@Getter
public class EditableToolPenEntityBaseCosmos {
  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("PenName")
  private String penName;
}
