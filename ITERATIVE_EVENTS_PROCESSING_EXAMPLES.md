# Iterative Events Processing - API Examples

This document provides examples for the new iterative events processing endpoint that accepts a list of events and processes them one by one.

## New Endpoint: `/log-events-list`

**POST** `/user-journey-analytics/log-events-list`

This endpoint:
1. **Accepts a list of events** in the request body
2. **Iterates through each event** individually 
3. **Saves each event one by one** to the database
4. **Provides detailed processing information** in the response
5. **Continues processing** even if individual events fail

## Example Request Payload

```json
[
  {
    "eventName": "SESSION_START",
    "path": "/login",
    "username": "<EMAIL>",
    "userId": "user_001",
    "sessionId": "sess_abc123",
    "actionType": "LOGIN",
    "featureUsed": "Authentication",
    "moduleName": "Security",
    "lastVisited": "2024-01-15T09:00:00Z",
    "browser": "Chrome 120.0",
    "operatingSystem": "Windows 10",
    "deviceType": "Desktop"
  },
  {
    "eventName": "PAGE_VIEW",
    "path": "/dashboard",
    "username": "<EMAIL>",
    "userId": "user_001", 
    "sessionId": "sess_abc123",
    "actionType": "NAVIGATION",
    "pageTitle": "Dashboard - Dairy Analytics",
    "featureUsed": "Dashboard",
    "moduleName": "Main Interface",
    "lastVisited": "2024-01-15T09:00:30Z",
    "timeOnPageSeconds": 45,
    "scrollDepthPercentage": 75.5
  },
  {
    "eventName": "BUTTON_CLICK",
    "path": "/reports/milk-production",
    "username": "<EMAIL>",
    "userId": "user_001",
    "sessionId": "sess_abc123",
    "actionType": "CLICK",
    "elementClicked": "btn-generate-report",
    "featureUsed": "Report Generation",
    "moduleName": "Reports",
    "lastVisited": "2024-01-15T09:02:15Z",
    "responseTimeMs": 1250
  },
  {
    "eventName": "FORM_SUBMIT",
    "path": "/settings/preferences",
    "username": "<EMAIL>",
    "userId": "user_001",
    "sessionId": "sess_abc123",
    "actionType": "SUBMIT",
    "elementClicked": "btn-save-settings",
    "formData": "{\"theme\":\"dark\",\"notifications\":true}",
    "featureUsed": "User Settings",
    "moduleName": "Account Management",
    "lastVisited": "2024-01-15T09:05:00Z",
    "httpStatusCode": 200
  },
  {
    "eventName": "DOWNLOAD",
    "path": "/reports/export",
    "username": "<EMAIL>",
    "userId": "user_001",
    "sessionId": "sess_abc123",
    "actionType": "DOWNLOAD",
    "elementClicked": "btn-download-excel",
    "featureUsed": "Data Export",
    "moduleName": "Reporting System",
    "lastVisited": "2024-01-15T09:07:30Z",
    "requestSizeBytes": 1024,
    "responseSizeBytes": 2048000
  }
]
```

## Example Response

```json
{
  "status": "SUCCESS",
  "message": "Processed 5 events iteratively, saved 5 to database",
  "data": {
    "totalEventsReceived": 5,
    "totalEventsSaved": 5,
    "processingStatus": "SUCCESS",
    "processingMethod": "ITERATIVE",
    "message": "Successfully iterated through 5 events and saved each to database",
    "savedEvents": [
      {
        "id": 1001,
        "eventName": "SESSION_START",
        "path": "/login",
        "username": "<EMAIL>",
        "userId": "user_001",
        "sessionId": "sess_abc123",
        "actionType": "LOGIN",
        "featureUsed": "Authentication",
        "moduleName": "Security",
        "lastVisited": "2024-01-15T09:00:00Z",
        "createdDate": "2024-01-15T09:00:01Z",
        "updatedDate": "2024-01-15T09:00:01Z"
      },
      {
        "id": 1002,
        "eventName": "PAGE_VIEW",
        "path": "/dashboard",
        "username": "<EMAIL>",
        "userId": "user_001",
        "sessionId": "sess_abc123",
        "actionType": "NAVIGATION",
        "pageTitle": "Dashboard - Dairy Analytics",
        "featureUsed": "Dashboard",
        "moduleName": "Main Interface",
        "lastVisited": "2024-01-15T09:00:30Z",
        "timeOnPageSeconds": 45,
        "scrollDepthPercentage": 75.5,
        "createdDate": "2024-01-15T09:00:31Z",
        "updatedDate": "2024-01-15T09:00:31Z"
      }
      // ... other saved events
    ]
  },
  "currentTimeStamp": "2024-01-15T09:08:00Z"
}
```

## Key Features

### 1. **Individual Processing**
- Each event is processed and saved individually
- Detailed logging for each event processing step
- Database transaction per event for reliability

### 2. **Error Resilience**
- If one event fails, processing continues with the next events
- Failed events are logged but don't stop the entire process
- Response shows exactly how many events were successfully saved

### 3. **Request Enrichment**
- Automatically enriches events with IP address, User-Agent, and Referer
- Only adds missing information, doesn't override provided data

### 4. **Detailed Response**
- Shows total events received vs. total events saved
- Includes all successfully saved events with their database IDs
- Processing method indicator ("ITERATIVE")

## Comparison with Batch Processing

| Feature | `/log-activities-batch` | `/log-events-list` |
|---------|------------------------|-------------------|
| Processing Method | Bulk database operation | Individual iteration |
| Error Handling | All-or-nothing | Continue on individual failures |
| Performance | Faster for large datasets | More resilient |
| Logging Detail | Batch-level logging | Per-event logging |
| Use Case | High-volume, reliable data | Mixed quality data streams |

## Testing Commands

### Curl Example
```bash
curl -X POST http://localhost:8080/user-journey-analytics/log-events-list \
  -H "Content-Type: application/json" \
  -d '[
    {
      "eventName": "PAGE_VIEW",
      "path": "/test-page",
      "username": "<EMAIL>",
      "lastVisited": "2024-01-15T10:00:00Z"
    },
    {
      "eventName": "BUTTON_CLICK",
      "path": "/test-page",
      "username": "<EMAIL>",
      "actionType": "CLICK",
      "elementClicked": "test-button",
      "lastVisited": "2024-01-15T10:01:00Z"
    }
  ]'
```

### Postman/HTTP Client
```
POST /user-journey-analytics/log-events-list
Content-Type: application/json

[
  {
    "eventName": "TEST_EVENT_1",
    "path": "/test",
    "username": "<EMAIL>",
    "lastVisited": "2024-01-15T10:00:00Z"
  },
  {
    "eventName": "TEST_EVENT_2", 
    "path": "/test",
    "username": "<EMAIL>",
    "lastVisited": "2024-01-15T10:01:00Z"
  }
]
```

## Use Cases

1. **Frontend Event Queues**: Process accumulated events from frontend queues
2. **Data Migration**: Import historical events from other systems
3. **Batch Analytics**: Process events collected from multiple sources
4. **Error Recovery**: Reprocess failed events individually
5. **Testing**: Easily test multiple scenarios with different event types

## Performance Considerations

- **Individual Transactions**: Each event gets its own database transaction
- **Memory Efficient**: Processes events one at a time, not loading all in memory
- **Logging Overhead**: More detailed logging per event
- **Network Efficiency**: Single HTTP request for multiple events

The iterative processing approach provides maximum reliability and detailed tracking for each event in your user journey analytics! 🔄📊
