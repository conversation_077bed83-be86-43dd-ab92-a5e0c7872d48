/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MilkProductionOutputs implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("RatioSNFPerButterfat")
  private Double ratioSNFPerButterfat;

  @JsonProperty("MaxAllowed")
  private Double maxAllowed;

  @JsonProperty("TotalFatProtein")
  private Double totalFatProtein;

  @JsonProperty("DairyEfficiency")
  private Double dairyEfficiency;

  @JsonProperty("ComponentEfficiency")
  private Double componentEfficiency;

  @JsonProperty("TotalRevenuePerLiter")
  private Double totalRevenuePerLiter;

  @JsonProperty("FeedCostPerLiter")
  private Double feedCostPerLiter;

  @JsonProperty("PurchasedFeedCostPerLiter")
  private Double purchasedFeedCostPerLiter;

  @JsonProperty("ConcentrateCostPerLiter")
  private Double concentrateCostPerLiter;

  @JsonProperty("ConcentrateCostPerKgBF")
  private Double concentrateCostPerKgBF;

  @JsonProperty("BFRevenue")
  private Double bfRevenue;

  @JsonProperty("ProteinRevenue")
  private Double proteinRevenue;

  @JsonProperty("OtherSolidsRevenue")
  private Double otherSolidsRevenue;

  @JsonProperty("DeductionsPricePerCowPerDay")
  private Double deductionsPricePerCowPerDay;

  @JsonProperty("SNFNonPayment")
  private Double snfNonPayment;

  @JsonProperty("TotalRevenuePricePerKgFat")
  private Double totalRevenuePricePerKgFat;

  @JsonProperty("TotalRevenueCowDay")
  private Double totalRevenueCowDay;

  @JsonProperty("UnderQuotaLostRevenuePerMonth")
  private Double underQuotaLostRevenuePerMonth;

  @JsonProperty("RofPerKgButterFat")
  private Double rofPerKgButterFat;

  @JsonProperty("ROF")
  private Double rof;
}
