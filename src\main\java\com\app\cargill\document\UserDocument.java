/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.MobileDeviceType;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class UserDocument {

  private UUID id;

  @JsonProperty("UserName")
  private String userName;

  @JsonProperty("CountryId")
  private Business countryId;

  @JsonProperty("SalesforceCountryId")
  private Integer salesforceCountryId;

  @JsonProperty("LastLoginDateTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastLoginDateTime;

  @JsonProperty("ApplicationVersion")
  private String applicationVersion;

  @JsonProperty("DeviceName")
  private MobileDeviceType deviceType; // ios android

  @JsonProperty("DeviceModel")
  private String deviceModel;

  @JsonProperty("DeviceId")
  private String deviceId;
}
