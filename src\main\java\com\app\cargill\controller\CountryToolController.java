/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.Business;
import com.app.cargill.dto.CountryToolDto;
import com.app.cargill.dto.CountryToolSaveDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.ICountryToolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/countryTools")
@Tag(
    name = "Country Tools Information Controller",
    description = "Country Tools Information Controller")
@RequiredArgsConstructor
public class CountryToolController extends BaseController {

  private final ICountryToolService countryToolService;

  @GetMapping("/paginated")
  @Operation(
      summary = "Get all Country Tools WRT User's Country",
      description = "This method will return all Country Tools list WRT to User's location")
  public ResponseEntity<ResponseEntityDto<Page<CountryToolDto>>> getAllCountryToolsPaginated(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", name = "sortBy") String sortBy,
      @RequestParam(
              name = "lastSyncTime",
              defaultValue = "${app.configurations.default-utc-timestamp}")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        countryToolService.getAllCountryToolsByCurrentLoggedInUserPaginated(
            page, size, sortBy, sorting, lastSyncTime));
  }

  @PostMapping
  @Operation(
      summary = "Saves countryTools list",
      description = "This method will Save country Tools list")
  public ResponseEntity<ResponseEntityDto<List<CountryToolDto>>> save(
      @RequestBody CountryToolSaveDto countryToolsDtoList) {

    return handleSuccessResponse(
        countryToolService.save(countryToolsDtoList.getCountryToolDtoList()));
  }

  @DeleteMapping("/id")
  @Operation(
      summary = "Deletes countryTool WRT ID",
      description = "This method will delete country Tool WRT ID")
  public ResponseEntity<ResponseEntityDto<CountryToolDto>> deleteById(
      @RequestParam(required = true, name = "id") UUID id) {

    return handleSuccessResponse(countryToolService.deleteById(id));
  }

  @DeleteMapping("/countryId")
  @Operation(
      summary = "Deletes countryTool WRT CountryID",
      description = "This method will delete country Tool WRT COUNTRY ID")
  public ResponseEntity<ResponseEntityDto<List<CountryToolDto>>> deleteByCountryId(
      @RequestParam(required = true, name = "countryId") Business countryId) {

    return handleSuccessResponse(countryToolService.deleteByCountryId(countryId));
  }
}
