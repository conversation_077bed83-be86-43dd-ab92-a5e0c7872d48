/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.NotificationDto;
import com.app.cargill.dto.ReadNotificationDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.INotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/notification")
@Tag(
    name = "Notification Information Controller",
    description = "Notification Information Controller")
@RequiredArgsConstructor
public class NotificationController extends BaseController {

  private final INotificationService notificationServiceImpl;

  @GetMapping("/paginated")
  @Operation(
      summary = "Get all Notifications based on current logged in User",
      description = "This method will return all Notifications based on current Logged in user")
  public ResponseEntity<ResponseEntityDto<Page<NotificationDto>>> getAllNotificationsPaginated(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", name = "sortBy") String sortBy,
      @RequestParam(
              name = "lastSyncTime",
              defaultValue = "${app.configurations.default-utc-timestamp}")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        notificationServiceImpl.getAllNotificationsPaginated(
            page, size, sortBy, sorting, lastSyncTime));
  }

  @PostMapping
  @Operation(summary = "Save Notification", description = "This api will save notifications")
  public ResponseEntity<ResponseEntityDto<NotificationDto>> save(
      @RequestBody NotificationDto notificationDto) {
    return handleSuccessResponse(notificationServiceImpl.save(notificationDto));
  }

  @PutMapping
  @Operation(
      summary = "Update notifications",
      description = "This api will Update Notifications WRT to notifications ID")
  public ResponseEntity<ResponseEntityDto<NotificationDto>> update(
      @RequestBody NotificationDto notificationDto) {
    return handleSuccessResponse(notificationServiceImpl.update(notificationDto));
  }

  @PostMapping("/read")
  @Operation(
      summary = "Mark Notifications as read",
      description = "This api will mark notifications as read")
  public ResponseEntity<ResponseEntityDto<ReadNotificationDto>> markAsRead(
      @RequestBody ReadNotificationDto notifications) {
    return handleSuccessResponse(notificationServiceImpl.markAsRead(notifications));
  }

  @GetMapping("/filteredNotifications")
  @Operation(
      summary = "Get Notification Ids which are accessible for the user",
      description = "This method will return Notification Ids which are accessible for the user")
  public ResponseEntity<ResponseEntityDto<List<String>>> getFilteredNotificationIds() {

    return handleSuccessResponse(notificationServiceImpl.getFilteredNotificationIds());
  }
}
