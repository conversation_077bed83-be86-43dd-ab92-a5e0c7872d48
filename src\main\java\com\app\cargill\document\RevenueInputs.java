/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class RevenueInputs extends EditableDocumentBase {

  @JsonProperty("SelectedProcessor")
  private MilkProcessorListItem selectedProcessor;

  @JsonProperty("Items")
  private List<RevenueInputItem> items;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("ScenarioItems")
  private RevenueInputToolItem scenarioItems;
}
