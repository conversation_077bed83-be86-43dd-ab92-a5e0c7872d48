# Excel Export Test Guide

## Quick Test Scenario

### Step 1: Log Some Test Activities

First, log a few test activities using the batch endpoint:

```bash
POST http://localhost:8080/user-journey-analytics/log-activities-batch
Content-Type: application/json

[
  {
    "eventName": "PAGE_VIEW",
    "path": "/dashboard",
    "username": "<EMAIL>",
    "lastVisited": "2024-01-15T10:00:00Z"
  },
  {
    "eventName": "BUTTON_CLICK", 
    "path": "/reports",
    "username": "<EMAIL>",
    "lastVisited": "2024-01-15T10:05:00Z"
  },
  {
    "eventName": "FORM_SUBMIT",
    "path": "/settings",
    "username": "<EMAIL>", 
    "lastVisited": "2024-01-15T10:10:00Z"
  },
  {
    "eventName": "FEATURE_ACCESS",
    "path": "/analytics",
    "username": "<EMAIL>",
    "lastVisited": "2024-01-15T10:15:00Z"
  },
  {
    "eventName": "DOWNLOAD",
    "path": "/reports/export",
    "username": "<EMAIL>",
    "lastVisited": "2024-01-15T10:20:00Z"
  }
]
```

### Step 2: Export to Excel

Now export the data to Excel using the lastUpdatedTime filter:

```bash
GET http://localhost:8080/user-journey-analytics/export/excel?lastUpdatedTime=2024-01-01T00:00:00Z
```

This will download an Excel file with **4 columns**:

| Event Name     | Path              | Username              | Last Visited        |
|----------------|-------------------|-----------------------|---------------------|
| PAGE_VIEW      | /dashboard        | <EMAIL> | 2024-01-15 10:00:00 |
| BUTTON_CLICK   | /reports          | <EMAIL> | 2024-01-15 10:05:00 |
| FORM_SUBMIT    | /settings         | <EMAIL> | 2024-01-15 10:10:00 |
| FEATURE_ACCESS | /analytics        | <EMAIL>   | 2024-01-15 10:15:00 |
| DOWNLOAD       | /reports/export   | <EMAIL>   | 2024-01-15 10:20:00 |

### Step 3: Test Incremental Export

Wait a few minutes, then log more activities:

```bash
POST http://localhost:8080/user-journey-analytics/log-activity
Content-Type: application/json

{
  "eventName": "SEARCH",
  "path": "/search",
  "username": "<EMAIL>",
  "lastVisited": "2024-01-15T10:30:00Z"
}
```

Now export again with a more recent lastUpdatedTime:

```bash
GET http://localhost:8080/user-journey-analytics/export/excel?lastUpdatedTime=2024-01-15T10:25:00Z
```

This should only return the new activity logged after 10:25:00.

## Key Benefits of 4-Field Export

1. **Simplified Analytics**: Focus on core user journey data
2. **Fast Processing**: Minimal data transfer and processing
3. **Tool Compatibility**: Easy to import into any analytics platform
4. **Clear Insights**: Event → Path → User → Time pattern is clear

## Analytics Use Cases

With these 4 fields, you can analyze:

- **User Flow**: Track how users navigate through the application
- **Feature Usage**: See which features (paths) are most accessed
- **User Behavior**: Identify power users vs casual users
- **Time Patterns**: Understand when users are most active
- **Event Distribution**: See which events are most common

## Sample Analytics Queries (in Excel/Analytics Tools)

1. **Most Popular Pages**: Count of events by Path
2. **Most Active Users**: Count of events by Username  
3. **Event Distribution**: Count by Event Name
4. **Hourly Activity**: Group Last Visited by hour
5. **User Journey**: Order events by Username and Last Visited

## Curl Commands for Testing

```bash
# Log test data
curl -X POST http://localhost:8080/user-journey-analytics/log-activity \
  -H "Content-Type: application/json" \
  -d '{
    "eventName": "PAGE_VIEW",
    "path": "/dashboard", 
    "username": "<EMAIL>",
    "lastVisited": "2024-01-15T10:00:00Z"
  }'

# Export to Excel
curl -X GET "http://localhost:8080/user-journey-analytics/export/excel?lastUpdatedTime=2024-01-01T00:00:00Z" \
  -H "Accept: application/octet-stream" \
  --output user_journey_analytics.xlsx
```

The exported Excel file will be clean, focused, and ready for analytics processing!
