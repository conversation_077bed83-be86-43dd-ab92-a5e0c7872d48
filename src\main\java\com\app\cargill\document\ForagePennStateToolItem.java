/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.SilageType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ForagePennStateToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Silage")
  private SilageType silage;

  @JsonProperty("SilageId")
  private UUID silageId;

  @JsonProperty("Top")
  private Double top;

  @JsonProperty("Mid1")
  private Double mid1;

  @JsonProperty("Mid2")
  private Double mid2;

  @JsonProperty("Tray")
  private Double tray;

  @JsonProperty("TotalScreenAmount")
  public Double totalScreenAmount;

  @JsonProperty("TopOnScreenPercentage")
  private Double topOnScreenPercentage;

  @JsonProperty("Mid1OnScreenPercentage")
  private Double mid1OnScreenPercentage;

  @JsonProperty("Mid2OnScreenPercentage")
  private Double mid2OnScreenPercentage;

  @JsonProperty("TrayOnScreenPercentage")
  private Double trayOnScreenPercentage;

  @JsonProperty("TopStdDevValue")
  public Double topStdDevValue;

  @JsonProperty("Mid1StdDevValue")
  public Double mid1StdDevValue;

  @JsonProperty("Mid2StdDevValue")
  public Double mid2StdDevValue;

  @JsonProperty("TrayStdDevValue")
  public Double trayStdDevValue;
}
