/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class RumenFillScoreToolItem extends EditableToolPenEntityBase {
  @JsonProperty("RumenFillScoreVisitsSelected")
  private List<UUID> rumenFillScoreVisitsSelected;

  @JsonProperty("RumenFillScores")
  private RumenHealthManureScores rumenFillScores;

  @JsonProperty("IsToolItemNew")
  private Boolean isToolItemNew;

  @JsonProperty("IsFirstTimeWithScore")
  private Boolean isFirstTimeWithScore;

  @JsonProperty("DaysInMilk")
  private Integer daysInMilk;
}
