/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdHealth extends HerdBase {

  @JsonProperty("DeathLoss")
  private String deathLoss;

  @JsonProperty("DeathLossLact1")
  private String deathLossLact1;

  @JsonProperty("DeathLossLact2")
  private String deathLossLact2;

  @JsonProperty("DeathLossLact3plus")
  private String deathLossLact3plus;

  @JsonProperty("DisplacedAbomasumIncidence")
  private String displacedAbomasumIncidence;

  @JsonProperty("DystociaIncidence")
  private String dystociaIncidence;

  @JsonProperty("KetosisIncidence")
  private String ketosisIncidence;

  @JsonProperty("MetritisIncidence")
  private String metritisIncidence;

  @JsonProperty("MilkFeverIncidence")
  private String milkFeverIncidence;

  @JsonProperty("RetainedPlacentaIncidence")
  private String retainedPlacentaIncidence;

  @JsonProperty("nrAnimals")
  private String nrAnimals;
}
