/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.tools;

import com.app.cargill.cosmos.mapper.CosmosToModelMapper;
import com.app.cargill.cosmos.mapper.UuidMapper;
import com.app.cargill.cosmos.model.tools.RumenHealthTMRParticleScoreToolCosmos;
import com.app.cargill.cosmos.model.tools.RumenHealthTMRParticleScoreToolItemCosmos;
import com.app.cargill.document.RumenHealthTMRParticleScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreToolItem;
import com.app.cargill.document.RumenHealthTMRParticleScoreToolItem.RumenHealthTMRParticleScoreToolItemBuilder;
import java.util.UUID;

public class RumenHealthTMRParticleScoreToolMapper {

  private RumenHealthTMRParticleScoreToolMapper() {}

  public static RumenHealthTMRParticleScoreTool map(RumenHealthTMRParticleScoreToolCosmos input) {

    CosmosToModelMapper<
            RumenHealthTMRParticleScoreToolItemCosmos, RumenHealthTMRParticleScoreToolItem>
        itemMapper =
            source -> {
              RumenHealthTMRParticleScoreToolItemBuilder<?, ?> toolItem =
                  RumenHealthTMRParticleScoreToolItem.builder()
                      .visitsSelected(
                          source.getVisitsSelected() != null
                              ? source.getVisitsSelected().stream().map(UUID::fromString).toList()
                              : null)
                      .isToolItemNew(source.getIsToolItemNew())
                      .topScaleAmountInGrams(source.getTopScaleAmountInGrams())
                      .topGoalMinimumPercent(source.getTopGoalMinimumPercent())
                      .topGoalMaximumPercent(source.getTopGoalMaximumPercent())
                      .topScreenTareAmountInGrams(source.getTopScreenTareAmountInGrams())
                      .mid1ScaleAmountInGrams(source.getMid1ScaleAmountInGrams())
                      .mid1GoalMinimumPercent(source.getMid1GoalMinimumPercent())
                      .mid1GoalMaximumPercent(source.getMid1GoalMaximumPercent())
                      .mid1ScreenTareAmountInGrams(source.getMid1ScreenTareAmountInGrams())
                      .mid2ScaleAmountInGrams(source.getMid2ScaleAmountInGrams())
                      .mid2GoalMinimumPercent(source.getMid2GoalMinimumPercent())
                      .mid2GoalMaximumPercent(source.getMid2GoalMaximumPercent())
                      .mid2ScreenTareAmountInGrams(source.getMid2ScreenTareAmountInGrams())
                      .trayScaleAmountInGrams(source.getTrayScaleAmountInGrams())
                      .trayGoalMinimumPercent(source.getTrayGoalMinimumPercent())
                      .trayGoalMaximumPercent(source.getTrayGoalMaximumPercent())
                      .trayScreenTareAmountInGrams(source.getTrayScreenTareAmountInGrams())
                      .toolStatus(source.getToolStatus())
                      .daysInMilk(source.getDaysInMilk())
                      .isFirstTimeWithScore(source.getIsFirstTimeWithScore())
                      .tmrScoreName(source.getTmrScoreName())
                      .penId(UuidMapper.getNullableUuid(source.getPenId()))
                      .penName(source.getPenName())
                      .createTimeUtc(source.getCreateTimeUtc());
              if (source.getTmrScoreId() != null) {
                toolItem.tmrScoreId(source.getTmrScoreId());
              }
              return toolItem.build();
            };

    CosmosToModelMapper<RumenHealthTMRParticleScoreToolCosmos, RumenHealthTMRParticleScoreTool>
        mapper =
            source ->
                RumenHealthTMRParticleScoreTool.builder()
                    .id(UUID.fromString(source.getId()))
                    .createUser(source.getCreateUser())
                    .isDeleted(source.isDeleted())
                    .lastModifyUser(source.getLastModifyUser())
                    .createTimeUtc(source.getCreateTimeUtc())
                    .lastModifiedTimeUtc(source.getLastModifiedTimeUtc().getDate())
                    .lastSyncTimeUtc(source.getLastSyncTimeUtc())
                    .isNew(source.isNew())
                    .visitId(UUID.fromString(source.getVisitId()))
                    .tmrScores(
                        source.getTmrScores() != null
                            ? source.getTmrScores().stream().map(itemMapper::map).toList()
                            : null)
                    .selectedScorer(source.getSelectedScorer())
                    .build();

    return mapper.map(input);
  }
}
