/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.cosmos.model.MilkProcessorCosmos;
import com.app.cargill.cosmos.repo.MilkProcessorsCosmosRepository;
import com.app.cargill.document.MilkProcessorsDocument;
import com.app.cargill.model.MilkProcessors;
import com.app.cargill.repository.MilkProcessorsRepository;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.PeriodType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class MilkProcessorsMigration implements CosmosDataMigration {

  private final MilkProcessorsCosmosRepository cosmosRepository;

  private final MilkProcessorsRepository dbRepository;

  @Override
  @Async
  public CompletableFuture<MigrationResult> moveAll() {
    MigrationResult migrationResult = new MigrationResult(MigrationType.MILK_PROCESSORS.name());

    DateTime start = DateTime.now();
    AtomicInteger failedItems = new AtomicInteger(0);
    Flux<MilkProcessors> mpFlux = fetchAll(failedItems);
    return processRecords(mpFlux, failedItems)
        .reduce(0, (accumulator, a) -> accumulator + 1)
        .map(
            v -> {
              DateTime end = DateTime.now();
              Duration duration = new Duration(start, end);
              migrationResult.setFailed(failedItems.get());
              migrationResult.setSucceeded(v);
              log.info(
                  "Time taken for MilkProcessors migration: {} start: {} end: {}",
                  duration.toPeriod().normalizedStandard(PeriodType.standard()),
                  start,
                  end);

              log.info("MilkProcessors migration completed. {}", migrationResult);
              return migrationResult;
            })
        .toFuture();
  }

  public Flux<MilkProcessors> moveRecords(String userId) {
    return processUserRecords(userId, new AtomicInteger(0));
  }

  @Override
  public MigrationType migrationType() {
    return MigrationType.MILK_PROCESSORS;
  }

  private Flux<MilkProcessors> processRecords(
      Flux<MilkProcessors> records, AtomicInteger failedItems) {
    return records
        .filterWhen(
            mp ->
                Mono.fromCallable(() -> checkIfExists(failedItems, mp))
                    .subscribeOn(Schedulers.boundedElastic()))
        .flatMap(
            mp ->
                Mono.fromCallable(() -> dbRepository.save(mp))
                    .subscribeOn(Schedulers.boundedElastic()))
        .onErrorContinue(
            (t, o) -> {
              failedItems.incrementAndGet();
              log.error("Error with object: {}", o);
              log.error("Error during MilkProcessors save in DB", t);
            });
  }

  private Flux<MilkProcessors> fetchAll(AtomicInteger failedItems) {
    return processCosmosFlux(cosmosRepository.findAll(), failedItems);
  }

  private Flux<MilkProcessors> processUserRecords(String userId, AtomicInteger failedItems) {
    // Unused
    Flux<MilkProcessors> cosmosRecords =
        processCosmosFlux(cosmosRepository.getMilkProcessorsByUserId(userId), failedItems);
    return processRecords(cosmosRecords, failedItems);
  }

  private Flux<MilkProcessors> processCosmosFlux(
      Flux<MilkProcessorCosmos> records, AtomicInteger failedItems) {
    return records
        .filter(this::filterCosmosData)
        .map(this::cosmosToDocumentMapper)
        .map(
            milkProcessorsDocument -> {
              MilkProcessors milkProcessor = new MilkProcessors(milkProcessorsDocument);
              milkProcessor.setLocalId(milkProcessorsDocument.getId().toString());
              return milkProcessor;
            })
        .onErrorContinue(
            (throwable, object) -> {
              failedItems.incrementAndGet();
              log.error("Error during MilkProcessors migration: {}", object);
              log.error(throwable.getMessage());
            });
  }

  private boolean filterCosmosData(MilkProcessorCosmos mpCosmos) {
    boolean result = true;

    if (mpCosmos.getUserId() == null || mpCosmos.getUserId().isBlank()) {
      log.warn("Missing userId for MilkProcessors. Skipping.");
      log.warn("{}", mpCosmos);
      return false;
    }

    return result;
  }

  private MilkProcessorsDocument cosmosToDocumentMapper(MilkProcessorCosmos mpCosmos) {
    MilkProcessorsDocument document = new MilkProcessorsDocument();
    document.setId(UUID.fromString(mpCosmos.getId()));
    document.setUserId(mpCosmos.getUserId());
    document.setComponentProcessors(mpCosmos.getComponentProcessors());
    document.setConcentrationProcessors(mpCosmos.getConcentrationProcessors());
    document.setDeleted(mpCosmos.isDeleted());

    return document;
  }

  private Boolean checkIfExists(AtomicInteger failedItems, MilkProcessors mp) {
    try {
      return dbRepository.findByDocumentId(mp.getMilkProcessorsDocument().getId().toString())
          == null;
    } catch (Exception e) {
      failedItems.incrementAndGet();
      log.error("Error with object: {}", mp);
      log.error("Error during MilkProcessors find in DB", e);
      return false;
    }
  }
}
