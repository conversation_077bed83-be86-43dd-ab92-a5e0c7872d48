/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model.tools;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class HeatStressToolCosmos extends EditableDocumentBaseCosmos {

  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("AvgMilkWeightInkg")
  private Double avgMilkWeightInkg;

  @JsonProperty("AvgDMIWeightInkg")
  private Double avgDMIWeightInkg;

  @JsonProperty("AvgNELWeightInkg")
  private Double avgNELWeightInkg;

  @JsonProperty("AvgMilkFatPercent")
  private Double avgMilkFatPercent;

  @JsonProperty("AvgMilkProteinPercent")
  private Double avgMilkProteinPercent;

  @JsonProperty("TemperatureInCelsius")
  private Double temperatureInCelsius;

  @JsonProperty("HumidityPercent")
  private Double humidityPercent;

  @JsonProperty("HoursExposedToSun")
  private Integer hoursExposedToSun;

  @JsonProperty("TemperatureHumidityInCelsius")
  private Double temperatureHumidityInCelsius;

  @JsonProperty("IntakeAdjustmentPercent")
  private Double intakeAdjustmentPercent;

  @JsonProperty("DMIReductionPercent")
  private Double dmiReductionPercent;

  @JsonProperty("EstimatedDryMatterIntakeWeightInkg")
  private Double estimatedDryMatterIntakeWeightInkg;

  @JsonProperty("ReductionInDMIWeightInkg")
  private Double reductionInDMIWeightInkg;

  @JsonProperty("LossOfEnergyConsumedInMcal")
  private Double lossOfEnergyConsumedInMcal;

  @JsonProperty("EnergyEquivalentMilkLossWeightInkg")
  private Double energyEquivalentMilkLossWeightInkg;

  @JsonProperty("AvgLactatingAnimals")
  private Double avgLactatingAnimals;

  @JsonProperty("AvgCurrentMilkPrice")
  private Double avgCurrentMilkPrice;
}
