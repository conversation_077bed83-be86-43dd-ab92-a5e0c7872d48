/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.blobstorage.S3Migration;
import com.app.cargill.cosmos.migration.CosmosDataMigration;
import com.app.cargill.cosmos.migration.CosmosDataMigration.MigrationType;
import com.app.cargill.cosmos.migration.MigrationService;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.LongRunningTask.TaskName;
import com.app.cargill.model.TaskStatus;
import com.app.cargill.model.tasks.CosmosMigrationMeta;
import com.app.cargill.model.tasks.EmptyMetaData;
import com.azure.cosmos.implementation.BadRequestException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/migration")
@Tag(name = "Database Migration Controller", description = "Database Migration Controller")
@RequiredArgsConstructor
@Slf4j
public class MigrationController {

  private final List<CosmosDataMigration> migrations;
  private final MigrationService migrationService;
  private final S3Migration s3Migration;

  @GetMapping(value = "/{taskId}")
  @Operation(
      summary = "Get migration status",
      description = "This method will return the status of a migration task")
  public LongRunningTask<EmptyMetaData> getMigrationTask(@PathVariable UUID taskId) {
    return new LongRunningTask<>(taskId.toString(), TaskName.UNDEFINED, TaskStatus.UNKNOWN);
  }

  @PostMapping("/content-details")
  @Operation(
      summary = "Initiate content-details migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL")
  public LongRunningTask<CosmosMigrationMeta> startContentDetailsMigration() {
    log.debug("starting content details migration");
    CosmosDataMigration migration =
        migrations.stream()
            .filter(m -> m.migrationType().equals(MigrationType.CONTENT_DETAILS))
            .findAny()
            .orElseThrow(IllegalArgumentException::new);
    migration.moveAll();
    log.info("ContentDetails migration started");

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/notes")
  @Operation(
      summary = "Initiate notes migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL")
  public LongRunningTask<CosmosMigrationMeta> startNotesMigration() {
    CosmosDataMigration migration =
        migrations.stream()
            .filter(m -> m.migrationType().equals(MigrationType.NOTES))
            .findAny()
            .orElseThrow(IllegalArgumentException::new);
    migration.moveAll();
    log.info("Notes migration started");

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/milk-processors")
  @Operation(
      summary = "Initiate Milk Processors migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL")
  public LongRunningTask<CosmosMigrationMeta> startMilkProcessorsMigration() {
    CosmosDataMigration migration =
        migrations.stream()
            .filter(m -> m.migrationType().equals(MigrationType.MILK_PROCESSORS))
            .findAny()
            .orElseThrow(IllegalArgumentException::new);
    migration.moveAll();
    log.info("MilkProcessors migration started");

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/activities")
  @Operation(
      summary = "Initiate Activities migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL")
  public LongRunningTask<CosmosMigrationMeta> startActivitiesMigration() {
    CosmosDataMigration migration =
        migrations.stream()
            .filter(m -> m.migrationType().equals(MigrationType.ACTIVITIES))
            .findAny()
            .orElseThrow(IllegalArgumentException::new);
    migration.moveAll();
    log.info("ACTIVITIES migration started");

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/postMigration")
  @Operation(
      summary = "Initiate post migration tasks",
      description = "This will start post migration data tasks")
  public LongRunningTask<CosmosMigrationMeta> startPostMigration(
      @RequestParam String migrationType) {

    migrations.forEach(cosmosDataMigration -> cosmosDataMigration.postMigration(migrationType));

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.POST_COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/migrationFix")
  @Operation(
      summary = "This api will fix post migration Data issues",
      description = "This api will fix post migration Data issues")
  public LongRunningTask<CosmosMigrationMeta> startMigrationFix(@RequestParam String fixVersion) {
    migrations.forEach(cosmosDataMigration -> cosmosDataMigration.migrationFix(fixVersion));

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.MISSING_ID_FIX_IN_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/blob2s3")
  @Operation(
      summary = "This api will migrate contents from Azure Blob Storage to S3 bucket",
      description = "This api will migrate contents from Azure Blob Storage to S3 bucket")
  public LongRunningTask<CosmosMigrationMeta> contentMigration(
      @RequestParam(defaultValue = "1000") Integer size,
      @RequestParam(defaultValue = "false", name = "migrateAllAccounts")
          boolean migrateAllAccounts) {

    try {
      if (size <= 0) {
        throw new BadRequestException("size must be positive");
      }
      s3Migration.migrate(size, migrateAllAccounts);
    } catch (CustomDEExceptions e) {
      log.error(e.getMessage());
      return new LongRunningTask<>(
          UUID.randomUUID().toString(), TaskName.CONTENT_MIGRATION, TaskStatus.FAILED);
    }

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.CONTENT_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/all")
  @Operation(
      summary = "Initiate migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL for all"
              + " tables")
  public LongRunningTask<CosmosMigrationMeta> moveAll() {
    migrations.forEach(CosmosDataMigration::moveAll);

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/country-tools")
  @Operation(
      summary = "Initiate country-tools migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL")
  public LongRunningTask<CosmosMigrationMeta> countryToolsMigration() {
    CosmosDataMigration migration =
        migrations.stream()
            .filter(m -> m.migrationType().equals(MigrationType.COUNTRY_TOOLS))
            .findAny()
            .orElseThrow(IllegalArgumentException::new);
    migration
        .moveAll()
        .whenComplete(
            (res, t) -> {
              if (t == null) {
                migration.postMigration(MigrationType.COUNTRY_TOOLS.name());
              }
            });
    log.info("country-tools migration started");

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/countries")
  @Operation(
      summary = "Initiate countries migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL")
  public LongRunningTask<CosmosMigrationMeta> countriesMigration() {
    CosmosDataMigration migration =
        migrations.stream()
            .filter(m -> m.migrationType().equals(MigrationType.COUNTRY))
            .findAny()
            .orElseThrow(IllegalArgumentException::new);
    migration
        .moveAll()
        .whenComplete(
            (res, t) -> {
              if (t == null) {
                migration.postMigration(MigrationType.COUNTRY.name());
              }
            });
    log.info("Countries migration started");

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/state")
  @Operation(
      summary = "Initiate state migration task",
      description =
          "This will start a migration of the database from Azure Cosmos to AWS PostgresSQL")
  public LongRunningTask<CosmosMigrationMeta> statesMigration() {
    CosmosDataMigration migration =
        migrations.stream()
            .filter(m -> m.migrationType().equals(MigrationType.STATE))
            .findAny()
            .orElseThrow(IllegalArgumentException::new);
    migration
        .moveAll()
        .whenComplete(
            (res, t) -> {
              if (t == null) {
                migration.postMigration(MigrationType.STATE.name());
              }
            });
    log.info("States migration started");

    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }

  @PostMapping("/visit/pen-id")
  public LongRunningTask<CosmosMigrationMeta> moveTmrPenId() {
    migrationService.fixAllPensTmrPenId();
    return new LongRunningTask<>(
        UUID.randomUUID().toString(), TaskName.COSMOS_DATA_MIGRATION, TaskStatus.RUNNING);
  }
}
