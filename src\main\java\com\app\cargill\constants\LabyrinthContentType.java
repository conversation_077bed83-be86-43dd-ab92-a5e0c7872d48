/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import java.util.Objects;

@SuppressWarnings("java:S115") // Constants Naming
public enum LabyrinthContentType {
  Image(0),
  Video(1),
  Pdf(2),
  SiteVisitReport(3);

  private final Integer value;

  LabyrinthContentType(Integer value) {
    this.value = value;
  }

  public Integer getValue() {
    return value;
  }

  public static LabyrinthContentType fromId(Integer id) {
    for (LabyrinthContentType type : values()) {
      if (Objects.equals(type.getValue(), id)) {
        return type;
      }
    }
    return null;
  }
}
