/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Pickup implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("PickUpIndex")
  public Integer pickUpIndex;

  @JsonProperty("MilkSold")
  public Long milkSold;

  @JsonProperty("AnimalsInTank")
  public Long animalsInTank;

  @JsonProperty("DaysInTank")
  public Integer daysInTank;

  @JsonProperty("MilkFatPer")
  public Long milkFatPer;

  @JsonProperty("MilkProteinPer")
  public Long milkProteinPer;

  @JsonProperty("MUN")
  public Long mUN;

  @JsonProperty("SomaticCellCount")
  public Integer somaticCellCount;

  @JsonProperty("BacteriaCellCount")
  public Long bacteriaCellCount;
}
