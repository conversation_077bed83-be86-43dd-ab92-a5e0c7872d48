/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.Currencies;
import com.app.cargill.converter.CurrenciesStringToEnumDeserializer;
import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.Getter;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkProcessorDataCosmos implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("id")
  private String id;

  @JsonProperty("BasePriceMilkFat")
  private Double basePriceMilkFat;

  @JsonProperty("BasePriceMilkProtein")
  private Double basePriceMilkProtein;

  @JsonProperty("BasePriceOtherSolids")
  private Double basePriceOtherSolids;

  @JsonProperty("BacteriaCellCountPricingMatrix")
  private PricingMatrixCosmos bacteriaCellCountPricingMatrix;

  @JsonProperty("SomaticCellCountPricingMatrix")
  private PricingMatrixCosmos somaticCellCountPricingMatrix;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Currency")
  @JsonDeserialize(using = CurrenciesStringToEnumDeserializer.class)
  private Currencies currency;

  @JsonProperty("Type")
  private int type;

  @JsonProperty("PricingMatrixTypes")
  private int pricingMatrixTypes;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("CreateUser")
  private String createUser;
}
