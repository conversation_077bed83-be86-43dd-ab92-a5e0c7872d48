/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.*;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.User;
import com.app.cargill.service.IUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.UnsupportedEncodingException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/user")
@Tag(name = "User Information Controller", description = "User Information Controller")
@RequiredArgsConstructor
public class UserController extends BaseController {

  private final IUserService userServiceImpl;

  @GetMapping
  @Operation(summary = "Get all users", description = "This method returns all users")
  public ResponseEntity<ResponseEntityDto<List<UserDto>>> getAllUsers() {

    return handleSuccessResponse(userServiceImpl.getAllUsers());
  }

  @GetMapping("/{id}")
  @Operation(
      summary = "Get User Information by Id",
      description = "This method returns  user information by Id")
  public ResponseEntity<ResponseEntityDto<UserDto>> getUserById(@PathVariable("id") Long userId)
      throws NotFoundDEException {

    if (userId != null) {
      return handleSuccessResponse(userServiceImpl.getUserById(userId));
    }
    throw new NotFoundDEException("User Id is Empty");
  }

  @GetMapping("/email/{email}")
  @Operation(summary = "Get user info by email", description = "Get user info by email")
  public ResponseEntity<ResponseEntityDto<UserDto>> getUserInfoByEmail(
      @PathVariable("email") String email) {

    return handleSuccessResponse(userServiceImpl.getUserByPrincipal(email));
  }

  /** migrated to Spring Security 5 */
  @GetMapping("/by-jwt")
  @Operation(summary = "Get user info by jwt token", description = "Get user info by jwt token")
  public ResponseEntity<ResponseEntityDto<UserDto>> getUserInfoByJwtToken() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    return handleSuccessResponse(userServiceImpl.getUserByPrincipal(authentication.getName()));
  }

  @GetMapping("/by/{id-token}")
  @Operation(summary = "Get user info by Id token", description = "Get user info by Id token")
  public ResponseEntity<ResponseEntityDto<UserDto>> getUserInfoByIdToken(
      @PathVariable("id-token") String idToken) throws UnsupportedEncodingException {

    return handleSuccessResponse(userServiceImpl.getUserByIdToken(idToken));
  }

  @PostMapping("/fetchAndUpdateUserInfo")
  @Operation(
      summary = "Fetch and update user info by Id token",
      description = "Fetch and update user info by Id token")
  public ResponseEntity<ResponseEntityDto<UserDto>> fetchAndUpdateUserInfo(
      @RequestBody IdTokenDto idTokenDto) throws UnsupportedEncodingException {

    return handleSuccessResponse(userServiceImpl.fetchAndUpdateUserInfo(idTokenDto));
  }

  @PostMapping
  @Operation(summary = "Create User", description = "This api creates a User")
  public ResponseEntity<ResponseEntityDto<UserSaveDto>> save(@RequestBody UserSaveDto userDto) {

    return handleSuccessResponse(userServiceImpl.save(userDto));
  }

  @PutMapping
  @Operation(summary = "Update User", description = "This api updates a User")
  public ResponseEntity<ResponseEntityDto<UserSaveDto>> update(@RequestBody UserSaveDto userDto) {

    return handleSuccessResponse(userServiceImpl.update(userDto));
  }

  @PostMapping("/updateName/{code}")
  @Operation(
      summary = "Update User Name ",
      description = "This api updates a User's FullName where fullname is :code")
  public ResponseEntity<ResponseEntityDto<List<User>>> updateName(@PathVariable String code) {

    return handleSuccessResponse(userServiceImpl.updateName(code));
  }

  @PostMapping("/bulkInsertUsers")
  @Operation(summary = "Create Bulk Users", description = "This api creates Bulk Users")
  public ResponseEntity<ResponseEntityDto<BulkUserInsertDto>> bulkInsertUsers(
      @RequestBody BulkUserInsertDto userDto) {

    return handleSuccessResponse(userServiceImpl.bulkInsertUsers(userDto));
  }
}
