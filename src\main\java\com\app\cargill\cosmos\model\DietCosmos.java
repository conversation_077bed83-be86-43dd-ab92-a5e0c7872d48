/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import com.app.cargill.constants.DietSource;
import com.app.cargill.document.AnalyzeDietOptimization;
import com.app.cargill.document.FormulateDietOptimization;
import com.azure.spring.data.cosmos.core.mapping.Container;
import com.azure.spring.data.cosmos.core.mapping.PartitionKey;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

@Container(containerName = "Diets")
@Getter
@Setter
public class DietCosmos {

  @Id
  @PartitionKey
  @JsonProperty("id")
  private String id;

  @JsonProperty("LabyrinthAccountId")
  private String labyrinthAccountId;

  @JsonProperty("SiteId")
  private String siteId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("BreedName")
  private String breedName;

  @JsonProperty("BreedId")
  private String breedId;

  @JsonProperty("StartDate")
  private Instant startDate;

  @JsonProperty("EndDate")
  private Instant endDate;

  @JsonProperty("AnimalType")
  private AnimalCosmos animalType;

  @JsonProperty("BarnId")
  private String barnId;

  @JsonProperty("EnvironmentName")
  private String environmentName;

  @JsonProperty("ReportMilkWeight")
  private Double reportMilkWeight;

  @JsonProperty("NumberOfAnimals")
  private Integer numberOfAnimals;

  @JsonProperty("Source")
  private DietSource source;

  @JsonProperty("Selected")
  private Boolean selected = false;

  @JsonProperty("SelectedPenGuids")
  private List<String> selectedPenGuids;

  @JsonProperty("IsSystemGenerated")
  // Added the below field for checking it for Animalclass and Subclasss and diet
  private Boolean isSystemGenerated = false;

  @JsonProperty("AnalyzeOptimization")
  private AnalyzeDietOptimization analyzeOptimization;

  @JsonProperty("IsDeleted")
  private Boolean isDeleted = false;

  @JsonProperty("IsActive")
  private Boolean isActive;

  @JsonProperty("FormulateOptimization")
  private FormulateDietOptimization formulateOptimization;

  @JsonProperty("OptimizationId")
  private Integer optimizationId;

  @JsonProperty("OptimizationType")
  private String optimizationType;

  @JsonProperty("OptimizationStatus")
  private String optimizationStatus;

  @JsonProperty("CreateUser")
  private String createUser;
}
