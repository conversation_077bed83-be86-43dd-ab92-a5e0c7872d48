/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.AnimalClassDto;
import com.app.cargill.dto.DietDto;
import com.app.cargill.dto.DuplicatePenIdDTO;
import com.app.cargill.dto.PenDto;
import com.app.cargill.dto.PenGroupingDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IAnimalClassService;
import com.app.cargill.service.IDietService;
import com.app.cargill.service.IPensService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pens")
@Tag(name = "Pens Information Controller", description = "Pens Information Controller")
@RequiredArgsConstructor
public class PensController extends BaseController {
  private final ResourceBundleMessageSource source;
  private final IPensService pensServiceImpl;
  private final IAnimalClassService animalClassServiceImpl;
  private final IDietService dietServiceImpl;

  @Value("${app.configurations.default-utc-timestamp}")
  private Instant defaultTime;

  @GetMapping("/paginated")
  @Operation(
      summary = "Get all pens paginated by current logged in user",
      description = "This api will return all pens paginated by current logged in user")
  public ResponseEntity<ResponseEntityDto<PageImpl<PenDto>>> getAllPens(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        pensServiceImpl.getAllPensByCurrentLoggedInUser(page, size, sortBy, lastSyncTime, sorting));
  }

  @GetMapping("/bySiteId/paginated")
  @Operation(
      summary = "Get all pens paginated by site Id",
      description = "This api will return all pens paginated by site Id")
  public ResponseEntity<ResponseEntityDto<PageImpl<PenDto>>> getAllPensBySiteId(
      @RequestParam(defaultValue = "siteId", name = "siteId") String siteId,
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {

    return handleSuccessResponse(
        pensServiceImpl.getAllPensBySiteId(siteId, page, size, sortBy, lastSyncTime, sorting));
  }

  @PostMapping
  @Operation(summary = "save pens", description = "This api will save the list of Pens")
  public ResponseEntity<ResponseEntityDto<PenGroupingDto>> save(
      @Valid @RequestBody PenGroupingDto pens)
      throws NotFoundDEException, AlreadyExistsDEException {

    PenGroupingDto save = pensServiceImpl.save(pens);

    long failedCount =
        save.getPens().parallelStream()
            .filter(x -> x.getStatus() != null && x.getStatus() == ResponseStatus.FAILED)
            .count();
    long successCount =
        save.getPens().parallelStream()
            .filter(x -> x.getStatus() != null && x.getStatus() == ResponseStatus.SUCCESS)
            .count();
    if (failedCount > 0 && successCount > 0) {
      return handleResponse(
          "Create Pens Partial Successful",
          save,
          ResponseStatus.PARTIAL,
          HttpStatus.PARTIAL_CONTENT);
    } else if (failedCount == 0 && successCount > 0) {
      return handleSuccessResponse(save);
    } else {
      return handleResponse(
          "Create Pens Failed", save, ResponseStatus.FAILED, HttpStatus.BAD_REQUEST);
    }
  }

  @PutMapping
  @Operation(summary = "update pen", description = "This api will update the list of Pens")
  public ResponseEntity<ResponseEntityDto<PenGroupingDto>> update(
      @Valid @RequestBody PenGroupingDto pens) throws NotFoundDEException {
    PenGroupingDto update = pensServiceImpl.update(pens);
    long failedCount =
        update.getPens().parallelStream()
            .filter(x -> x.getStatus() != null && x.getStatus() == ResponseStatus.FAILED)
            .count();
    long successCount =
        update.getPens().parallelStream()
            .filter(x -> x.getStatus() != null && x.getStatus() == ResponseStatus.SUCCESS)
            .count();
    if (failedCount > 0 && successCount > 0) {
      return handleResponse(
          "Update Partial Successful", update, ResponseStatus.PARTIAL, HttpStatus.PARTIAL_CONTENT);
    } else if (failedCount == 0 && successCount > 0) {
      return handleSuccessResponse(update);
    } else {
      return handleResponse("Update Failed", update, ResponseStatus.FAILED, HttpStatus.BAD_REQUEST);
    }
  }

  @GetMapping("/getAnimalClasses")
  @Operation(
      summary = "get all Multilingual Animal Classes",
      description = "This api will return all all Multilingual Animal Classes")
  public ResponseEntity<ResponseEntityDto<AnimalClassDto>> getAllAnimalClasses() {

    return handleSuccessResponse(animalClassServiceImpl.getAnimalClasses(source));
  }

  @GetMapping("/diets")
  @Operation(
      summary = "get all diets paginated",
      description = "This api will return all diets paginated")
  public ResponseEntity<ResponseEntityDto<PageImpl<DietDto>>> getDietsPaginated(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(
              defaultValue = "${app.configurations.default-utc-timestamp}",
              name = "lastSyncTime")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting,
      @RequestParam(defaultValue = "MAX", name = "dietSource") DietSource dietSource) {

    return handleSuccessResponse(
        dietServiceImpl.getDietsPaginated(page, size, sortBy, lastSyncTime, sorting, dietSource));
  }

  @GetMapping("/getDuplicatePens")
  public ResponseEntity<ResponseEntityDto<List<DuplicatePenIdDTO>>> getAllPensDuplicate(
      @RequestParam(defaultValue = "0", name = "page") int offset,
      @RequestParam(defaultValue = "100", name = "limit") int limit) {
    List<DuplicatePenIdDTO> pens = pensServiceImpl.getAllPensDuplicate(offset, limit);
    return handleSuccessResponse(pens);
  }
}
