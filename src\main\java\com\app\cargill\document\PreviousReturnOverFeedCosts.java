/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class PreviousReturnOverFeedCosts implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("ReturnOverFeedCostPerCowPerDay")
  private Double returnOverFeedCostPerCowPerDay;

  @JsonProperty("ReturnOverFeedCostPerKgOfBF")
  private Double returnOverFeedCostPerKgOfBF;

  @JsonProperty("ReturnOverFeedCostPerLitre")
  private Double returnOverFeedCostPerLitre;
}
