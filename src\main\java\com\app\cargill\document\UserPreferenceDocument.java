/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.MilkPickup;
import com.app.cargill.constants.MilkUreaMeasure;
import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.constants.UserSettingsBrands;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class UserPreferenceDocument extends EditableDocumentBase implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("UserId")
  private String userId;

  @JsonProperty("UnitOfMeasure")
  private UnitOfMeasureKeys unitOfMeasure;

  @JsonProperty("LastSyncOperationDateTime")
  private Instant lastSyncOperationDateTime;

  @JsonProperty("LastEulaVersionAccepted")
  private Instant lastEulaVersionAccepted;

  @JsonProperty("BrandList")
  private List<UserSettingsBrands> brandList;

  @JsonProperty("BCSPointScale")
  private BCSPointScale bcsPointScale;

  @JsonProperty("EulaContent")
  private String eulaContent;

  @JsonProperty("SelectedCurrency")
  private Currencies selectedCurrency;

  @JsonProperty("LastPrivacyVersionAccepted")
  private Instant lastPrivacyVersionAccepted;

  private UserFavourites favourites;

  @Builder.Default private boolean showBCSAnimalAnalysisToast = true;

  private MilkUreaMeasure defaultMilkUreaMeasure;
  private MilkPickup defaultMilkPickup;

  @Builder.Default private Map<String, String> defaultValues = new HashMap<>();
}
