/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.UserActivityLogDto;
import com.app.cargill.dto.UserActivityLogExportDto;
import com.app.cargill.dto.UserActivityLogResponseDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IUserActivityLogService;
import com.app.cargill.service.impl.UserActivityLogExcelReportServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@RestController
@RequestMapping("/user-journey-analytics")
@Tag(name = "User Journey Analytics Controller", description = "Endpoints for tracking user journey and generating analytics reports")
@RequiredArgsConstructor
@Slf4j
public class UserJourneyAnalyticsController extends BaseController {

    private final IUserActivityLogService userActivityLogService;
    private final UserActivityLogExcelReportServiceImpl excelReportService;
    private final ResourceBundleMessageSource resourceBundleMessageSource;

    @PostMapping("/log-activity")
    @Operation(
        summary = "Log User Activity",
        description = "Log a single user activity event for journey tracking and analytics"
    )
    public ResponseEntity<ResponseEntityDto<UserActivityLogResponseDto>> logActivity(
            @Valid @RequestBody UserActivityLogDto activityLogDto,
            HttpServletRequest request) {
        try {
            // Enrich with request information if not provided
            enrichActivityLogFromRequest(activityLogDto, request);
            
            UserActivityLogResponseDto response = userActivityLogService.logActivity(activityLogDto);
            log.info("User activity logged successfully: {} for user: {}", 
                    activityLogDto.getEventName(), activityLogDto.getUsername());
            
            return handleSuccessResponse("Activity logged successfully", response);
        } catch (Exception e) {
            log.error("Error logging user activity: {}", e.getMessage(), e);
            return handleResponse("Failed to log activity: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/log-activities-batch")
    @Operation(
        summary = "Log Multiple User Activities",
        description = "Log multiple user activity events in batch for journey tracking and analytics"
    )
    public ResponseEntity<ResponseEntityDto<List<UserActivityLogResponseDto>>> logActivitiesBatch(
            @Valid @RequestBody List<UserActivityLogDto> activityLogDtos,
            HttpServletRequest request) {
        try {
            // Enrich each activity log with request information if not provided
            activityLogDtos.forEach(dto -> enrichActivityLogFromRequest(dto, request));

            List<UserActivityLogResponseDto> responses = userActivityLogService.logActivitiesBatch(activityLogDtos);
            log.info("Batch of {} user activities logged successfully", responses.size());

            return handleSuccessResponse("Activities logged successfully", responses);
        } catch (Exception e) {
            log.error("Error logging user activities batch: {}", e.getMessage(), e);
            return handleResponse("Failed to log activities: " + e.getMessage(),
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/log-events-list")
    @Operation(
        summary = "Log List of Events with Iteration",
        description = "Accept a list of user events, iterate through each event individually, and save them one by one to the database with detailed processing information"
    )
    public ResponseEntity<ResponseEntityDto<Map<String, Object>>> logEventsList(
            @Valid @RequestBody List<UserActivityLogDto> eventsList,
            HttpServletRequest request) {
        try {
            log.info("Starting to process list of {} events for individual database storage", eventsList.size());

            // Process each event in the list iteratively
            List<UserActivityLogResponseDto> savedEvents = userActivityLogService.processEventsListIteratively(eventsList, request);

            // Create detailed response summary
            Map<String, Object> response = Map.of(
                "totalEventsReceived", eventsList.size(),
                "totalEventsSaved", savedEvents.size(),
                "processingStatus", "SUCCESS",
                "processingMethod", "ITERATIVE",
                "savedEvents", savedEvents,
                "message", String.format("Successfully iterated through %d events and saved each to database", savedEvents.size())
            );

            log.info("Successfully processed {} events iteratively and saved {} to database",
                    eventsList.size(), savedEvents.size());

            return handleSuccessResponse(
                String.format("Processed %d events iteratively, saved %d to database", eventsList.size(), savedEvents.size()),
                response);

        } catch (Exception e) {
            log.error("Error processing events list iteratively: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = Map.of(
                "totalEventsReceived", eventsList.size(),
                "totalEventsSaved", 0,
                "processingStatus", "FAILED",
                "processingMethod", "ITERATIVE",
                "errorMessage", e.getMessage()
            );

            return handleResponse("Failed to process events list: " + e.getMessage(),
                    errorResponse, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/activities")
    @Operation(
        summary = "Get User Activities",
        description = "Retrieve user activities with pagination and filtering options"
    )
    public ResponseEntity<ResponseEntityDto<Page<UserActivityLogResponseDto>>> getActivities(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String eventName,
            @RequestParam(required = false) String accountId,
            @RequestParam(required = false) String siteId,
            @RequestParam(required = false) String featureUsed,
            @RequestParam(required = false) String moduleName,
            @RequestParam(required = false) String pathPattern,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant endDate,
            @PageableDefault(size = 20) Pageable pageable) {
        try {
            Page<UserActivityLogResponseDto> activities;
            
            if (startDate != null && endDate != null && username != null) {
                activities = userActivityLogService.getActivityLogsByUsernameAndDateRange(username, startDate, endDate, pageable);
            } else if (startDate != null && endDate != null && eventName != null) {
                activities = userActivityLogService.getActivityLogsByEventNameAndDateRange(eventName, startDate, endDate, pageable);
            } else if (startDate != null && endDate != null) {
                activities = userActivityLogService.getActivityLogsByDateRange(startDate, endDate, pageable);
            } else if (username != null) {
                activities = userActivityLogService.getActivityLogsByUsername(username, pageable);
            } else if (eventName != null) {
                activities = userActivityLogService.getActivityLogsByEventName(eventName, pageable);
            } else if (accountId != null) {
                activities = userActivityLogService.getActivityLogsByAccountId(accountId, pageable);
            } else if (siteId != null) {
                activities = userActivityLogService.getActivityLogsBySiteId(siteId, pageable);
            } else if (featureUsed != null) {
                activities = userActivityLogService.getActivityLogsByFeatureUsed(featureUsed, pageable);
            } else if (moduleName != null) {
                activities = userActivityLogService.getActivityLogsByModuleName(moduleName, pageable);
            } else if (pathPattern != null) {
                activities = userActivityLogService.getActivityLogsByPathPattern(pathPattern, pageable);
            } else {
                activities = userActivityLogService.getAllActivityLogs(pageable);
            }
            
            return handleSuccessResponse("Activities retrieved successfully", activities);
        } catch (Exception e) {
            log.error("Error retrieving user activities: {}", e.getMessage(), e);
            return handleResponse("Failed to retrieve activities: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/analytics/events")
    @Operation(
        summary = "Get Event Analytics",
        description = "Get analytics data for event names"
    )
    public ResponseEntity<ResponseEntityDto<Map<String, Long>>> getEventAnalytics() {
        try {
            Map<String, Long> analytics = userActivityLogService.getEventNameAnalytics();
            return handleSuccessResponse("Event analytics retrieved successfully", analytics);
        } catch (Exception e) {
            log.error("Error retrieving event analytics: {}", e.getMessage(), e);
            return handleResponse("Failed to retrieve event analytics: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/analytics/paths")
    @Operation(
        summary = "Get Path Analytics",
        description = "Get analytics data for paths"
    )
    public ResponseEntity<ResponseEntityDto<Map<String, Long>>> getPathAnalytics() {
        try {
            Map<String, Long> analytics = userActivityLogService.getPathAnalytics();
            return handleSuccessResponse("Path analytics retrieved successfully", analytics);
        } catch (Exception e) {
            log.error("Error retrieving path analytics: {}", e.getMessage(), e);
            return handleResponse("Failed to retrieve path analytics: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/analytics/users")
    @Operation(
        summary = "Get User Analytics",
        description = "Get analytics data for users"
    )
    public ResponseEntity<ResponseEntityDto<Map<String, Long>>> getUserAnalytics() {
        try {
            Map<String, Long> analytics = userActivityLogService.getUserAnalytics();
            return handleSuccessResponse("User analytics retrieved successfully", analytics);
        } catch (Exception e) {
            log.error("Error retrieving user analytics: {}", e.getMessage(), e);
            return handleResponse("Failed to retrieve user analytics: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/analytics/daily")
    @Operation(
        summary = "Get Daily Activity Analytics",
        description = "Get daily activity analytics for a date range"
    )
    public ResponseEntity<ResponseEntityDto<Map<String, Long>>> getDailyAnalytics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant endDate) {
        try {
            Map<String, Long> analytics = userActivityLogService.getDailyActivityAnalytics(startDate, endDate);
            return handleSuccessResponse("Daily analytics retrieved successfully", analytics);
        } catch (Exception e) {
            log.error("Error retrieving daily analytics: {}", e.getMessage(), e);
            return handleResponse("Failed to retrieve daily analytics: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/excel")
    @Operation(
        summary = "Export User Journey Analytics to Excel",
        description = "Generate and download Excel report of user journey analytics data. Only includes data updated after the specified lastUpdatedTime."
    )
    public ResponseEntity<ByteArrayResource> exportToExcel(
            @Parameter(description = "Only include data updated after this timestamp", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant lastUpdatedTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Instant endDate,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String eventName,
            @RequestParam(required = false) String accountId,
            @RequestHeader(name = "Accept-Language", required = false, defaultValue = "EN") String localeString) {
        try {
            Locale locale = Locale.forLanguageTag(localeString);
            
            // Set end date to now if not provided
            Instant actualEndDate = endDate != null ? endDate : Instant.now();
            
            log.info("Exporting user journey analytics from {} to {} for user: {}, event: {}, account: {}", 
                    lastUpdatedTime, actualEndDate, username, eventName, accountId);
            
            UserActivityLogExportDto exportData = userActivityLogService.prepareExportData(
                    lastUpdatedTime, actualEndDate, username, eventName, accountId);
            
            ByteArrayResource resource = excelReportService.prepareExportToExcel(exportData, resourceBundleMessageSource, locale);
            String fileName = excelReportService.getFileName(exportData);
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            
            log.info("User journey analytics Excel export completed successfully. Records: {}", exportData.getTotalRecords());
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(resource.contentLength())
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (IOException e) {
            log.error("Error generating Excel export: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("Error exporting user journey analytics: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get Activity by ID",
        description = "Retrieve a specific user activity by ID"
    )
    public ResponseEntity<ResponseEntityDto<UserActivityLogResponseDto>> getActivityById(@PathVariable Long id) {
        try {
            UserActivityLogResponseDto activity = userActivityLogService.getActivityLogById(id);
            return handleSuccessResponse("Activity retrieved successfully", activity);
        } catch (NotFoundDEException e) {
            return handleResponse("Activity not found", null, ResponseStatus.FAILED, HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("Error retrieving activity by ID: {}", e.getMessage(), e);
            return handleResponse("Failed to retrieve activity: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete Activity",
        description = "Soft delete a user activity by ID"
    )
    public ResponseEntity<ResponseEntityDto<String>> deleteActivity(@PathVariable Long id) {
        try {
            userActivityLogService.deleteActivityLog(id);
            return handleSuccessResponse("Activity deleted successfully", "Activity with ID " + id + " has been deleted");
        } catch (NotFoundDEException e) {
            return handleResponse("Activity not found", null, ResponseStatus.FAILED, HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("Error deleting activity: {}", e.getMessage(), e);
            return handleResponse("Failed to delete activity: " + e.getMessage(), 
                    null, ResponseStatus.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Enrich activity log with request information if not already provided
     */
    private void enrichActivityLogFromRequest(UserActivityLogDto activityLogDto, HttpServletRequest request) {
        if (activityLogDto.getIpAddress() == null || activityLogDto.getIpAddress().trim().isEmpty()) {
            activityLogDto.setIpAddress(getClientIpAddress(request));
        }
        
        if (activityLogDto.getUserAgent() == null || activityLogDto.getUserAgent().trim().isEmpty()) {
            activityLogDto.setUserAgent(request.getHeader("User-Agent"));
        }
        
        if (activityLogDto.getReferrerUrl() == null || activityLogDto.getReferrerUrl().trim().isEmpty()) {
            activityLogDto.setReferrerUrl(request.getHeader("Referer"));
        }
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
