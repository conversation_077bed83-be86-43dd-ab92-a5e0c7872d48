/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.model;

import com.app.cargill.sf.cc.model.AuthToken;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Builder
public class CrescendoMetadata implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private AuthToken authToken;
  private String apiPath;
}
