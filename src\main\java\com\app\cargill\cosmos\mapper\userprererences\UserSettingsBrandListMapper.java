/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper.userprererences;

import com.app.cargill.constants.UserSettingsBrands;
import java.util.ArrayList;
import java.util.List;

public class UserSettingsBrandListMapper {

  private UserSettingsBrandListMapper() {}

  public static List<UserSettingsBrands> map(List<Integer> brandList) {
    if (brandList == null || brandList.isEmpty()) {
      return new ArrayList<>();
    }
    List<UserSettingsBrands> userSettingsBrandsList = new ArrayList<>();
    brandList.stream()
        .forEach(brand -> userSettingsBrandsList.add(UserSettingsBrands.fromId(brand)));

    return userSettingsBrandsList;
  }
}
