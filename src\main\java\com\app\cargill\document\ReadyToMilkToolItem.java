/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReadyToMilkToolItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Outputs")
  private ReadyToMilkOutputToolItem outputs;

  // #region Herd Level Info
  @JsonProperty("MilkPrice")
  private Double milkPrice;

  @JsonProperty("TotalFreshCowsPerYear")
  private Integer totalFreshCowsPerYear;

  @JsonProperty("ReplacementCowCost")
  private Double replacementCowCost;

  @JsonProperty("CostOfExtraDaysOpen")
  private Double costOfExtraDaysOpen;
  // #endregion

  // #region Close up cows
  @JsonProperty("SpecificCloseupDiet")
  private Integer specificCloseupDiet;

  @JsonProperty("PercievedHeatStressCloseUp")
  private Integer percievedHeatStressCloseUp;

  @JsonProperty("ComfortCloseUp")
  private Integer comfortCloseUp;
  // #endregion

  // #region Fresh Cows
  @JsonProperty("SpecificDiet")
  private Integer specificDiet;

  @JsonProperty("PerceivedHeatStress")
  private Integer perceivedHeatStress;

  @JsonProperty("Comfort")
  private Integer comfort;

  @JsonProperty("BCSVariationFromDryOff")
  private Integer bcsVariationFromDryOff;

  @JsonProperty("CudChewing")
  private CudChewingCowCount cudChewing;

  @JsonProperty("LocomotionScore")
  private List<LocomotionToolItemCategoryItem> locomotionScore;

  @JsonProperty("RumenFill")
  private Integer rumenFill;

  @JsonProperty("MilkYield")
  private Double milkYield;
  // #endregion

  // #region Health
  @JsonProperty("TotalFreshCowsEvoluation")
  private Integer totalFreshCowsEvoluation;

  @JsonProperty("RetainedPlacentaCases")
  private Integer retainedPlacentaCases;

  @JsonProperty("MetritisCases")
  private Integer metritisCases;

  @JsonProperty("DisplacedAbomasumCases")
  private Integer displacedAbomasumCases;

  @JsonProperty("KetosisCases")
  private Integer ketosisCases;

  @JsonProperty("MilkFeverCases")
  private Integer milkFeverCases;

  @JsonProperty("DystociaCases")
  private Integer dystociaCases;

  @JsonProperty("PreMatureCalvings")
  private Integer preMatureCalvings;

  @JsonProperty("MastitisCases")
  private Double mastitisCases;

  @JsonProperty("SCCFirstTest")
  private Integer sccFirstTest;

  @JsonProperty("DeadCowsOrCulled")
  private Integer deadCowsOrCulled;
  // #endregion
  @JsonProperty("AverageLocomotionScoreCopy")
  private Double averageLocomotionScoreCopy;
}
