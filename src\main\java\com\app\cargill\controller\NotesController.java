/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.dto.NotesDto;
import com.app.cargill.dto.NotesSearchDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.INotesService;
import com.app.cargill.service.IUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/notes")
@Tag(name = "Notes Information Controller", description = "Notes Information Controller")
@RequiredArgsConstructor
public class NotesController extends BaseController {
  private final IUserService userServiceImpl;

  private final INotesService notesServiceImpl;

  @GetMapping("/paginated")
  @Operation(
      summary = "Get all Notes based on current logged in User",
      description = "This method will return all Notes based on current Logged in user")
  public ResponseEntity<ResponseEntityDto<Page<NotesDto>>> getAllNotesPaginated(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", name = "sortBy") String sortBy,
      @RequestParam(
              name = "lastSyncTime",
              defaultValue = "${app.configurations.default-utc-timestamp}")
          Instant lastSyncTime,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting) {
    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    return handleSuccessResponse(
        notesServiceImpl.getAllNotesPaginated(
            page,
            size,
            sortBy,
            sorting,
            lastSyncTime,
            currentLoggedInUser,
            userServiceImpl.getCurrentLoggedInUser()));
  }

  @PostMapping("/paginated/online")
  @Operation(
      summary = "Get all Notes Paginated",
      description = "This api returns Paginated Notes in Online mode.")
  public ResponseEntity<ResponseEntityDto<Page<NotesDto>>> getAllNotesPaginatedInOnlineMode(
      @RequestParam(defaultValue = "0", name = "page") int page,
      @RequestParam(defaultValue = "${app.configurations.default-page-size}", name = "size")
          int size,
      @RequestParam(defaultValue = "id", required = false, name = "sortBy") String sortBy,
      @RequestParam(defaultValue = "${app.configurations.default-page-sorting}", name = "sorting")
          String sorting,
      @RequestBody NotesSearchDto notesSearchDto) {

    return handleSuccessResponse(
        notesServiceImpl.getAllNotesOnlinePaginated(page, size, sortBy, sorting, notesSearchDto));
  }

  @PostMapping
  @Operation(
      summary = "Save Notes",
      description = "This api will save Notes WRT to CustomerId and SiteId")
  public ResponseEntity<ResponseEntityDto<NotesDto>> save(@RequestBody NotesDto notesDto) {
    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUser();
    return handleSuccessResponse(notesServiceImpl.save(notesDto, currentLoggedInUser));
  }

  @PutMapping
  @Operation(summary = "Update Notes", description = "This api will Update Notes WRT to Notes ID")
  public ResponseEntity<ResponseEntityDto<NotesDto>> update(@RequestBody NotesDto notesDto) {
    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUser();
    return handleSuccessResponse(notesServiceImpl.update(notesDto, currentLoggedInUser));
  }

  @PostMapping(
      value = "/upsert",
      produces = {MediaType.APPLICATION_JSON_VALUE})
  @Operation(
      summary = "update or insert Notes",
      description = "This api will Update or Insert Notes WRT to Notes ID")
  public ResponseEntity<ResponseEntityDto<List<NotesDto>>> upsert(
      @RequestBody List<NotesDto> notesDto) {
    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUser();

    return handleSuccessResponse(notesServiceImpl.updateOrInsert(notesDto, currentLoggedInUser));
  }
}
